using System;
using System.IO;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using RealtoCrm.Web.Startup;

AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
var builder = new WebHostBuilder()
    .UseKestrel(opt =>
    {
        opt.AddServerHeader = false;
        opt.Limits.MaxRequestLineSize = 16 * 1024;
    })
    .UseContentRoot(Directory.GetCurrentDirectory())
    .ConfigureLogging((context, logging) =>
    {
        logging.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", LogLevel.Warning);
    })
    .UseStartup<Startup>();
    // Stopped by <PERSON><PERSON> - nobody watches it...
    // TODO: should be extracted in extension method
    // .UseSentry(o =>
    // {
    //     o.Dsn = Environment.GetEnvironmentVariable("Sentry__DSN");
    //     o.TracesSampleRate = 1.0;
    // });

var app = builder.Build();

app.Run();