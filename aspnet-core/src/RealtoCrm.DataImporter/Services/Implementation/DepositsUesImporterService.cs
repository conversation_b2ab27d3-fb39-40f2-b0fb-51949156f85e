namespace RealtoCrm.DataImporter.Services.Implementation;

using Companies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Azure.Storage.Blobs.Models;
using Mappings.DataResolvers.Deposits;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Nomenclatures;
using RealtoCrm.BlobStorage;
using RealtoCrm.Contracts;
using RealtoCrm.DataImporter.Services.Implementation.Helpers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Deposits;
using RealtoCrm.Employees;
using RealtoCrm.Extensions;
using static RealtoCrm.CosherConsts.ContainerNames;
using static RealtoCrm.CosherConsts.ContractTypes;


public class DepositsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DepositsUesImporterModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService,
    IBlobStorageService blobStorageService)
    : BaseUesImporterService<Deposit, DepositsUesImporterModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/deposits";

    private static readonly HttpClient HttpClient = new()
    {
        Timeout = TimeSpan.FromMinutes(4)
    };
    
    protected override IEnumerable<Deposit> ApplySpecifics(IEnumerable<Deposit> entities)
        => entities.Where(x => x.SearchId > 0)
            .Where(x => x.OfferId > 0)
            .Where(x => x.Match.Id > 0)
            .Where(x => x.OfferClient.Id > 0)
            .Where(x => x.SearchClientId > 0)
            .Where(x => x.DepositStatusId > 0)
            .Where(x => x.SearchEmployeeId  > 0)
            .Where(x => x.OfferEmployee.Id != EmployeeValueResolver.NonExistentEmployee.Id);

    protected override async Task DecorateBeforeAsync(List<Deposit> entityList,
        IEnumerable<DepositsUesImporterModel> batchAsiData)
    {
        var uesTenant = this.DbContext.Tenants.First(x => x.Name == UniqueEstatesCompanyName).Id;
        var guaranteeContractType =  this.DbContext.ContractTypes.FirstOrDefault(x => x.Name == GuaranteeContractTypeName)?.Id;

        foreach (var deposit in entityList)
        {
            var correspondingModel = batchAsiData.First(b => b.UesDepositId == deposit.DepositMapping.AdminId);

            if (string.IsNullOrEmpty(correspondingModel.FilePath))
            {
                continue;
            }

            byte[] file;
            string fileExtension;

            var fileName = $"deposit-for-offer-{deposit.OfferId}-and-search-{deposit.SearchId}";

            var blobImageSource = $"/{DepositContainerName}/{fileName}";

            var fileExistsInBlob = await blobStorageService.BlobExistsAsync(DepositContainerName, fileName);

            var contract = new Contract
            {
                Name = fileName,
                TenantId = uesTenant,
                SignDate = deposit.SigningDate,
                ExpirationDate = deposit.SigningEndDate,
                ContractTypeId = guaranteeContractType,
                EmployeeId = deposit.SearchEmployeeId,
                ClientId = deposit.SearchClientId,
                OfferId = deposit.OfferId,
                SearchId = deposit.SearchId,
                Match = deposit.Match,
            };

            if (!fileExistsInBlob)
            {
                try
                {
                    log.Write($"Downloading file for deposit {correspondingModel.FilePath}...");
                    var response =  await DownloadHelper.DownloadFileAsync(HttpClient, log, correspondingModel.FilePath, this.ApiKey);
                    file = response.Content;
                    fileExtension = response.Extension;
                    fileName += $"{fileExtension}";
                    contract.Size = file.Length;
                }
                catch (HttpRequestException e)
                {
                    log.Write($"Error fetching files for deposit {correspondingModel.UesDepositId}: {e.Message}");
                    continue;
                }
                catch (Exception e)
                {
                    log.Write($"Unexpected error for deposit {correspondingModel.UesDepositId}: {e.Message}");
                    continue;
                }

                using var fileStream = file.ToMemoryStream();

                blobImageSource = await blobStorageService.UploadFileAsync(
                    fileStream,
                    fileName,
                    DepositContainerName,
                    new BlobHttpHeaders
                    {
                        ContentType = DownloadHelper.GetContentType(fileExtension)
                    });

                contract.Source = blobImageSource;
            }

            deposit.Contracts.Add(contract);
        }
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var offerMappings = await this.DbContext.OfferMappings.Include(x => x.Offer).Where(x => x.AdminId != null).ToListAsync();
        var searchMappings =
            await this.DbContext.SearchMappings.Include(x => x.Search).Where(x => x.AdminId != null).ToListAsync();
        var matches = await this.DbContext.Matches.Include(x => x.MatchMapping).Where(x => x.MatchMapping.AdminId != null).ToListAsync();
        var offers = await this.DbContext.Offers.Where(x => offerMappings.Select(om => om.OfferId).Contains(x.Id)).ToListAsync();
        var clientsMappings = await this.DbContext.ClientMappings.Include(x => x.Client).Where(x => x.AdminId != null).ToListAsync();
        var employeesMappings = await this.DbContext.EmployeeMappings.Include(x => x.Employee).Where(x => x.AdminId != null).ToListAsync();
        var systemEmployee = await this.DbContext
            .Set<Employee>()
            .Where(e => e.IsSystem && e.Company != null && e.Company.Name == UniqueEstatesCompanyName)
            .SingleAsync();
        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == UniqueEstatesCompanyName)
            .SingleAsync();
        var depositStatuses = await this.DbContext
            .Set<DepositStatus>()
            .ToListAsync();

        return await Task.FromResult<IEnumerable<KeyValuePair<string, object>>>(
            new Dictionary<string, object>
            {
                { OffersByOfferMappingsValueResolver.OffersByOfferMappingsItemsKey, offerMappings },
                { SearchBySearchesMappingValueResolver.SearchesMappingsItemsKey, searchMappings },
                { SearchClientIdBySearchMappingValueResolver.SearchesMappingItemsKey, searchMappings },
                { SearchEmployeeBySearchMappings.SearchEmployeeBySearchMappingsItemsKey, searchMappings },
                { MatchByViewingValueResolver.MatchByViewingValueResolverItemsKey, matches },
                { OfferEmployeeIdByOfferMappings.OffersByOfferMappingsItemsKey, offerMappings },
                { OfferEmployeeIdByOfferMappings.OfferEmployeeIdByOfferMappingsOffersKeys, offers },
                { ClientValueResolver.ClientsMappingsItemsKey, clientsMappings },
                { EmployeeValueResolver.EmployeeMappingsItemsKey, employeesMappings },
                { EmployeeValueResolver.SystemEmployeeItemsKey, systemEmployee },
                { DepositStatusValueResolver.DepositStatusesItemsKey, depositStatuses },
                { TenantValueResolver<DepositsUesImporterModel, Deposit>.CompanyNameItemsKey, company },
            });
    }
}