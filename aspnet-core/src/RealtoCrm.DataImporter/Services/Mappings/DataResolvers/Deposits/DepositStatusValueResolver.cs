namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Deposits;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Deposits;
using static CosherConsts.DepositStatuses;

public class DepositStatusValueResolver : IValueResolver<DepositsUesImporterModel, Deposit, DepositStatus>
{
    public const string DepositStatusesItemsKey = nameof(DepositStatusValueResolver);

    private readonly Dictionary<int?, string> UesDepositTypeIdToCosherDepositStatus = new()
    {
        { 1, ActiveDepositStatusName },
        { 2, RejectedDepositStatusName },
        { 3, ActiveDepositStatusName },
        { 4, RedirectedDepositStatusName },
        { 5, AnnexedDepositStatusName },
        { 6, AdoptedDepositStatusName }
    };

    public DepositStatus Resolve(DepositsUesImporterModel source, Deposit destination, DepositStatus destMember,
        ResolutionContext context)
    {
        var statuses = context.Items[DepositStatusesItemsKey] as List<DepositStatus>;

        var statusName = this.GetStatusName(source.DepositTypeId, source.SigningEndDate);
        return statuses?.FirstOrDefault(s => s.Name == statusName);
    }

    private string GetStatusName(int? statusId, string? signingEndDate)
    {
        if (statusId.HasValue && this.UesDepositTypeIdToCosherDepositStatus.TryGetValue(statusId.Value, out var mappedName))
            return mappedName;

        if (string.IsNullOrWhiteSpace(signingEndDate) ||
            signingEndDate == "0000-00-00" ||
            !DateTime.TryParse(signingEndDate, out var endDate) ||
            endDate <= DateTime.Now.AddMonths(-6))
        {
            return RejectedDepositStatusName;
        }

        return ActiveDepositStatusName;
    }
}