namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Offers;
using static CosherConsts.LifeStyleNames;

public class LifeStylesValueResolver : IValueResolver<OfferUesImportModel, Offer, ICollection<OfferLifestyle>>
{
    public const string LifeStylesItemsKey = nameof(LifeStylesValueResolver);

    private Dictionary<int, string> UesLifeStyleIdsToLifeStyleNames = new()
    {
        { 1, SeaLifeStyleName },
        { 2, MountainLifestyleName },
        { 3, GolfLifeStyleName },
        { 4, EthnoLifeStyleName },
        { 5, RiverLakeLifestyleName },
    };

    public ICollection<OfferLifestyle> Resolve(OfferUesImportModel source, Offer destination,
        ICollection<OfferLifestyle> destMember, ResolutionContext context)
    {
        var returnResult = new List<OfferLifestyle>();

        if (source.LifeStyles == null || source.LifeStyles.Count == 0)
        {
            return returnResult;
        }

        var lifeStyles = context.Items[LifeStylesItemsKey] as List<Lifestyle>;

        foreach (var lifeStyle in source.LifeStyles)
        {
            var lifeStyleExists =
                UesLifeStyleIdsToLifeStyleNames.TryGetValue(lifeStyle.Id, out var lifeStyleName);
        
            if (lifeStyleExists)
            {
                returnResult.Add(new OfferLifestyle
                {
                    Lifestyle = lifeStyles!.First(n => n.Name == lifeStyleName)
                });
            }
        }

        

        return returnResult;
    }
}