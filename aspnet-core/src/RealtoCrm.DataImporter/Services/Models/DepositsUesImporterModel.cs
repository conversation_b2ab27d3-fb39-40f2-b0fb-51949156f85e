using System;
using System.Collections.Generic;
using System.Globalization;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;
using RealtoCrm.Deposits;
using RealtoCrm.Mapping;
using RealtoCrm.Money;

namespace RealtoCrm.DataImporter.Services.Models;

using Services.Mappings.DataResolvers.Deposits;

public class DepositsUesImporterModel : IMapFrom<Deposit>, IMapTo<Deposit>, IMapExplicitly
{
    public int Total { get; set; }

    [JsonProperty("per_page")] public int PerPage { get; set; }

    [JsonProperty("current_page")] public int CurrentPage { get; set; }

    [JsonProperty("last_page")] public int LastPage { get; set; }

    [JsonProperty("next_page_url")] public string NextPageUrl { get; set; }

    [JsonProperty("prev_page_url")] public object PrevPageUrl { get; set; }

    public int From { get; set; }

    public int To { get; set; }

    [JsonProperty("id")] public int UesDepositId { get; set; }

    [JsonProperty("offer_id")] public int? OfferUesId { get; set; }

    [JsonProperty("search_id")] public int? SearchId { get; set; }

    public decimal? Amount { get; set; }

    [JsonProperty("offered_price")] public decimal? OfferedPrice { get; set; }

    [JsonProperty("file_path")] public string? FilePath { get; set; }

    [JsonProperty("sign_date")] public string? SigningDate { get; set; }

    [JsonProperty("contract_agency_agreement_expire_date")]
    public string? SigningEndDate { get; set; }

    [JsonProperty("created_at")] public DateTime? CreationTime { get; set; }

    public int Deleted { get; set; }

    [JsonProperty("deposit_type_id")] 
    public int? DepositTypeId { get; set; }

    [JsonProperty("offer")] public UesOfferModel? UesOffer { get; set; }

    public UesMatchFromDeposits? Match { get; set; }


    public class UesDepositClient
    {
        [JsonProperty("id")] public int? ClientId { get; set; }
    }

    public class UesMatchFromDeposits
    {
        [JsonProperty("id")] public int MatchId { get; set; }
        public int? Status { get; set; }

        [JsonProperty("offer")] public UesOfferModel? UesOffer { get; set; }
    }


    public class UesOfferModel
    {
        public int Id { get; set; }
        [JsonProperty("user_id")] public int EmployeeId { get; set; }

        public decimal? Price { get; set; }

        public UesEstate? Estate { get; set; }
    }

    public class UesEstate
    {
        [JsonProperty("clients")] public List<UesDepositClient> DepositClients { get; } = new();
    }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<DepositsUesImporterModel, Deposit>()
            .ForMember(dest => dest.DepositMapping, opt => opt.MapFrom(src => new DepositMapping
            {
                AdminId = src.UesDepositId
            }))
            .ForMember(dest => dest.SearchId, opt
                => opt.MapFrom<SearchBySearchesMappingValueResolver>())
            .ForMember(dest => dest.SearchClientId, opt =>
                opt.MapFrom<SearchClientIdBySearchMappingValueResolver>())
            .ForMember(dest => dest.SearchEmployeeId, opt =>
                opt.MapFrom<SearchEmployeeBySearchMappings>())
            .ForMember(dest => dest.Match,
                opt =>
                {
                    opt.PreCondition(src => src.Match != null);
                    opt.MapFrom<MatchByViewingValueResolver>();
                })
            .ForMember(dest => dest.OfferId, opt
                => opt.MapFrom<OffersByOfferMappingsValueResolver>())
            .ForMember(dest => dest.OfferEmployee, opt
                => opt.MapFrom<EmployeeValueResolver>())
            .ForMember(dest => dest.OfferClient, opt
                => opt.MapFrom<ClientValueResolver>())
            .ForMember(dest => dest.OfferedPrice, opt
                => opt.MapFrom(src =>
                    src.OfferedPrice.HasValue
                        ? new Money.Money
                        {
                            Amount = src.OfferedPrice.Value,
                            Currency = Currency.EUR
                        }
                        : new Money.Money
                        {
                            Amount = src.UesOffer != null ? src.UesOffer.Price ?? 0 : 0,
                            Currency = Currency.EUR
                        }))
            .ForMember(dest => dest.Amount, opt =>
                opt.MapFrom(new MoneyValueResolver<DepositsUesImporterModel, Deposit>(
                    nameof(Amount),
                    nameof(Currency.EUR),
                    false
                )))
            .ForMember(
                dest => dest.Tenant,
                opt => opt.MapFrom<TenantValueResolver<DepositsUesImporterModel, Deposit>>())
            .ForMember(dest => dest.IsDeleted, opt =>
                opt.MapFrom(src => src.Deleted == 1))
            .ForMember(dest => dest.SigningDate, opt
                => opt.MapFrom(src =>
                    src.SigningDate == "0000-00-00"
                        ? (DateTime?)null
                        : DateTime.Parse(src.SigningDate, CultureInfo.InvariantCulture)))
            .ForMember(dest => dest.DepositStatus, opt => opt.MapFrom<DepositStatusValueResolver>())
            .ForMember(dest => dest.SigningEndDate, opt
                => opt.MapFrom(src =>
                    src.SigningEndDate == "0000-00-00"
                        ? (DateTime?)null
                        : DateTime.Parse(src.SigningEndDate, CultureInfo.InvariantCulture)))
            .ForMember(dest => dest.Search, opt => opt.Ignore())
            .ForMember(dest => dest.Id, opt => opt.Ignore());
}