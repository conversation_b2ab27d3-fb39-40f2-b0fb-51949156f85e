namespace RealtoCrm.Configuration.Tenants.Dto;

public class PotentialContactSettingsModel
{
    public int RegisterSearchBuyDays { get; init; }

    public int RegisterSearchRentDays { get; init; }

    public int ReleaseSearchDaysForInfluenceSphereRecommendationExternalBrokerSource { get; init; }

    public int ReleaseSearchDaysForConsultingActivityFiledPotentialsCorporateClientsSource { get; init; }

    public int ReleaseSearchDaysForAdvertisementSource { get; init; }

    public int ReleaseSearchDaysForCommissionContract { get; init; }

    public int ReleaseSearchDaysForExclusiveContract { get; init; }

    public int ReleaseSearchDaysForExclusiveContractForInfluenceSphereRecommendationExternalBrokerSource { get; init; }

    public int ReleaseSearchDaysForExclusiveContractForConsultingActivityFiledPotentialsCorporateClientsSource { get; init; }

    public int ReleaseSearchDaysForExclusiveContractForAdvertisementSource { get; init; }
}