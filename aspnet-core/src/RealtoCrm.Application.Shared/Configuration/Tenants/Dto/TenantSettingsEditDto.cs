using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Runtime.Validation;
using Abp.Timing;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.Configuration.Host.Dto;

namespace RealtoCrm.Configuration.Tenants.Dto;

public class TenantSettingsEditDto
{
    public GeneralSettingsEditDto General { get; set; } = new();

    [Required]
    public TenantUserManagementSettingsEditDto UserManagement { get; set; } = new();

    public TenantEmailSettingsEditDto Email { get; set; } = new();

    public LdapSettingsEditDto Ldap { get; set; } = new();

    [Required]
    public SecuritySettingsEditDto Security { get; set; } = new();

    public TenantBillingSettingsEditDto Billing { get; set; } = new();

    public TenantOtherSettingsEditDto OtherSettings { get; set; } = new();

    public ExternalLoginProviderSettingsEditDto ExternalLoginProviderSettings { get; set; } = new();

    /// <summary>
    /// This validation is done for single-tenant applications.
    /// Because, these settings can only be set by tenant in a single-tenant application.
    /// </summary>
    public void ValidateHostSettings()
    {
        var validationErrors = new List<ValidationResult>();
        if (Clock.SupportsMultipleTimezone && this.General == null)
        {
            validationErrors.Add(new ValidationResult("General settings can not be null", new[] { "General" }));
        }

        if (this.Email == null)
        {
            validationErrors.Add(new ValidationResult("Email settings can not be null", new[] { "Email" }));
        }

        if (validationErrors.Count > 0)
        {
            throw new AbpValidationException("Method arguments are not valid! See ValidationErrors for details.", validationErrors);
        }
    }
}