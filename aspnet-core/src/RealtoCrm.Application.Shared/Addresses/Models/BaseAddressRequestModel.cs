namespace RealtoCrm.Addresses.Models;

using System.ComponentModel.DataAnnotations;
using Mapping;
using static Addresses.ModelConstants.Address;

public class BaseAddressRequestModel : IMapTo<Address>
{
    public int? CountryId { get; init; }

    public int? PopulatedPlaceId { get; init; }

    public int? ProvinceId { get; init; }

    public int? MunicipalityId { get; init; }

    public int? DistrictId { get; init; }

    public int? StreetId { get; init; }

    [MinLength(MinStreetNumberLength)]
    [MaxLength(MaxStreetNumberLength)]
    public string? StreetNumber { get; init; }

    [MinLength(MinBlockNumberLength)]
    [MaxLength(MaxBlockNumberLength)]
    public string? BlockNumber { get; init; }

    [MinLength(MinEntranceNumberLength)]
    [MaxLength(MaxEntranceNumberLength)]
    public string? EntranceNumber { get; init; }

    [MinLength(MinFloorNumberLength)]
    [MaxLength(MaxFloorNumberLength)]
    public string? FloorNumber { get; init; }

    [MinLength(MinApartmentNumberLength)]
    [MaxLength(MaxApartmentNumberLength)]
    public string? ApartmentNumber { get; init; }
    
    public int? TenantId { get; init; }
}