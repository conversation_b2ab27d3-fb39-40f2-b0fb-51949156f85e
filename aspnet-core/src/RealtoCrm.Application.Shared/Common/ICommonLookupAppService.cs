using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using RealtoCrm.Common.Dto;
using RealtoCrm.Editions.Dto;

namespace RealtoCrm.Common;

public interface ICommonLookupAppService : IApplicationService
{
    Task<ListResultDto<SubscribableEditionComboboxItemDto>> GetEditionsForCombobox(bool onlyFreeItems = false);

    Task<PagedResultDto<FindUsersOutputDto>> FindUsers(FindUsersInput input);

    GetDefaultEditionNameOutput GetDefaultEditionName();
}