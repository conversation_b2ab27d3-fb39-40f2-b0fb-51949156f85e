using System;
using System.ComponentModel.DataAnnotations;
using System.Web;
using Abp.Auditing;
using Abp.Runtime.Security;
using Abp.Runtime.Validation;

namespace RealtoCrm.Authorization.Accounts.Dto;

public class ResetPasswordInput: IShouldNormalize
{
    public long UserId { get; set; }

    public DateTime ExpireDate { get; set; }

    [DisableAuditing]
    public string Password { get; set; }

    /// <summary>
    /// Encrypted values for {TenantId}, {UserId}, {ResetCode} and {ExpireDate}
    /// </summary>
    public string C { get; set; }

    public void Normalize()
    {
        this.ResolveParameters();
    }

    protected virtual void ResolveParameters()
    {
        if (!string.IsNullOrEmpty(this.C))
        {
            try
            {
                var parameters = SimpleStringCipher.Instance.Decrypt(this.C);
                var query = HttpUtility.ParseQueryString(parameters);

                if (query["userId"] != null)
                {
                    this.UserId = Convert.ToInt32(query["userId"]);
                }

                if (query["expireDate"] == null)
                {
                    throw new AbpValidationException();
                }
                    
                this.ExpireDate = Convert.ToDateTime(query["expireDate"]);

            }
            catch (Exception)
            {
                throw new AbpValidationException("Invalid reset password link!");
            }
        }
    }
}