using System;
using System.ComponentModel.DataAnnotations;
using System.Web;
using Abp.Runtime.Security;
using Abp.Runtime.Validation;

namespace RealtoCrm.Authorization.Accounts.Dto;

public class ActivateEmailInput: IShouldNormalize
{
    public long UserId { get; set; }

    public string ConfirmationCode { get; set; }

    /// <summary>
    /// Encrypted values for {TenantId}, {UserId} and {ConfirmationCode}
    /// </summary>
    public string C { get; set; }

    public void Normalize()
    {
        this.ResolveParameters();
    }

    protected virtual void ResolveParameters()
    {
        if (!string.IsNullOrEmpty(this.C))
        {
            var parameters = SimpleStringCipher.Instance.Decrypt(this.C);
            var query = HttpUtility.ParseQueryString(parameters);

            if (query["userId"] != null)
            {
                this.UserId = Convert.ToInt32(query["userId"]);
            }

            if (query["confirmationCode"] != null)
            {
                this.ConfirmationCode = query["confirmationCode"];
            }
        }
    }
}