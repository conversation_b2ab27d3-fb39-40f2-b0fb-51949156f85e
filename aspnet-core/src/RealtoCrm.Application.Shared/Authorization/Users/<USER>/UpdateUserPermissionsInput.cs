namespace RealtoCrm.Authorization.Users.Dto;

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

public class UpdateUserPermissionsInput
{
    public int? TenantId { get; init; }

    [Range(1, int.MaxValue)]
    public long Id { get; init; }

    [Required]
    public IEnumerable<int> RoleIds { get; init; } = default!;

    [Required]
    public IEnumerable<string> GrantedPermissionNames { get; init; } = default!;
}