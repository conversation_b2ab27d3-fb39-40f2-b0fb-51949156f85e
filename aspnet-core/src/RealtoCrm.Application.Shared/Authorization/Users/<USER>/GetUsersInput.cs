using System.Collections.Generic;
using Abp.Runtime.Validation;
using RealtoCrm.Dto;

namespace RealtoCrm.Authorization.Users.Dto;

public class GetUsersInput : PagedAndSortedInputDto, IShouldNormalize, IGetUsersInput
{
    public string Filter { get; set; }

    public List<string> Permissions { get; set; }

    public int? Role { get; set; }

    public bool OnlyLockedUsers { get; set; }

    public void Normalize()
    {
        if (string.IsNullOrEmpty(this.Sorting))
        {
            this.Sorting = "Name,Surname";
        }

        this.Filter = this.Filter?.Trim();
    }
}