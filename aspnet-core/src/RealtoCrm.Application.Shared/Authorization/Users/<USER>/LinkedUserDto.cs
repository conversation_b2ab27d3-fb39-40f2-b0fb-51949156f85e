using System;
using Abp.Application.Services.Dto;

namespace RealtoCrm.Authorization.Users.Dto;

public class LinkedUserDto : EntityDto<long>
{
    public int? TenantId { get; set; }

    public string TenancyName { get; set; }

    public string Username { get; set; }

    public object GetShownLoginName(bool multiTenancyEnabled)
    {
        if (!multiTenancyEnabled)
        {
            return this.Username;
        }

        return string.IsNullOrEmpty(this.TenancyName)
            ? ".\\" + this.Username
            : this.TenancyName + "\\" + this.Username;
    }
}