using System;
using Abp.Authorization.Users;
using RealtoCrm.Mapping;

namespace RealtoCrm.Authorization.Users.Dto;

public class UserLoginAttemptDto
    : IReverseMap<UserLoginAttempt>

{
    public string TenancyName { get; set; }

    public string UserNameOrEmail { get; set; }

    public string ClientIpAddress { get; set; }

    public string ClientName { get; set; }

    public string BrowserInfo { get; set; }

    public string Result { get; set; }

    public DateTime CreationTime { get; set; }
}