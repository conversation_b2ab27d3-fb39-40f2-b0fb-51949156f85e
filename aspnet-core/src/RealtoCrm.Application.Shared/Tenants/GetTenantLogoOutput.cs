using Abp.Extensions;

namespace RealtoCrm.Tenants;

public class GetTenantLogoOutput
{
    public string Logo { get; set; }

    public string LogoFileType { get; set; }

    public bool HasLogo => !this.Logo.IsNullOrWhiteSpace() && !this.LogoFileType.IsNullOrWhiteSpace();

    public GetTenantLogoOutput()
    {

    }

    public GetTenantLogoOutput(string profilePicture, string logoFileType)
    {
        this.Logo = profilePicture;
        this.LogoFileType = logoFileType;
    }
}