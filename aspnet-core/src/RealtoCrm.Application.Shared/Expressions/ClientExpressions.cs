using System;
using System.Linq;
using System.Linq.Expressions;
using RealtoCrm.Clients;
using static RealtoCrm.CosherConsts.OfferStatuses;
using static RealtoCrm.CosherConsts.SearchStatuses;
using static RealtoCrm.CosherConsts.MeetingStatusesNames;
using static RealtoCrm.CosherConsts.ViewingStatuses;

namespace RealtoCrm.Expressions;

public static class ClientExpressions
{
    private static int currentYear = DateTime.Now.Date.Year;
    private static int currentMonth = DateTime.Now.Date.Month;

    public static Expression<Func<Client, bool>> IsPotentialClient()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            ExpressionsHelper.Not(HasPassedMeetings()),
            ExpressionsHelper.Not(HasViewingsForActiveSearches()),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches()),
            ExpressionsHelper.Not(HasDealsForTheCurrentMonth())
        );

    public static Expression<Func<Client, bool>> IsClientWithMeetings()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            HasPassedMeetings(),
            ExpressionsHelper.Not(HasViewingsForActiveSearches()),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches()),
            ExpressionsHelper.Not(HasDealsForTheCurrentMonth())
        );

    public static Expression<Func<Client, bool>> IsClientWithContract()
        => // same as meetings until you wire up contracts
            IsClientWithMeetings();

    public static Expression<Func<Client, bool>> IsClientWithViewings()
        => ExpressionsHelper.And(
            HasActiveSearches(),
            HasPassedMeetings(),
            HasViewingsForActiveSearches(),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches())
        );

    public static Expression<Func<Client, bool>> IsClientWithDeals()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            HasPassedMeetings(),
            HasViewingsForActiveSearches(),
            ExpressionsHelper.Or(
                HasDepositsForActiveOffers(),
                HasDepositsForActiveSearches()
            ),
            HasDealsForTheCurrentMonth()
        );

    public static Expression<Func<Client, bool>> IsClientWithDeposits()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            HasPassedMeetings(),
            HasViewingsForActiveSearches(),
            ExpressionsHelper.Or(
                HasDepositsForActiveOffers(),
                HasDepositsForActiveSearches()
            ),
            ExpressionsHelper.Not(HasDealsForTheCurrentMonth())
        );

    private static Expression<Func<Client, bool>> HasDealsForTheCurrentMonth()
        => c => (c.Searches.Any(s => s.Deals.Count != 0) &&
                 c.Searches.Any(s => s.Deals.Any(d =>
                     d.CreationTime.Date.Year == currentYear &&
                     d.CreationTime.Date.Month == currentMonth))) ||
                (c.Offers.Any(o => o.Deals.Count != 0) &&
                 c.Offers.Any(o => o.Deals.Any(d =>
                     d.CreationTime.Date.Year == currentYear &&
                     d.CreationTime.Date.Month == currentMonth)));

    private static Expression<Func<Client, bool>> HasActiveOffers()
        => c => c.Offers.Any(o => o.OfferStatus.Name == ActiveOfferStatusName);

    private static Expression<Func<Client, bool>> HasActiveSearches()
        => c => c.Searches.Any(s => s.SearchStatus != null && s.SearchStatus.Name == ActiveSearchStatusName);

    private static Expression<Func<Client, bool>> HasActiveSearchesOrOffers()
        => ExpressionsHelper.Or(HasActiveOffers(), HasActiveSearches());

    private static Expression<Func<Client, bool>> HasPassedMeetings()
        => c => c.Meetings.Any(m => m.MeetingStatus != null && m.MeetingStatus.Name == CompletedMeetingStatusName);

    private static Expression<Func<Client, bool>> HasViewingsForActiveSearches()
        => c => c.Searches
            .Where(s => s.SearchStatus != null && s.SearchStatus.Name == ActiveSearchStatusName)
            .Any(s => s.Viewings.Any(v => v.Status.Name == CompletedViewingStatusName));

    private static Expression<Func<Client, bool>> HasDepositsForActiveOffers()
        => c => c.Offers
            .Where(o => o.OfferStatus.Name == ActiveOfferStatusName)
            .Any(o => o.Deposits.Count != 0);

    private static Expression<Func<Client, bool>> HasDepositsForActiveSearches()
        => c => c.Searches
            .Where(s => s.SearchStatus != null && s.SearchStatus.Name == ActiveSearchStatusName)
            .Any(s => s.Deposits.Any());
}