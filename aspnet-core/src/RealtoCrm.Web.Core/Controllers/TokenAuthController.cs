using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Abp;
using Abp.AspNetCore.Mvc.Authorization;
using Abp.AspNetZeroCore.Web.Authentication.External;
using Abp.Authorization;
using Abp.Authorization.Users;
using Abp.MultiTenancy;
using Abp.Configuration;
using Abp.Extensions;
using Abp.Net.Mail;
using Abp.Notifications;
using Abp.Runtime.Caching;
using Abp.Runtime.Security;
using Abp.Runtime.Session;
using Abp.Timing;
using Abp.UI;
using Abp.Zero.Configuration;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using RealtoCrm.Authentication.TwoFactor;
using RealtoCrm.Authentication.TwoFactor.Google;
using RealtoCrm.Authorization;
using RealtoCrm.Authorization.Accounts.Dto;
using RealtoCrm.Authorization.Users;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Web.Authentication.JwtBearer;
using RealtoCrm.Web.Authentication.TwoFactor;
using RealtoCrm.Web.Models.TokenAuth;
using RealtoCrm.Authorization.Impersonation;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Configuration;
using RealtoCrm.Identity;
using RealtoCrm.Net.Sms;
using RealtoCrm.Notifications;
using RealtoCrm.Security.Recaptcha;
using RealtoCrm.Web.Authentication.External;
using RealtoCrm.Web.Common;
using RealtoCrm.Authorization.Delegation;

namespace RealtoCrm.Web.Controllers;

[Route("api/[controller]/[action]")]
public class TokenAuthController(
    LogInManager logInManager,
    ITenantCache tenantCache,
    AbpLoginResultTypeHelper abpLoginResultTypeHelper,
    TokenAuthConfiguration configuration,
    UserManager userManager,
    ICacheManager cacheManager,
    IOptions<AsyncJwtBearerOptions> jwtOptions,
    IExternalAuthConfiguration externalAuthConfiguration,
    IExternalAuthManager externalAuthManager,
    UserRegistrationManager userRegistrationManager,
    IImpersonationManager impersonationManager,
    IUserLinkManager userLinkManager,
    IAppNotifier appNotifier,
    ISmsSender smsSender,
    IEmailSender emailSender,
    IOptions<IdentityOptions> identityOptions,
    GoogleAuthenticatorProvider googleAuthenticatorProvider,
    ExternalLoginInfoManagerFactory externalLoginInfoManagerFactory,
    ISettingManager settingManager,
    IJwtSecurityStampHandler securityStampHandler,
    AbpUserClaimsPrincipalFactory<User, Role> claimsPrincipalFactory,
    IUserDelegationManager userDelegationManager)
    : RealtoCrmControllerBase
{
    private readonly IdentityOptions identityOptions = identityOptions.Value;
    private readonly UserManager userManager = userManager;
    public IRecaptchaValidator RecaptchaValidator { get; set; } = NullRecaptchaValidator.Instance;

    private async Task<string> EncryptQueryParameters(long userId, Tenant tenant, string passwordResetCode)
    {
        var expirationHours = await settingManager.GetSettingValueAsync<int>(
            AppSettings.UserManagement.Password.PasswordResetCodeExpirationHours
        );

        var expireDate = Uri.EscapeDataString(Clock.Now.AddHours(expirationHours)
            .ToString(RealtoCrmConsts.DateTimeOffsetFormat));

        var query = $"userId={userId}&resetCode={passwordResetCode}&expireDate={expireDate}";

        if (tenant != null)
        {
            query += $"&tenantId={tenant.Id}";
        }

        return SimpleStringCipher.Instance.Encrypt(query);
    }


    [HttpPost]
    public async Task<AuthenticateResultModel> Authenticate([FromBody] AuthenticateModel model)
    {
        if (this.UseCaptchaOnLogin())
        {
            await this.ValidateReCaptcha(model.CaptchaResponse);
        }

        var loginResult = await this.GetLoginResultAsync(
            model.UserNameOrEmailAddress,
            model.Password,
            await this.GetTenancyNameOrNull(model.UserNameOrEmailAddress)
        );

        var returnUrl = model.ReturnUrl;

        if (model.SingleSignIn.HasValue && model.SingleSignIn.Value &&
            loginResult.Result == AbpLoginResultType.Success)
        {
            loginResult.User.SetSignInToken();
            returnUrl = AddSingleSignInParametersToReturnUrl(model.ReturnUrl, loginResult.User.SignInToken,
                loginResult.User.Id, loginResult.User.TenantId);
        }

        //Password reset
        if (loginResult.User.ShouldChangePasswordOnNextLogin)
        {
            loginResult.User.SetNewPasswordResetCode();
            return new AuthenticateResultModel
            {
                ShouldResetPassword = true,
                ReturnUrl = returnUrl,
                C = await this.EncryptQueryParameters(loginResult.User.Id, loginResult.Tenant, loginResult.User.PasswordResetCode)
            };
        }

        //Two factor auth
        await userManager.InitializeOptionsAsync(loginResult.Tenant?.Id);

        string twoFactorRememberClientToken = null;
        if (await this.IsTwoFactorAuthRequiredAsync(loginResult, model))
        {
            if (model.TwoFactorVerificationCode.IsNullOrEmpty())
            {
                //Add a cache item which will be checked in SendTwoFactorAuthCode to prevent sending unwanted two factor code to users.
                await cacheManager
                    .GetTwoFactorCodeCache()
                    .SetAsync(
                        loginResult.User.ToUserIdentifier().ToString(),
                        new TwoFactorCodeCacheItem()
                    );

                return new AuthenticateResultModel
                {
                    RequiresTwoFactorVerification = true,
                    UserId = loginResult.User.Id,
                    TwoFactorAuthProviders = await userManager.GetValidTwoFactorProvidersAsync(loginResult.User),
                    ReturnUrl = returnUrl
                };
            }

            twoFactorRememberClientToken = await this.TwoFactorAuthenticateAsync(loginResult, model);
        }

        // One Concurrent Login 
        if (this.AllowOneConcurrentLoginPerUser())
        {
            await this.ResetSecurityStampForLoginResult(loginResult);
        }

        var refreshToken = this.CreateRefreshToken(
            await this.CreateJwtClaims(
                loginResult.Identity,
                loginResult.User,
                tokenType: TokenType.RefreshToken
            )
        );

        var accessToken = this.CreateAccessToken(
            await this.CreateJwtClaims(
                loginResult.Identity,
                loginResult.User,
                refreshTokenKey: refreshToken.key
            )
        );

        return new AuthenticateResultModel
        {
            AccessToken = accessToken,
            ExpireInSeconds = (int)configuration.AccessTokenExpiration.TotalSeconds,
            RefreshToken = refreshToken.token,
            RefreshTokenExpireInSeconds = (int)configuration.RefreshTokenExpiration.TotalSeconds,
            EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
            TwoFactorRememberClientToken = twoFactorRememberClientToken,
            UserId = loginResult.User.Id,
            ReturnUrl = returnUrl
        };
    }

    [HttpPost]
    public async Task<RefreshTokenResult> RefreshToken(string refreshToken)
    {
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            throw new ArgumentNullException(nameof(refreshToken));
        }

        var (isRefreshTokenValid, principal) = await this.IsRefreshTokenValid(refreshToken);
        if (!isRefreshTokenValid)
        {
            throw new ValidationException("Refresh token is not valid!");
        }

        try
        {
            var user = await userManager.GetUserAsync(
                UserIdentifier.Parse(principal.Claims.First(x => x.Type == AppConsts.UserIdentifier).Value)
            );

            if (user == null)
            {
                throw new UserFriendlyException("Unknown user or user identifier");
            }

            if (this.AllowOneConcurrentLoginPerUser())
            {
                await userManager.UpdateSecurityStampAsync(user);
                await securityStampHandler.SetSecurityStampCacheItem(user.TenantId, user.Id, user.SecurityStamp);
            }

            principal = await claimsPrincipalFactory.CreateAsync(user);

            var accessToken = this.CreateAccessToken(
                await this.CreateJwtClaims(principal.Identity as ClaimsIdentity, user)
            );

            return await Task.FromResult(new RefreshTokenResult(
                accessToken,
                GetEncryptedAccessToken(accessToken),
                (int)configuration.AccessTokenExpiration.TotalSeconds)
            );
        }
        catch (UserFriendlyException)
        {
            throw;
        }
        catch (Exception e)
        {
            throw new ValidationException("Refresh token is not valid!", e);
        }
    }

    private bool UseCaptchaOnLogin()
    {
        return this.SettingManager.GetSettingValue<bool>(AppSettings.UserManagement.UseCaptchaOnLogin);
    }


    [HttpGet]
    [AbpAuthorize]
    public async Task LogOut()
    {
        if (this.AbpSession.UserId != null)
        {
            var tokenValidityKeyInClaims = this.User.Claims.First(c => c.Type == AppConsts.TokenValidityKey);
            await this.RemoveTokenAsync(tokenValidityKeyInClaims.Value);

            var refreshTokenValidityKeyInClaims = this.User.Claims.FirstOrDefault(c => c.Type == AppConsts.RefreshTokenValidityKey);
            if (refreshTokenValidityKeyInClaims != null)
            {
                await this.RemoveTokenAsync(refreshTokenValidityKeyInClaims.Value);
            }

            if (this.AllowOneConcurrentLoginPerUser())
            {
                await securityStampHandler.RemoveSecurityStampCacheItem(this.AbpSession.TenantId, this.AbpSession.GetUserId()
                );
            }
        }
    }

    private async Task RemoveTokenAsync(string tokenKey)
    {
        await userManager.RemoveTokenValidityKeyAsync(
            await userManager.GetUserAsync(this.AbpSession.ToUserIdentifier()), tokenKey
        );

        await cacheManager.GetCache(AppConsts.TokenValidityKey).RemoveAsync(tokenKey);
    }

    [HttpPost]
    public async Task SendTwoFactorAuthCode([FromBody] SendTwoFactorAuthCodeModel model)
    {
        var cacheKey = new UserIdentifier(this.AbpSession.TenantId, model.UserId).ToString();

        var cacheItem = await cacheManager
            .GetTwoFactorCodeCache()
            .GetOrDefaultAsync(cacheKey);

        if (cacheItem == null)
        {
            //There should be a cache item added in Authenticate method! This check is needed to prevent sending unwanted two factor code to users.
            throw new UserFriendlyException(this.L("SendSecurityCodeErrorMessage"));
        }

        var user = await userManager.FindByIdAsync(model.UserId.ToString());

        if (model.Provider != GoogleAuthenticatorProvider.Name)
        {
            cacheItem.Code = await userManager.GenerateTwoFactorTokenAsync(user, model.Provider);
            var message = this.L("EmailSecurityCodeBody", cacheItem.Code);

            if (model.Provider == "Email")
            {
                await emailSender.SendAsync(await userManager.GetEmailAsync(user), this.L("EmailSecurityCodeSubject"),
                    message);
            }
            else if (model.Provider == "Phone")
            {
                await smsSender.SendAsync(await userManager.GetPhoneNumberAsync(user), message);
            }
        }

        await cacheManager.GetTwoFactorCodeCache().SetAsync(
            cacheKey,
            cacheItem
        );

        await cacheManager.GetCache("ProviderCache").SetAsync(
            "Provider",
            model.Provider
        );
    }

    [HttpPost]
    public async Task<ImpersonatedAuthenticateResultModel> ImpersonatedAuthenticate(string impersonationToken)
    {
        var result = await impersonationManager.GetImpersonatedUserAndIdentity(impersonationToken);
        var accessToken = this.CreateAccessToken(await this.CreateJwtClaims(result.Identity, result.User));

        return new ImpersonatedAuthenticateResultModel
        {
            AccessToken = accessToken,
            EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
            ExpireInSeconds = (int)configuration.AccessTokenExpiration.TotalSeconds
        };
    }

    [HttpPost]
    public async Task<ImpersonatedAuthenticateResultModel> DelegatedImpersonatedAuthenticate(long userDelegationId,
        string impersonationToken)
    {
        var result = await impersonationManager.GetImpersonatedUserAndIdentity(impersonationToken);
        var userDelegation = await userDelegationManager.GetAsync(userDelegationId);

        if (!userDelegation.IsCreatedByUser(result.User.Id))
        {
            throw new UserFriendlyException("User delegation error...");
        }

        var expiration = userDelegation.EndTime.Subtract(Clock.Now);
        var accessToken = this.CreateAccessToken(await this.CreateJwtClaims(result.Identity, result.User, expiration),
            expiration);

        return new ImpersonatedAuthenticateResultModel
        {
            AccessToken = accessToken,
            EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
            ExpireInSeconds = (int)expiration.TotalSeconds
        };
    }

    [HttpPost]
    public async Task<SwitchedAccountAuthenticateResultModel> LinkedAccountAuthenticate(string switchAccountToken)
    {
        var result = await userLinkManager.GetSwitchedUserAndIdentity(switchAccountToken);
        var accessToken = this.CreateAccessToken(await this.CreateJwtClaims(result.Identity, result.User));

        return new SwitchedAccountAuthenticateResultModel
        {
            AccessToken = accessToken,
            EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
            ExpireInSeconds = (int)configuration.AccessTokenExpiration.TotalSeconds
        };
    }

    [HttpGet]
    public List<ExternalLoginProviderInfoModel> GetExternalAuthenticationProviders()
    {
        var allProviders = externalAuthConfiguration.ExternalLoginInfoProviders
            .Select(infoProvider => infoProvider.GetExternalLoginInfo())
            .Where(this.IsSchemeEnabledOnTenant)
            .ToList();
        return this.ObjectMapper.Map<List<ExternalLoginProviderInfoModel>>(allProviders);
    }

    private bool IsSchemeEnabledOnTenant(ExternalLoginProviderInfo scheme)
    {
        if (!this.AbpSession.TenantId.HasValue)
        {
            return true;
        }

        switch (scheme.Name)
        {
            case "OpenIdConnect":
                return !settingManager.GetSettingValueForTenant<bool>(
                    AppSettings.ExternalLoginProvider.Tenant.OpenIdConnectIsDeactivated, this.AbpSession.GetTenantId());
            case "Microsoft":
                return !settingManager.GetSettingValueForTenant<bool>(
                    AppSettings.ExternalLoginProvider.Tenant.MicrosoftIsDeactivated, this.AbpSession.GetTenantId());
            case "Google":
                return !settingManager.GetSettingValueForTenant<bool>(
                    AppSettings.ExternalLoginProvider.Tenant.GoogleIsDeactivated, this.AbpSession.GetTenantId());
            case "Twitter":
                return !settingManager.GetSettingValueForTenant<bool>(
                    AppSettings.ExternalLoginProvider.Tenant.TwitterIsDeactivated, this.AbpSession.GetTenantId());
            case "Facebook":
                return !settingManager.GetSettingValueForTenant<bool>(
                    AppSettings.ExternalLoginProvider.Tenant.FacebookIsDeactivated, this.AbpSession.GetTenantId());
            case "WsFederation":
                return !settingManager.GetSettingValueForTenant<bool>(
                    AppSettings.ExternalLoginProvider.Tenant.WsFederationIsDeactivated, this.AbpSession.GetTenantId());
            default: return true;
        }
    }

    [HttpPost]
    public async Task<ExternalAuthenticateResultModel> ExternalAuthenticate(
        [FromBody] ExternalAuthenticateModel model)
    {
        var externalUser = await this.GetExternalUserInfo(model);

        var loginResult = await logInManager.LoginAsync(
            new UserLoginInfo(model.AuthProvider, externalUser.ProviderKey, model.AuthProvider),
            await this.GetTenancyNameOrNull(externalUser.EmailAddress)
        );

        switch (loginResult.Result)
        {
            case AbpLoginResultType.Success:
            {
                // One Concurrent Login 
                if (this.AllowOneConcurrentLoginPerUser())
                {
                    await this.ResetSecurityStampForLoginResult(loginResult);
                }

                var refreshToken = this.CreateRefreshToken(
                    await this.CreateJwtClaims(
                        loginResult.Identity,
                        loginResult.User,
                        tokenType: TokenType.RefreshToken
                    )
                );

                var accessToken = this.CreateAccessToken(
                    await this.CreateJwtClaims(
                        loginResult.Identity,
                        loginResult.User,
                        refreshTokenKey: refreshToken.key
                    )
                );

                var returnUrl = model.ReturnUrl;

                if (model.SingleSignIn.HasValue && model.SingleSignIn.Value &&
                    loginResult.Result == AbpLoginResultType.Success)
                {
                    loginResult.User.SetSignInToken();
                    returnUrl = AddSingleSignInParametersToReturnUrl(
                        model.ReturnUrl,
                        loginResult.User.SignInToken,
                        loginResult.User.Id,
                        loginResult.User.TenantId
                    );
                }

                return new ExternalAuthenticateResultModel
                {
                    AccessToken = accessToken,
                    EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
                    ExpireInSeconds = (int)configuration.AccessTokenExpiration.TotalSeconds,
                    ReturnUrl = returnUrl,
                    RefreshToken = refreshToken.token,
                    RefreshTokenExpireInSeconds = (int)configuration.RefreshTokenExpiration.TotalSeconds
                };
            }
            case AbpLoginResultType.UnknownExternalLogin:
            {
                var newUser = await this.RegisterExternalUserAsync(externalUser);
                if (!newUser.IsActive)
                {
                    return new ExternalAuthenticateResultModel
                    {
                        WaitingForActivation = true
                    };
                }

                //Try to login again with newly registered user!
                loginResult = await logInManager.LoginAsync(
                    new UserLoginInfo(model.AuthProvider, model.ProviderKey, model.AuthProvider),
                    await this.GetTenancyNameOrNull(externalUser.EmailAddress)
                );

                if (loginResult.Result != AbpLoginResultType.Success)
                {
                    throw abpLoginResultTypeHelper.CreateExceptionForFailedLoginAttempt(
                        loginResult.Result,
                        externalUser.EmailAddress, await this.GetTenancyNameOrNull(externalUser.EmailAddress)
                    );
                }

                var refreshToken = this.CreateRefreshToken(await this.CreateJwtClaims(loginResult.Identity,
                    loginResult.User, tokenType: TokenType.RefreshToken)
                );

                var accessToken = this.CreateAccessToken(await this.CreateJwtClaims(loginResult.Identity,
                    loginResult.User, refreshTokenKey: refreshToken.key));

                return new ExternalAuthenticateResultModel
                {
                    AccessToken = accessToken,
                    EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
                    ExpireInSeconds = (int)configuration.AccessTokenExpiration.TotalSeconds,
                    RefreshToken = refreshToken.token,
                    RefreshTokenExpireInSeconds = (int)configuration.RefreshTokenExpiration.TotalSeconds
                };
            }
            default:
            {
                throw abpLoginResultTypeHelper.CreateExceptionForFailedLoginAttempt(
                    loginResult.Result,
                    externalUser.EmailAddress, await this.GetTenancyNameOrNull(externalUser.EmailAddress)
                );
            }
        }
    }

    private async Task ResetSecurityStampForLoginResult(AbpLoginResult<Tenant, User> loginResult)
    {
        await userManager.UpdateSecurityStampAsync(loginResult.User);
        await securityStampHandler.SetSecurityStampCacheItem(loginResult.User.TenantId, loginResult.User.Id,
            loginResult.User.SecurityStamp);
        loginResult.Identity.ReplaceClaim(new Claim(AppConsts.SecurityStampKey, loginResult.User.SecurityStamp));
    }

    #region Etc

    [AbpMvcAuthorize]
    [HttpGet]
    public async Task<ActionResult> TestNotification(string message = "", string severity = "info")
    {
        if (message.IsNullOrEmpty())
        {
            message = "This is a test notification, created at " + Clock.Now;
        }

        await appNotifier.SendMessageAsync(this.AbpSession.ToUserIdentifier(),
            message,
            severity.ToPascalCase().ToEnum<NotificationSeverity>()
        );

        return this.Content("Sent notification: " + message);
    }

    #endregion

    private async Task<User> RegisterExternalUserAsync(ExternalAuthUserInfo externalLoginInfo)
    {
        string username;

        using (var providerManager =
               externalLoginInfoManagerFactory.GetExternalLoginInfoManager(externalLoginInfo.Provider))
        {
            username = providerManager.Object.GetUserNameFromExternalAuthUserInfo(externalLoginInfo);
        }

        var user = await userRegistrationManager.RegisterAsync(
            externalLoginInfo.Name,
            externalLoginInfo.Surname,
            externalLoginInfo.EmailAddress,
            username,
            await userManager.CreateRandomPassword(),
            true,
            null
        );

        user.Logins = new List<UserLogin>
        {
            new UserLogin
            {
                LoginProvider = externalLoginInfo.Provider,
                ProviderKey = externalLoginInfo.ProviderKey,
                TenantId = user.TenantId
            }
        };

        await this.CurrentUnitOfWork.SaveChangesAsync();

        return user;
    }

    private async Task<ExternalAuthUserInfo> GetExternalUserInfo(ExternalAuthenticateModel model)
    {
        var userInfo = await externalAuthManager.GetUserInfo(model.AuthProvider, model.ProviderAccessCode);
        if (!this.ProviderKeysAreEqual(model, userInfo))
        {
            throw new UserFriendlyException(this.L("CouldNotValidateExternalUser"));
        }

        return userInfo;
    }

    private bool ProviderKeysAreEqual(ExternalAuthenticateModel model, ExternalAuthUserInfo userInfo)
    {
        if (userInfo.ProviderKey == model.ProviderKey)
        {
            return true;
        }

        ;

        return userInfo.ProviderKey == model.ProviderKey.Replace("-", "").TrimStart('0');
    }

    private async Task<bool> IsTwoFactorAuthRequiredAsync(AbpLoginResult<Tenant, User> loginResult,
        AuthenticateModel authenticateModel)
    {
        if (!await this.SettingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.TwoFactorLogin
                .IsEnabled))
        {
            return false;
        }

        if (!loginResult.User.IsTwoFactorEnabled)
        {
            return false;
        }

        if ((await userManager.GetValidTwoFactorProvidersAsync(loginResult.User)).Count <= 0)
        {
            return false;
        }

        if (await this.TwoFactorClientRememberedAsync(loginResult.User.ToUserIdentifier(), authenticateModel))
        {
            return false;
        }

        return true;
    }

    private async Task<bool> TwoFactorClientRememberedAsync(UserIdentifier userIdentifier,
        AuthenticateModel authenticateModel)
    {
        if (!await this.SettingManager.GetSettingValueAsync<bool>(
                AbpZeroSettingNames.UserManagement.TwoFactorLogin.IsRememberBrowserEnabled)
           )
        {
            return false;
        }

        if (string.IsNullOrWhiteSpace(authenticateModel.TwoFactorRememberClientToken))
        {
            return false;
        }

        try
        {
            var validationParameters = new TokenValidationParameters
            {
                ValidAudience = configuration.Audience,
                ValidIssuer = configuration.Issuer,
                IssuerSigningKey = configuration.SecurityKey
            };

            foreach (var validator in jwtOptions.Value.AsyncSecurityTokenValidators)
            {
                if (validator.CanReadToken(authenticateModel.TwoFactorRememberClientToken))
                {
                    try
                    {
                        var (principal, _) = await validator.ValidateToken(
                            authenticateModel.TwoFactorRememberClientToken,
                            validationParameters
                        );

                        var userIdentifierClaim = principal.FindFirst(c => c.Type == AppConsts.UserIdentifier);
                        if (userIdentifierClaim == null)
                        {
                            return false;
                        }

                        return userIdentifierClaim.Value == userIdentifier.ToString();
                    }
                    catch (Exception ex)
                    {
                        this.Logger.Debug(ex.ToString(), ex);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            this.Logger.Debug(ex.ToString(), ex);
        }

        return false;
    }

    /* Checkes two factor code and returns a token to remember the client (browser) if needed */
    private async Task<string> TwoFactorAuthenticateAsync(AbpLoginResult<Tenant, User> loginResult,
        AuthenticateModel authenticateModel)
    {
        var twoFactorCodeCache = cacheManager.GetTwoFactorCodeCache();
        var userIdentifier = loginResult.User.ToUserIdentifier().ToString();
        var cachedCode = await twoFactorCodeCache.GetOrDefaultAsync(userIdentifier);
        var provider = cacheManager.GetCache("ProviderCache").Get("Provider", cache => cache).ToString();

        if (provider == GoogleAuthenticatorProvider.Name)
        {
            if (!await googleAuthenticatorProvider.ValidateAsync("TwoFactor",
                    authenticateModel.TwoFactorVerificationCode, userManager, loginResult.User))
            {
                throw new UserFriendlyException(this.L("InvalidSecurityCode"));
            }
        }
        else if (cachedCode?.Code == null || cachedCode.Code != authenticateModel.TwoFactorVerificationCode)
        {
            throw new UserFriendlyException(this.L("InvalidSecurityCode"));
        }

        //Delete from the cache since it was a single usage code
        await twoFactorCodeCache.RemoveAsync(userIdentifier);

        if (authenticateModel.RememberClient)
        {
            if (await this.SettingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.TwoFactorLogin
                    .IsRememberBrowserEnabled))
            {
                return this.CreateAccessToken(
                    await this.CreateJwtClaims(
                        loginResult.Identity,
                        loginResult.User
                    )
                );
            }
        }

        return null;
    }

    private async Task<string> GetTenancyNameOrNull(string userNameOrEmailAddress)
    {
        var tenantId = await this.userManager.TryGetTenantIdByUserNameOrEmailAddress(userNameOrEmailAddress);

        if (tenantId == null)
        {
            return null;
        }

        return tenantCache.GetOrNull(tenantId.Value)?.TenancyName;
    }

    private async Task<AbpLoginResult<Tenant, User>> GetLoginResultAsync(string usernameOrEmailAddress,
        string password, string tenancyName)
    {
        var shouldLockout = await this.SettingManager.GetSettingValueAsync<bool>(
            AbpZeroSettingNames.UserManagement.UserLockOut.IsEnabled
        );
        var loginResult = await logInManager.LoginAsync(usernameOrEmailAddress, password, tenancyName, shouldLockout);

        if (loginResult.Tenant != null)
        {
            this.SetTenantIdCookie(loginResult.Tenant.Id);
        }

        switch (loginResult.Result)
        {
            case AbpLoginResultType.Success:
                return loginResult;
            default:
                throw abpLoginResultTypeHelper.CreateExceptionForFailedLoginAttempt(
                    loginResult.Result,
                    usernameOrEmailAddress,
                    tenancyName
                );
        }
    }

    private string CreateAccessToken(IEnumerable<Claim> claims, TimeSpan? expiration = null)
    {
        return this.CreateToken(claims, expiration ?? configuration.AccessTokenExpiration);
    }

    private (string token, string key) CreateRefreshToken(IEnumerable<Claim> claims)
    {
        var claimsList = claims.ToList();
        return (this.CreateToken(claimsList, AppConsts.RefreshTokenExpiration),
            claimsList.First(c => c.Type == AppConsts.TokenValidityKey).Value);
    }

    private string CreateToken(IEnumerable<Claim> claims, TimeSpan? expiration = null)
    {
        var now = DateTime.UtcNow;

        var jwtSecurityToken = new JwtSecurityToken(
            issuer: configuration.Issuer,
            audience: configuration.Audience,
            claims: claims,
            notBefore: now,
            signingCredentials: configuration.SigningCredentials,
            expires: expiration == null ? (DateTime?)null : now.Add(expiration.Value)
        );

        return new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
    }

    private static string GetEncryptedAccessToken(string accessToken)
    {
        return SimpleStringCipher.Instance.Encrypt(accessToken, AppConsts.DefaultPassPhrase);
    }

    private async Task<IEnumerable<Claim>> CreateJwtClaims(
        ClaimsIdentity identity, User user,
        TimeSpan? expiration = null,
        TokenType tokenType = TokenType.AccessToken,
        string refreshTokenKey = null)
    {
        var tokenValidityKey = Guid.NewGuid().ToString();
        var claims = identity.Claims.ToList();
        var nameIdClaim = claims.First(c => c.Type == this.identityOptions.ClaimsIdentity.UserIdClaimType);

        if (this.identityOptions.ClaimsIdentity.UserIdClaimType != JwtRegisteredClaimNames.Sub)
        {
            claims.Add(new Claim(JwtRegisteredClaimNames.Sub, nameIdClaim.Value));
        }

        claims.AddRange(new[]
        {
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.Now.ToUnixTimeSeconds().ToString(),
                ClaimValueTypes.Integer64),
            new Claim(AppConsts.TokenValidityKey, tokenValidityKey),
            new Claim(AppConsts.UserIdentifier, user.ToUserIdentifier().ToUserIdentifierString()),
            new Claim(AppConsts.TokenType, tokenType.To<int>().ToString())
        });

        if (!string.IsNullOrEmpty(refreshTokenKey))
        {
            claims.Add(new Claim(AppConsts.RefreshTokenValidityKey, refreshTokenKey));
        }

        if (!expiration.HasValue)
        {
            expiration = tokenType == TokenType.AccessToken
                ? configuration.AccessTokenExpiration
                : configuration.RefreshTokenExpiration;
        }

        var expirationDate = DateTime.UtcNow.Add(expiration.Value);

        await cacheManager
            .GetCache(AppConsts.TokenValidityKey)
            .SetAsync(tokenValidityKey, "", absoluteExpireTime: new DateTimeOffset(expirationDate));

        await userManager.AddTokenValidityKeyAsync(
            user,
            tokenValidityKey,
            expirationDate
        );

        return claims;
    }

    private static string AddSingleSignInParametersToReturnUrl(string returnUrl, string signInToken, long userId,
        int? tenantId)
    {
        returnUrl += (returnUrl.Contains("?") ? "&" : "?") +
                     "accessToken=" + signInToken +
                     "&userId=" + Convert.ToBase64String(Encoding.UTF8.GetBytes(userId.ToString()));
        if (tenantId.HasValue)
        {
            returnUrl += "&tenantId=" + Convert.ToBase64String(Encoding.UTF8.GetBytes(tenantId.Value.ToString()));
        }

        return returnUrl;
    }


    private async Task<(bool isValid, ClaimsPrincipal principal)> IsRefreshTokenValid(string refreshToken)
    {
        ClaimsPrincipal principal = null;

        try
        {
            var validationParameters = new TokenValidationParameters
            {
                ValidAudience = configuration.Audience,
                ValidIssuer = configuration.Issuer,
                IssuerSigningKey = configuration.SecurityKey
            };

            foreach (var validator in jwtOptions.Value.AsyncSecurityTokenValidators)
            {
                if (!validator.CanReadToken(refreshToken))
                {
                    continue;
                }

                try
                {
                    (principal, _) = await validator.ValidateRefreshToken(refreshToken, validationParameters);
                    return (true, principal);
                }
                catch (Exception ex)
                {
                    this.Logger.Debug(ex.ToString(), ex);
                }
            }
        }
        catch (Exception ex)
        {
            this.Logger.Debug(ex.ToString(), ex);
        }

        return (false, principal);
    }


    private bool AllowOneConcurrentLoginPerUser()
    {
        return settingManager.GetSettingValue<bool>(AppSettings.UserManagement.AllowOneConcurrentLoginPerUser);
    }

    private async Task ValidateReCaptcha(string captchaResponse)
    {
        var requestUserAgent = this.Request.Headers["User-Agent"].ToString();

        if (requestUserAgent.IsNullOrEmpty())
        {
            return;
        }

        if (WebConsts.ReCaptchaIgnoreWhiteList.Contains(requestUserAgent.Trim()))
        {
            return;
        }

        await this.RecaptchaValidator.ValidateAsync(captchaResponse);
    }
}