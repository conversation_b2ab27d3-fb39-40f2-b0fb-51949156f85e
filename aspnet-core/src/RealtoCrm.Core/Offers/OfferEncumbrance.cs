using System;
using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using RealtoCrm.Files;

namespace RealtoCrm.Offers;

public class OfferEncumbrance: FullAuditedEntity<int>
{
    public int OfferId { get; set; }
    
    public DateTime EncumbranceDate { get; set; }

    public Offer Offer { get; set; } = default!;
    
    public string? Notes { get; set; }
    
    public bool Encumbrance { get; set; }
    
    public ICollection<OfferEncumbranceFile> OfferEncumbranceFiles { get; } = new List<OfferEncumbranceFile>();
}