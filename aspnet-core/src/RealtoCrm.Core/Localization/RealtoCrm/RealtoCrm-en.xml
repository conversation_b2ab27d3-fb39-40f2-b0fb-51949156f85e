<?xml version="1.0" encoding="utf-8" ?>
<localizationDictionary culture="en">
    <texts>
        <text name="AdministrativeDivisionsRead">View Administrative Divisions</text>
        <text name="AdministrativeDivisionsUpdate">Edit Administrative Divisions</text>
        <text name="AdministrativeDivisionsCreate">Create Administrative Division</text>
        <text name="AdministrativeDivisionsDelete">Delete Administrative Division</text>
        <text name="EstateRead">View Estate</text>
        <text name="EstateUpdate">Edit Estate</text>
        <text name="EstateCreate">Create Estate</text>
        <text name="EstateDelete">Delete Estate</text>
        <text name="TeamRead">View Team</text>
        <text name="TeamUpdate">Edit Team</text>
        <text name="TeamCreate">Create Team</text>
        <text name="TeamDelete">Delete Team</text>
        <text name="MarketingRead">View Marketing</text>
        <text name="MarketingUpdate">Edit Marketing</text>
        <text name="MarketingCreate">Create Marketing</text>
        <text name="MarketingDelete">Delete Marketing</text>
        <text name="GeneralSettingsRead">View General Settings</text>
        <text name="GeneralSettingsUpdate">Edit General Settings</text>
        <text name="GeneralSettingsCreate">Create General Settings</text>
        <text name="GeneralSettingsDelete">Delete General Settings</text>
        <text name="NomenclaturesRead">View Nomenclatures</text>
        <text name="NomenclaturesUpdate">Edit Nomenclatures</text>
        <text name="NomenclaturesCreate">Create Nomenclature</text>
        <text name="NomenclaturesDelete">Delete Nomenclature</text>
        <text name="MatchRead">View Match</text>
        <text name="MatchUpdate">Edit Match</text>
        <text name="MatchCreate">Create Match</text>
        <text name="MatchDelete">Delete Match</text>
        <text name="ProjectRead">View Project</text>
        <text name="ProjectUpdate">Edit Project</text>
        <text name="ProjectCreate">Create Project</text>
        <text name="ProjectDelete">Delete Project</text>
        <text name="OfferRead">View Offer</text>
        <text name="OfferUpdate">Edit Offer</text>
        <text name="OfferCreate">Create Offer</text>
        <text name="OfferChangeStatus">Change Offer Status</text>
        <text name="OfferPublishToWebsites">Publish Offer to Portals</text>
        <text name="OfferPublishingAuthorize">Manager Authorization for Publishing Offers to External Portals</text>
        <text name="OfferUnpublishToWebsites">Unpublish Offer from Portals</text>
        <text name="OfferSuggestTo">Suggest Offer to...</text>
        <text name="OfferAssignTo">Assign Offer to...</text>
        <text name="EstateChangeOwner">Change Property Owner</text>
        <text name="ContactView">View Contact</text>
        <text name="ContactUpdate">Edit Contact</text>
        <text name="ContactCreate">Create Contact</text>
        <text name="ContactBrokerAssign">Assign Broker to Contact</text>
        <text name="ContactSoftDelete">Delete Contact</text>
        <text name="OfferEstateLocationVerification">Verify Exact Property Location on Map (Pins)</text>
        <text name="OfferEstateAddressVerification">Verify Exact Property Street Name</text>
        <text name="SearchRead">View Search</text>
        <text name="SearchCreate">Create Search</text>
        <text name="SearchUpdate">Edit Search</text>
        <text name="SearchChangeStatus">Change Search Status</text>
        <text name="SearchAddContract">Add Contract to Search</text>
        <text name="SearchAssignTo">Assign Search to...</text>
        <text name="EstateHistoryRead">View Full Property History</text>
        <text name="EstateHistoryUpdate">Edit Full Property History</text>
        <text name="BuildingProjectRead">View Project</text>
        <text name="BuildingProjectCreate">Create Project</text>
        <text name="BuildingProjectUpdate">Edit Project</text>
        <text name="BuildingProjectAddContract">Add Contract to Project</text>
        <text name="BuildingProjectChangeOwner">Change Project Owner</text>
        <text name="BuildingProjectAssignTo">Assign Project to...</text>
        <text name="OfferSaveToFavourites">Save Offer to Favourites</text>
        <text name="OfferSendOverViber">Send Offer via Viber to Client</text>
        <text name="OfferSendOverEmail">Send Offer via Email to Client</text>
        <text name="OfferSendOverWhatsapp">Send Offer via Whatsapp to Client</text>
        <text name="OfferAddContract">Add Contract to Offer</text>
        <text name="SPACreate">Create SPA Analysis</text>
        <text name="SPAUpdate">Edit SPA Analysis</text>
        <text name="SPAViewPDF">View SPA Analysis in PDF</text>
        <text name="SaveAsDraft">Save as Draft</text>
        <text name="CmaAnalysis">Generate CMA Analysis</text>
        <text name="CmaPrice">CMA Price</text>
        <text name="CMASummary">Preview CMA analysis</text>
        <text name="PessimisticPrice">Pesimistic Price</text>
        <text name="OptimisticPrice">Optimistic Price</text>
        <text name="CompetitiveOffers">Competitive Offers</text>
        <text name="ClosedDeals">Closed Deals</text>
        <text name="UnfulfilledOffers">Unfulfilled Offers</text>
        <text name="SuitableClients">Suitable Clients</text>
        <text name="SuggestionsAndComments">Suggestions and Comments</text>
        <text name="ReviewAndEdit">Review and Edit</text>
        <text name="SPADownloadPDF">Download SPA Analysis in PDF</text>
        <text name="MatchingRead">Read Matching</text>
        <text name="FilterSaveToFavourites">Save Filter Settings to Favourites</text>
        <text name="SearchesHistoryRead">History of My Filters</text>
        <text name="TaskRead">Read Tasks</text>
        <text name="TaskUpdate">Edit Tasks</text>
        <text name="TaskCreate">Create Tasks</text>
        <text name="TaskAssign">Assign Task to...</text>
        <text name="TaskDone">Mark Task as Done</text>
        <text name="TaskManagerApproved">Manager Approved Task for Broker</text>
        <text name="NoteCreate">Create Notes</text>
        <text name="NoteAdd">Add Note</text>
        <text name="NoteRead">Read Notes</text>
        <text name="NoteUpdate">Edit Notes</text>
        <text name="NoteDelete">Delete Notes</text>
        <text name="ViewingCreate">Create Viewings (+ Action Create Viewing in Offers and searches)</text>
        <text name="ViewingRead">Read Viewings</text>
        <text name="ViewingUpdate">Edit Viewings</text>
        <text name="ViewingChangeStatus">Change Viewing Status</text>
        <text name="ViewingAddRecord">Viewing Record (Register)</text>
        <text name="MeetingCreate">Create Meetings</text>
        <text name="MeetingRead">Read Meetings</text>
        <text name="MeetingUpdate">Edit Meetings</text>
        <text name="MeetingChangeStatus">Change Meeting Status</text>
        <text name="OfferCreatePicturesFor">Create Pictures for Offer</text>
        <text name="OfferUpdatePicturesFor">Edit Pictures for Offer</text>
        <text name="DepositRead">Read Deposits</text>
        <text name="DepositUpdate">Edit Deposits</text>
        <text name="DepositCreate">Create Deposits</text>
        <text name="DepositChangeStatus">Change Deposit Status</text>
        <text name="DepositAssignToAnotherOffer">Transfer Deposit from One Offer to Another</text>
        <text name="DepositAddDocument">Add Document to Deposits</text>
        <text name="DealCreate">Create Deals</text>
        <text name="DealUpdate">Edit Deals</text>
        <text name="DealRead">Read Deals</text>
        <text name="DealChangeStatus">Change Deal Status</text>
        <text name="DealAddCommissionPayment">Add Commission Payment to Deal</text>
        <text name="DealAddPreliminaryContract">Add Preliminary Contract to Deal</text>
        <text name="DealDoneWithTransaction">Archive with Deal</text>
        <text name="DealArchiveWithoutTransaction">Archive without Deal</text>
        <text name="NotificationsRead">View Notifications</text>
        <text name="NotificationsUpdate">Edit Notifications</text>
        <text name="NotificationsClear">Clear Notifications</text>
        <text name="KanbanPieChartRead">Read Pie Charts</text>
        <text name="KanbanPieChartUpdate">Edit Pie Charts</text>
        <text name="KanbanPieChartCreate">Create Pie Charts</text>
        <text name="UserCreate">Create User</text>
        <text name="UserUpdate">Edit User</text>
        <text name="UserDelete">Delete User</text>
        <text name="UserRead">View User</text>
        <text name="UserImpersonate">Log in as Another User</text>
        <text name="UserRoleCreate">Create Role</text>
        <text name="UserRoleUpdate">Edit Role</text>
        <text name="UserRoleDelete">Delete Role</text>
        <text name="UserRoleRead">View Role</text>
        <text name="UserRoleCompanyFilter">Company Filter for User Role</text>
        <text name="EmployeeCreate">Create Employee</text>
        <text name="EmployeeUpdate">Edit Employee</text>
        <text name="EmployeeDelete">Delete Employee</text>
        <text name="EmployeeRead">View Employee</text>
        <text name="UserPhoneNumberRead">View Phonebook</text>
        <text name="TrainingsRead">View Trainings Module</text>
        <text name="TrainingsUpdate">Edit Everything in Trainings Module</text>
        <text name="TrainingsDelete">Delete in Trainings Module</text>
        <text name="RankingListRead">View "Top Brokers" Ranking</text>
        <text name="CallsLogRead">View Call Log</text>
        <text name="DashboardRead">View Dashboard</text>
        <text name="DashboardSetting">Hide and Show Different Widgets on the Dashboard</text>
        <text name="ProfilePictureCreate">Upload Profile Picture</text>
        <text name="ProfilePictureUpdate">Edit Profile Picture</text>
        <text name="DisplayNameCreate">Create Nickname in Profile</text>
        <text name="DisplayNameUpdate">Edit Nickname in Profile</text>
        <text name="SetDefaultCity">Set Default City in Profile</text>
        <text name="SetDefaultListSize">Set Results Per Page in Profile</text>
        <text name="SetDefaultTheme">Set CRM Theme</text>
        <text name="CallCenterSurveyCreate">Create Survey Template</text>
        <text name="CallCenterSurveyUpdate">Edit Client Responses to Surveys</text>
        <text name="InquiryCreate">Create Inquiry</text>
        <text name="InquiryUpdate">Edit Inquiry</text>
        <text name="InquiryRead">Read Inquiry</text>
        <text name="InquiryDelete">Delete Inquiry</text>
        <text name="InquirySendToEmail">Send Inquiry via Email</text>
        <text name="ContactsMerge">Merge Two Identical Contacts</text>
        <text name="TargetSetForTeam">Set Targets for My Team</text>
        <text name="TargetSetForExternalExport">Set Targets for imot.bg</text>
        <text name="BasicInformation">Basic information</text>
        <text name="LegalInformation">Legal information</text>
        <text name="PersonalData">Personal data</text>
        <text name="DocumentNumber">Document number</text>
        <text name="DocumentIssueDate">Document issue date</text>
        <text name="DocumentAuthority">Document authority MIA</text>
        <text name="BirthDate">Birthdate</text>
        <text name="UnifiedIdentificationCode">Unified identification code</text>
        <text name="VatRegistration">VAT registration</text>
        <text name="Documents">Documents</text>
        <text name="Workplace">Workplace</text>
        <text name="Position">Position</text>
        <text name="SeeMore">See more</text>
        <text name="Consultants">Consultants</text>
        <text name="LastNotes">Last notes</text>
        <text name="NoNotes">No notes yet</text>
        <text name="LastActivity">Last activity</text>
        <text name="SeeHistory">See history</text>
        <text name="Activities">Activities</text>
        <text name="StreetNumber">Номер на улица</text>
        <text name="BlockNumber">Блок</text>
        <text name="EntranceNumber">Вход</text>
        <text name="FloorNumber">Етаж</text>
        <text name="ApartmentNumber">Апартамент</text>
        <text name="Teams">Teams</text>
        <text name="Departments">Departments</text>
        <text name="Department">Department</text>
        <text name="departmentName">Department</text>
        <text name="divisionName">Divisions</text>
        <text name="Divisions">Divisions</text>
        <text name="Marketing">Marketing</text>
        <text name="Trainings">Trainings</text>
        <text name="EmployeeUpdatedSuccessfully">The employee was successfully updated!</text>
        <text name="MiddleName">Middle name</text>
        <text name="LastName">Last name</text>
        <text name="Office">Office</text>
        <text name="officeName">Office</text>
        <text name="Team">Team</text>
        <text name="TeamName">Team</text>
        <text name="IdentificationNumber">Identification number</text>
        <text name="WorkPosition">Work position</text>
        <text name="SimCardNumber">Sim card number (IMEI)</text>
        <text name="Employee">Employee</text>
        <text name="EmployeeInformation">Employee Information</text>
        <text name="Employees">Employees</text>
        <text name="EmployeesAndPermissionsAdministration">Employees and permissions management</text>
        <text name="ContractTypes">Contract тypes</text>
        <text name="Fences">Fences</text>
        <text name="Financing">Financing</text>
        <text name="Genders">Genders</text>
        <text name="Lifestyles">Lifestyles</text>
        <text name="MaritalStatus">Marital status</text>
        <text name="MaritalStatuses">Marital statuses</text>
        <text name="Nationalities">Nationalities</text>
        <text name="OperationTypes">Operation types</text>
        <text name="Titles">Titles</text>
        <text name="Vats">Vats</text>
        <text name="Progress">Progress</text>
        <text name="Matches">Matches</text>
        <text name="Clients">Clients</text>
        <text name="Calls">Calls</text>
        <text name="Note">Note</text>
        <text name="Notes">Notes</text>
        <text name="Task">Task</text>
        <text name="Meeting">Meeting</text>
        <text name="Viewing">Viewing</text>
        <text name="Offer">Offer</text>
        <text name="Offers">Offers</text>
        <text name="Estate">Estate</text>
        <text name="Euro">Euro</text>
        <text name="PriceOnRequest">Price on request</text>
        <text name="SquareMetrePrice">Square metre price</text>
        <text name="ComparativeMarketAnalysisPrice">Comparative market analysis price</text>
        <text name="OldPrice">Old price</text>
        <text name="RecommendedPrice">Recommended price</text>
        <text name="MaintenanceFee">Maintenance fee</text>
        <text name="SearchClientPlaceholder">Search client by name, email, phone numer or #</text>
        <text name="ChooseEstatePlaceholder">Choose estate</text>
        <text name="Choose">Select</text>
        <text name="Unchoose">Deselect</text>
        <text name="Building">Building</text>
        <text name="Complex">Complex</text>
        <text name="Project">Project</text>
        <text name="Furniture">Furniture</text>
        <text name="Vat">VAT</text>
        <text name="Lifestyle">Lifestyle</text>
        <text name="ContractType">Contract type</text>
        <text name="Garage">Garage</text>
        <text name="OperationType">Operation type</text>
        <text name="OperationTypePlaceholder">Offer type</text>
        <text name="PriceBgnPlaceholder">Price (BGN)</text>
        <text name="AddOffer">Add offer</text>
        <text name="AddNewOffer">Add new offer</text>
        <text name="CreatingNewOffer">Create new offer</text>
        <text name="OfferInformation">Offer information</text>
        <text name="EstateDescription">Estate description</text>
        <text name="BuildingDescription">Building description</text>
        <text name="LocationDescription">Location description</text>
        <text name="AdvantagesDescription">Advantages description</text>
        <text name="DistributionDescription">Distribution description</text>
        <text name="ConvenientTimeToView">Convenient time to view</text>
        <text name="SaveChanges">Save changes</text>
        <text name="HomePage">Home page</text>
        <text name="AboutUs">About us</text>
        <text name="Administration">Administration</text>
        <text name="Roles">Roles</text>
        <text name="Users">Users</text>
        <text name="InvalidFormMessage">Your form is invalid. Please check and correct it.</text>
        <text name="InvalidUserNameOrPassword">Invalid user name or password</text>
        <text name="ThereIsNoTenantDefinedWithName{0}">There is no tenant defined with name {0}</text>
        <text name="ThereIsNoUserRegisteredWithNameOrEmail{0}">There is no user registered with name or email {0}</text>
        <text name="TenantIsNotActive">Tenant {0} is not active.</text>
        <text name="UserIsNotActiveAndCanNotLogin">User {0} is not active and can not log in.</text>
        <text name="PleaseEnterLoginInformation">Please enter login information</text>
        <text name="TenancyName">Tenancy Name</text>
        <text name="UserNameOrEmail">User name or email</text>
        <text name="Password">Password</text>
        <text name="RememberMe">Remember me</text>
        <text name="LogIn">Log in</text>
        <text name="ForgotPassword">Forgot password?</text>
        <text name="DontYouHaveAnAccount">Don't you have an account?</text>
        <text name="Create">Create</text>
        <text name="SaveAndNew">Save and New</text>
        <text name="NoProfileYet">Don't have an account yet?</text>
        <text name="RegisterHere">Регистрирай се тук</text>
        <text name="CreateAnAccount">Create account</text>
        <text name="Pages">Pages</text>
        <text name="RoleName">Role name</text>
        <text name="EditRole">Edit role</text>
        <text name="CreateNewRole">Create new role</text>
        <text name="RoleProperties">Role properties</text>
        <text name="Permissions">Permissions</text>
        <text name="Save">Save</text>
        <text name="Cancel">Cancel</text>
        <text name="RolesHeaderInfo">Use roles to group permissions.</text>
        <text name="Static">Static</text>
        <text name="Actions">Actions</text>
        <text name="Delete">Delete</text>
        <text name="SavedSuccessfully">Saved successfully.</text>
        <text name="AreYouSure">Are you sure?</text>
        <text name="YesDelete">Yes, delete!</text>
        <text name="DeletingUser">Deleting user</text>
        <text name="UsersHeaderInfo">Manage users and permissions.</text>
        <text name="CreateNewUser">Create new profile</text>
        <text name="UserName">User name</text>
        <text name="Name">Name</text>
        <text name="Surname">Surname</text>
        <text name="EmailAddress">Email address</text>
        <text name="LastLoginTime">Last login time</text>
        <text name="EditUser">Edit user</text>
        <text name="UserInformations">User information</text>
        <text name="PasswordRepeat">Password (repeat)</text>
        <text name="ChangePasswordOnNextLogin">Change password on next login</text>
        <text name="SuccessfullyDeleted">Successfully deleted.</text>
        <text name="RoleDeleteWarningMessage">Role {0} will be deleted and unassigned from all assigned users.</text>
        <text name="UserDeleteWarningMessage">User {0} will be deleted.</text>
        <text name="Active">Active</text>
        <text name="ActiveFeminine">Active</text>
        <text name="ConfirmDelete">Confirm delete</text>
        <text name="EmailConfirm">Email confirm</text>
        <text name="Yes">Yes</text>
        <text name="No">No</text>
        <text name="MySettings">My settings</text>
        <text name="Logout">Logout</text>
        <text name="Reset">Reset</text>
        <text name="ResetPermissionsTooltip">This button immediately resets user specific permission settings and saves
            changes. The user will only have permissions from assigned roles.
        </text>
        <text name="ResetSuccessfully">Reset successfully</text>
        <text name="Tenants">Companies</text>
        <text name="TenantsHeaderInfo">Manage your companies.</text>
        <text name="CreateNewTenant">Create new compnay</text>
        <text name="TenancyCodeName">Company Name</text>
        <text name="TenantName">Company Name</text>
        <text name="TenantDeleteWarningMessage">Tenant {0} will be deleted.</text>
        <text name="CompanyDeleteWarningMessage">Tenant {0} will be deleted.</text>
        <text name="TenantWithSameNameAlreadyExists">A company with the same name already exists.</text>
        <text name="AdminEmailAddress">Admin Email</text>
        <text name="AdminPassword">Admin Password</text>
        <text name="AdminPasswordRepeat">Admin Password (repeat)</text>
        <text name="EditCompany">Edit company</text>
        <text name="Bulstat">Bulstat</text>
        <text name="MaterialResponsiblePerson">Material Responsible person</text>
        <text name="ChangingPermissions">Changing permissions</text>
        <text name="EditingUser">Editing user</text>
        <text name="CreatingNewUser">Creating new user</text>
        <text name="Edit">Edit</text>
        <text name="EditingTenant">Editing company</text>
        <text name="CreatingNewTenant">Creating new company</text>
        <text name="DeletingTenant">Deleting company</text>
        <text name="EditingRole">Editing role</text>
        <text name="CreatingNewRole">Creating new role</text>
        <text name="DeletingRole">Deleting role</text>
        <text name="CreationTime">Creation time</text>
        <text name="creationTime">Creation time</text>
        <text name="SearchWithThreeDot">Search...</text>
        <text name="ResetSpecialPermissions">Reset special permissions</text>
        <text name="SavingWithThreeDot">Saving...</text>
        <text name="Settings">Settings</text>
        <text name="SaveAll">Save all</text>
        <text name="SettingsHeaderInfo">Show and change application settings.</text>
        <text name="EmailSmtp">Email (SMTP)</text>
        <text name="DefaultFromAddress">Default from (sender) email address</text>
        <text name="DefaultFromDisplayName">Default from (sender) display name</text>
        <text name="SmtpHost">SMTP host</text>
        <text name="SmtpPort">SMTP port</text>
        <text name="UseDefaultCredentials">Use default credentials</text>
        <text name="UseHostDefaultEmailSettings">Use default email settings</text>
        <text name="DomainName">Domain name</text>
        <text name="UseSsl">Use SSL</text>
        <text name="ThisWebSiteRootAddress">Web site root address</text>
        <text name="ThisWebSiteRootAddress_Hint">It will be used when building links to this web site. It's needed for
            some tasks like building password reset links. Ex: http://mydomain.com/
        </text>
        <text name="General">General</text>
        <text name="SendPasswordResetLink_Information">A password reset link will be sent to your email to reset your
            password. If you don't get an email within a few minutes, please re-try.
        </text>
        <text name="Send">Send</text>
        <text name="PasswordReset">Password reset</text>
        <text name="InvalidUserNameOrEmailAddress">Invalid user name or email address</text>
        <text name="PasswordResetEmail_Subject">Cosher password reset</text>
        <text name="PasswordResetEmail_Title">Cosher password reset.</text>
        <text name="PasswordResetEmail_SubTitle">This email is sent you to reset and re-create your password.</text>
        <text name="PasswordResetEmail_ClickTheLinkBelowToResetYourPassword">Please click the link below to reset your
            password:
        </text>
        <text name="EmailActivation_Subject">RealtoCrm account email activation</text>
        <text name="EmailActivation_Title">Welcome to RealtoCrm.</text>
        <text name="EmailActivation_SubTitle">This email is sent you to verify your email.</text>
        <text name="EmailActivation_ClickTheLinkBelowToVerifyYourEmail">Please click the link below to verify your email
            address:
        </text>
        <text name="InvalidEmailAddress">Invalid email address</text>
        <text name="InvalidPasswordResetCode">Invalid password reset code</text>
        <text name="InvalidPasswordResetCode_Detail">Please be sure you entered this page by clicking the link sent to
            your email. If this does not solve the problem, you can re-try to request a new password reset link.
        </text>
        <text name="PasswordResetLinkExpired">Password reset link expired.</text>
        <text name="PleaseEnterYourNewPassword">Please enter your new password.</text>
        <text name="UserEmailIsNotConfirmedAndCanNotLogin">Your email address is not confirmed. Please check your email
            and click the email confirmation link to activate your account. If you did not receive activation email,
            click the 'email confirmation' to request a new email.
        </text>
        <text name="LoginFailed">Login failed!</text>
        <text name="InvalidEmailConfirmationCode">Invalid email confirmation code</text>
        <text name="InvalidEmailConfirmationCode_Detail">Please be sure you entered this page by clicking the link sent
            to your email. If this does not solve the problem, you can re-try to request a new confirmation code.
        </text>
        <text name="YourEmailIsConfirmedMessage">Your email address is successfully confirmed.</text>
        <text name="ShouldChangePasswordOnNextLogin">Should change password on next login.</text>
        <text name="SetRandomPassword">Set random password.</text>
        <text name="EmailActivation">Email activation</text>
        <text name="SendEmailActivationLink_Information">A link will be sent to your email to activate your email
            address. If you don't get an email within a few minutes, please re-try.
        </text>
        <text name="SendActivationEmail">Send activation email.</text>
        <text name="ChangePassword">Change password</text>
        <text name="CurrentPassword">Current password</text>
        <text name="NewPassword">New password</text>
        <text name="NewPasswordRepeat">New password (repeat)</text>
        <text name="YourPasswordHasChangedSuccessfully">Your password has changed successfully.</text>
        <text name="SelfUserRegistrationIsDisabledMessage">You can not register to the system!</text>
        <text name="SelfUserRegistrationIsDisabledMessage_Detail">Self user registration is disabled. Please contact the
            system administrator to register.
        </text>
        <text name="SuccessfullyRegistered">Successfully registered</text>
        <text name="ConfirmationMailSentPleaseClickLinkInTheEmail">A confirmation mail sent to {0}. Please click link in
            the email in order to confirm your mail address.
        </text>
        <text name="YourAccountIsWaitingToBeActivatedByAdmin">Your account is waiting to be activated by system admin.
        </text>
        <text name="NameSurname">Name and Surname</text>
        <text name="UserManagement">User management</text>
        <text name="AllowUsersToRegisterThemselves">Allow users to register to the system.</text>
        <text name="AllowUsersToRegisterThemselves_Hint">If you disable this, users will only be added by admin using
            user management page.
        </text>
        <text name="NewRegisteredUsersIsActiveByDefault">New registered users are active by default.</text>
        <text name="NewRegisteredUsersIsActiveByDefault_Hint">If you disable this, new users will not be active (and can
            not login) until admin manually activates the account.
        </text>
        <text name="Default">Default</text>
        <text name="DefaultRole_Description">Assign to new users by default.</text>
        <text name="StaticRole_Tooltip">Static roles can not be deleted.</text>
        <text name="EmailConfirmationRequiredForLogin">Email confirmation required for login.</text>
        <text name="{0}UserCannotBeDeleted">{0} user can not be deleted.</text>
        <text name="TenantNameCanNotBeEmpty">Tenant name can not be empty</text>
        <text name="ChangeProfilePicture">Change profile picture</text>
        <text name="TenantName_Regex_Description">Tenant name must be at least 2 chars, starts with a letter and
            continue with letter, number, dash or underscore and without any spaces.
        </text>
        <text name="ProfilePicture_Change_Info">You can select a JPG/JPEG/PNG file with a maximum {0}MB size.</text>
        <text name="ProfilePicture_Change_Error">Your profile picture could not be changed.</text>
        <text name="ProfilePicture_Warn_SizeLimit">Picture size must be smaller than {0}MB. Please select another
            file.
        </text>
        <text name="ProfilePicture_Warn_FileType">You can only select JPG/JPEG file. Please select another file.</text>
        <text name="Dashboard">Dashboard</text>
        <text name="DashboardHeaderInfo">statistics and reports</text>
        <text name="GoToApplication">Go to application</text>
        <text name="TenantInformations">Tenant information</text>
        <text name="PersonalInformations">Personal information</text>
        <text name="AccountSettings">Account settings</text>
        <text name="Captha_Hint">Please prove you are not a robot</text>
        <text name="Back">Back</text>
        <text name="Submit">Submit</text>
        <text name="SignUp">Sign up</text>
        <text name="CaptchaCanNotBeEmpty">You must prove that you are not a robot.</text>
        <text name="IncorrectCaptchaAnswer">Unable to determine that you are not a robot.</text>
        <text name="FormIsNotValidMessage">Form is not valid. Please check and fix errors.</text>
        <text name="CanNotChangeAdminUserName">Can not change username of the admin.</text>
        <text name="Error">Error!</text>
        <text name="OopsYouAreLost">Oops! You're lost.</text>
        <text name="WeCanNotFindThePage">We can not find the page you're looking for.</text>
        <text name="DashboardDemo_Note_Title">Welcome to ASP.NET Zero startup project!</text>
        <text name="DashboardDemo_Note_Info">
            In this demo, you can use the left menu to test the application.
            This dashboard page is just for demonstration, but all other pages and functionalities are fully
            implemented, working and ready for production.
            You can add/apply your business requirements on this startup project (or remove any functionality you don't
            need).
            Thus, you can have a signification time gain to immediately start your own project requirements.
        </text>
        <text name="WelcomePage_Title">Welcome!</text>
        <text name="WelcomePage_Info">Welcome to the application. You can use the menu to start using the application.
            If you don't see any menu item, probably you don't have permission to see any page. Consider asking a system
            administrator for permissions.
        </text>
        <text name="RequestedFileDoesNotExists">Requested file does not exist!</text>
        <text name="MailSent">Mail sent</text>
        <text name="PasswordResetMailSentMessage">A password reset link was sent to your email address. Please check
            your inbox.
        </text>
        <text name="UseCaptchaOnRegistration">Use security image question (captcha) on registration.</text>
        <text name="FormBasedRegistration">Form-Based Registration</text>
        <text name="LdapSettings">LDAP Settings</text>
        <text name="EnableLdapAuthentication">Enable LDAP Authentication.</text>
        <text name="OtherSettings">Other Settings</text>
        <text name="AuditLogs">Audit logs</text>
        <text name="Success">Success</text>
        <text name="Time">Time</text>
        <text name="Service">Service</text>
        <text name="Action">Action</text>
        <text name="Duration">Duration</text>
        <text name="IpAddress">IP address</text>
        <text name="Client">Client</text>
        <text name="Browser">Browser</text>
        <text name="Xms">{0} ms</text>
        <text name="Close">Close</text>
        <text name="Refresh">Refresh</text>
        <text name="ErrorState">Error state</text>
        <text name="All">All</text>
        <text name="HasError">Has error</text>
        <text name="AdvancedFilters">Аdvanced filters</text>
        <text name="Characteristics">Characteristics</text>
        <text name="Advantages">Advantages</text>
        <text name="Disadvantages">Disadvantages</text>
        <text name="Estates">Estates</text>
        <text name="Details">Details</text>
        <text name="Advertisement">Advertisement</text>
        <text name="From">from</text>
        <text name="To">to</text>
        <text name="At">at</text>
        <text name="AtTime">at</text>
        <text name="By">by</text>
        <text name="ShowAdvancedFilters">Show advanced filters</text>
        <text name="HideAdvancedFilters">Hide advanced filters</text>
        <text name="AuditLogDetail">Audit log detail</text>
        <text name="Parameters">Parameters</text>
        <text name="DateRange">Date range</text>
        <text name="ActionInformations">Action information</text>
        <text name="Today">Today</text>
        <text name="Yesterday">Yesterday</text>
        <text name="Last7Days">Last 7 days</text>
        <text name="Last30Days">Last 30 days</text>
        <text name="ThisMonth">This month</text>
        <text name="LastMonth">Last month</text>
        <text name="TenantSelection">Tenant Selection</text>
        <text name="TenantSelection_Detail">Please select one of the following tenants.</text>
        <text name="LoginWith">Login with:</text>
        <text name="PasswordChangeDontRememberMessage">If you can not remember your password, {0}.</text>
        <text name="ClickHere">click here</text>
        <text name="Apply">Apply</text>
        <text name="CustomRange">Custom range</text>
        <text name="DashboardNoteForMpaVersion">
            <![CDATA[
      See Demo for Multi-Page Application version with <strong>ASP.NET MVC</strong> and <strong>jQuery</strong>.
      ]]>
        </text>
        <text name="DashboardNoteForSpaVersion">
            <![CDATA[
      Notice: There is also a <strong>Single-Page Application</strong> version built using ASP.NET MVC and <strong>AngularJs</strong>.
      ]]>
        </text>
        <text name="SeeDemo">See demo</text>
        <text name="Edition">Edition</text>
        <text name="Editions">Editions</text>
        <text name="EditionsHeaderInfo">Manage editions and features of the application</text>
        <text name="CreateNewEdition">Create new edition</text>
        <text name="EditEdition">Edit edition</text>
        <text name="EditionProperties">Edition properties</text>
        <text name="EditionName">Edition name</text>
        <text name="Features">Features</text>
        <text name="EditionDeleteWarningMessage">Are you sure to delete the edition {0}?</text>
        <text name="CreatingNewEdition">Creating new edition</text>
        <text name="EditingEdition">Editing edition</text>
        <text name="DeletingEdition">Deleting edition</text>
        <text name="ChangingFeatures">Changing features</text>
        <text name="ResetSpecialFeatures">Reset special features</text>
        <text name="ResetFeaturesTooltip">This button immediately resets tenant-specific feature settings and saves
            changes. The tenant will only have features from assigned edition.
        </text>
        <text name="NotAssigned">Not assigned</text>
        <text name="InvalidFeaturesWarning">One or more feature values are not valid!</text>
        <text name="Languages">Languages</text>
        <text name="LanguagesHeaderInfo">Manage user interface languages.</text>
        <text name="CreateNewLanguage">Create new language</text>
        <text name="Code">Code</text>
        <text name="CreatingNewLanguage">Creating new language</text>
        <text name="EditingLanguage">Editing language</text>
        <text name="DeletingLanguages">Deleting language</text>
        <text name="ChangingTexts">Changing texts</text>
        <text name="SuccessfullySaved">Successfully saved.</text>
        <text name="Language">Language</text>
        <text name="Flag">Flag</text>
        <text name="SetAsDefaultLanguage">Set as default language</text>
        <text name="EditLanguage">Edit language</text>
        <text name="ChangeTexts">Change texts</text>
        <text name="TargetValue">Target value</text>
        <text name="BaseValue">Base value</text>
        <text name="Key">Key</text>
        <text name="SaveAndClose">Save &amp; Close</text>
        <text name="SaveAndNext">Save &amp; Next</text>
        <text name="EditText">Edit text</text>
        <text name="LanguageTexts">Language texts</text>
        <text name="LanguageTextsHeaderInfo">Edit texts for languages.</text>
        <text name="EmptyOnes">Empty ones</text>
        <text name="Source">Source</text>
        <text name="BaseLanguage">Base language</text>
        <text name="TargetLanguage">Target language</text>
        <text name="Previous">Previous</text>
        <text name="LanguageDeleteWarningMessage">Are you sure to delete the language {0}?</text>
        <text name="CanNotEditOrDeleteDefaultLanguages">Can not edit or delete default languages. You can only edit or
            delete the languages you added. But you can change texts of all languages.
        </text>
        <text name="LookingForMpaVersion">Looking for Multi-Page Application Version?</text>
        <text name="LookingForSpaVersion">Looking for Single-Page Application Version?</text>
        <text name="ThisLanguageAlreadyExists">This language already exists!</text>
        <text name="OrganizationUnitsHeaderInfo">Use organization units to organize users and entities</text>
        <text name="OrganizationTree">Organization tree</text>
        <text name="Members">Members</text>
        <text name="SelectAnOrganizationUnitToSeeMembers">Select an organization unit to see members.</text>
        <text name="OrganizationUnitDeleteWarningMessage">Are you sure to delete organization unit {0}?</text>
        <text name="AddSubUnit">Add sub-unit</text>
        <text name="AddMember">Add member</text>
        <text name="SuccessfullyAdded">Successfully added</text>
        <text name="RemoveUserFromOuWarningMessage">Are you sure to remove user {0} from organization unit {1}?</text>
        <text name="SuccessfullyRemoved">Successfully removed</text>
        <text name="SelectAnItem">Select an item</text>
        <text name="NewOrganizationUnit">New organization unit</text>
        <text name="SelectAUser">Select a user</text>
        <text name="Select">Select</text>
        <text name="UserIsAlreadyInTheOrganizationUnit">This user is already in the organization unit!</text>
        <text name="AddRootUnit">Add root unit</text>
        <text name="AddUser">Add user</text>
        <text name="NoOrganizationUnitDefinedYet">No organization unit defined yet.</text>
        <text name="OrganizationUnitMoveConfirmMessage">Please confirm to move {0} to under {1}.</text>
        <text name="Root">Root</text>
        <text name="SuccessfullyMoved">Successfully moved.</text>
        <text name="AddedTime">Addition time</text>
        <text name="ManagingMembers">Managing members</text>
        <text name="ManagingOrganizationTree">Managing organization tree</text>
        <text name="LoginForUsers">Login for users</text>
        <text name="LoginAsThisUser">Login as this user</text>
        <text name="BackToMyAccount">Back to my account</text>
        <text name="LoginForTenants">Login for tenants</text>
        <text name="LoginAsThisTenant">Login as this tenant</text>
        <text name="AuditLogImpersonatedOperationInfo">Note: This operation is performed by another user on behalf of
            this user.
        </text>
        <text name="CustomData">Custom data</text>
        <text name="None">None</text>
        <text name="YouCanBackToYourAccount">You can back to your account here.</text>
        <text name="CascadeImpersonationErrorMessage">Can not make cascade impersonation. This is already an
            impersonated login!
        </text>
        <text name="FromTenantToHostImpersonationErrorMessage">Can not impersonate a host user from a tenant user!
        </text>
        <text name="DifferentTenantImpersonationErrorMessage">Can not impersonate a user of a different tenant!</text>
        <text name="ImpersonationTokenErrorMessage">Impersonation token is invalid or expired!</text>
        <text name="NotImpersonatedLoginErrorMessage">This is not an impersonated login!</text>
        <text name="YouCannotLinkToSameAccount">You can not link to same account!</text>
        <text name="ChangePasswordBeforeLinkToAnAccount">You must change your password before linking this account!
        </text>
        <text name="LinkedAccounts">Linked accounts</text>
        <text name="ManageLinkedAccounts">Manage linked accounts</text>
        <text name="LinkNewAccount">Link new account</text>
        <text name="SuccessfullyUnlinked">Successfully unlinked</text>
        <text name="LinkedUserDeleteWarningMessage">Link to user {0} will be deleted.</text>
        <text name="SwitchToLinkedAccountTokenErrorMessage">Impersonation token is invalid or expired!</text>
        <text name="WelcomeToTheApplicationNotificationMessage">Welcome to RealtoCrm! Notification system is used to
            inform you for intended events. You can select which type of notifications you want to receive from the
            notification settings.
        </text>
        <text name="NewUserRegisteredNotificationDefinition">On a new user registered to the application.</text>
        <text name="NewUserRegisteredNotificationMessage">A new user registered to the application. User name:
            {userName}, Email address: {emailAddress}.
        </text>
        <text name="SeeAllNotifications">See all notifications</text>
        <text name="SetAsRead">Set as read</text>
        <text name="SetAllAsRead">Set all as read</text>
        <text name="NotificationSettings">Notification settings</text>
        <text name="ReceiveNotifications">Receive Notifications</text>
        <text name="ReceiveNotifications_Definition">This option can be used to completely enable/disable receiving
            notifications.
        </text>
        <text name="NotificationTypes">Notification Types</text>
        <text name="ReceiveNotifications_DisableInfo">You completely disabled receiving notifications. You can enable it
            and select notification types you want to receive.
        </text>
        <text name="Notifications">Notifications</text>
        <text name="ThereAreNoNotifications">There are no notifications.</text>
        <text name="Unread">Unread</text>
        <text name="YouCanNotDeleteOwnAccount">You can not delete own user account!</text>
        <text name="YouCanNotDeleteGlobalAdminAccount">You can not delete global admin account!</text>
        <text name="On">On</text>
        <text name="Off">Off</text>
        <text name="Maintenance">Maintenance</text>
        <text name="Caches">Caches</text>
        <text name="CacheSuccessfullyCleared">Cache successfully cleared.</text>
        <text name="AllCachesSuccessfullyCleared">All caches successfully cleared.</text>
        <text name="ClearAll">Clear all</text>
        <text name="Clear">Clear</text>
        <text name="CachesHeaderInfo">You can clear caches in application on this page.</text>
        <text name="TestEmailSettingsHeader">Test Email Settings</text>
        <text name="SendTestEmail">Send Test Email</text>
        <text name="TestEmail_Subject">RealtoCrm test email</text>
        <text name="TestEmail_Body">This is a test email.</text>
        <text name="TestEmailSentSuccessfully">A test email was sent successfully.</text>
        <text name="WebSiteLogs">Website Logs</text>
        <text name="WebSiteLogsHeaderInfo">You can see latest logs in this page or download all logs in a single zip
            file.
        </text>
        <text name="DownloadAll">Download all</text>
        <text name="TenantManagement">Tenant management</text>
        <text name="AllowTenantsToRegisterThemselves">Allow tenants to register to the system.</text>
        <text name="AllowTenantsToRegisterThemselves_Hint">If you disable this, tenants will only be added by admin
            using tenant management page.
        </text>
        <text name="NewRegisteredTenantsIsActiveByDefault">New registered tenants are active by default.</text>
        <text name="NewRegisteredTenantsIsActiveByDefault_Hint">If you disable this, new tenants will not be active (and
            can not login) until admin manually activates the account.
        </text>
        <text name="TenantSignUp">Tenant SignUp</text>
        <text name="NewTenantRegisteredNotificationDefinition">On a new tenant registered to the application.</text>
        <text name="NewTenantRegisteredNotificationMessage">A new tenant registered to the application. Tenancy name:
            {tenancyName}.
        </text>
        <text name="LoginAttempts">Login attempts</text>
        <text name="Failed">Failed</text>
        <text name="NewTenant">New tenant</text>
        <text name="ResizedProfilePicture_Warn_SizeLimit">Resized picture size must be smaller than {0}KB. Please resize
            down and try again.
        </text>
        <text name="RegisterFormUserNameInvalidMessage">Please do not enter an email address for username.</text>
        <text name="UseHostDatabase">Use host database</text>
        <text name="DatabaseConnectionString">Database connection string</text>
        <text name="TenantDatabaseConnectionStringChangeWarningMessage">Notice: Before changing the database connection
            string for a tenant, you should move the tenant database to the new location. Changing connection string
            does not move the tenant database.
        </text>
        <text name="Timezone">Timezone</text>
        <text name="HasOwnDatabase">Has own database</text>
        <text name="TimeZoneSettingChangedRefreshPageNotification">Timezone setting changed. Click OK button to refresh
            page and changes to take effect.
        </text>
        <text name="Ok">Ok</text>
        <text name="Permission">Permission</text>
        <text name="Role">Role</text>
        <text name="Security">Security</text>
        <text name="PasswordComplexity">Password complexity</text>
        <text name="UseDefaultSettings">Use default settings</text>
        <text name="PasswordComplexityNotSatisfied">Password complexity is not satisfied.</text>
        <text name="AllowedMinimumLength">Minimum password length cannot be shorter than {0}.</text>
        <text name="AllowedMinimumPasswordResetCodeExpirationHours">Minimum password reset code expiration hours cannot
            be shorter than {0}.
        </text>
        <text name="YouAlreadySentAFriendshipRequestToThisUser">You already added this user.</text>
        <text name="YouCannotBeFriendWithYourself">You can not be a friend with yourself.</text>
        <text name="NewChatMessageEmail_Title">Message Details</text>
        <text name="NewChatMessageEmail_SubTitle">New chat message</text>
        <text name="NewChatMessageEmail_Subject">You have a new chat message</text>
        <text name="Sender">Sender</text>
        <text name="Message">Message</text>
        <text name="UserBlocked">User blocked successfully.</text>
        <text name="UserUnblocked">Block removed from user successfully.</text>
        <text name="FriendRemoved">User removed from your friend list.</text>
        <text name="BlockUser">Block user</text>
        <text name="UnblockUser">Unblock</text>
        <text name="RemoveFriend">Remove user</text>
        <text name="AreYouSureToRemoveFromFriends">The user will be removed from your friend list.</text>
        <text name="FriendshipRequestAccepted">Friendship request accepted</text>
        <text name="UserSendYouAFriendshipRequest">{0} added you as a friend.</text>
        <text name="Friends">Friends</text>
        <text name="Others">Others</text>
        <text name="BlockedUsers">Blocked Users</text>
        <text name="YouDontHaveAnyFriend">You don't have any friends. Write a username to above input box and click "Add
            Friend" button.
        </text>
        <text name="YouDontHaveAnyBlockedFriend">You don't have any blocked users. In order to block a friend, select a
            friend and select block from actions dropdown.
        </text>
        <text name="ChatUserSearch_Hint">
            <![CDATA[
      Write only username for same tenant users, <br>
      <strong>[tenancyName]\[userName]</strong> for other tenant's users <br><br>
      for example: <br>
      .\admin -> for host admin <br>
      Test\admin -> for test tenant's admin <br>
      admin -> for your tenant's admin
      ]]>
        </text>
        <text name="AddFriend">Add friend</text>
        <text name="AddFriendFromDifferentTenant">Add a friend from different tenant</text>
        <text name="IsHostUser">Is host user</text>
        <text name="MembersInThisTenantTitle">Members in this tenant</text>
        <text name="TypeAMessageHere">Type a message here...</text>
        <text name="Chat">Chat</text>
        <text name="ChatFeature">Chat</text>
        <text name="ChatFeatureIsNotEnabledForSender">Chat is not enabled for you.</text>
        <text name="ChatFeatureIsNotEnabledForReceiver">Chat is not enabled for tenant.</text>
        <text name="UserIsBlocked">User is blocked.</text>
        <text name="ChatIsNotConnectedWarning">Chat is not connected.</text>
        <text name="Demo_SampleChatMessage">Hi, this is a test message. Please login with my account in a different
            browser and check out how chat feature works.
        </text>
        <text name="TenantToTenantChatFeatureIsNotEnabledForSender">Chat with other tenants is not enabled for you.
        </text>
        <text name="TenantToTenantChatFeatureIsNotEnabledForReceiver">Chat with other tenants is not enabled for
            tenant.
        </text>
        <text name="TenantToHostChatFeatureIsNotEnabledForSender">Chat with host is not enabled for you.</text>
        <text name="TenantToHostChatFeatureIsNotEnabledForReceiver">Chat with host is not enabled for tenant.</text>
        <text name="TenantToTenantChatFeature">Chat with other tenants</text>
        <text name="TenantToHostChatFeature">Chat with host</text>
        <text name="FilterByPermission">Filter by permission</text>
        <text name="FilterByRole">Filter by role</text>
        <text name="FilterOrAddUser">Filter/Add user</text>
        <text name="HangfireDashboard">Hangfire dashboard</text>
        <text name="CouldNotCompleteLoginOperation">Could not complete the login operation.</text>
        <text name="UnAuthorizedOperation">Unauthorized Operation</text>
        <text name="YouAreNotAuthorizedToPerformThisOperation">You are not authorized to perform this operation.</text>
        <text name="TargetUserNotFoundProbablyDeleted">Target user could not be found. It's probably deleted.</text>
        <text name="UserLockedOutMessage">The user account has been locked out. Please try again later.</text>
        <text name="SendSecurityCode">Send security code.</text>
        <text name="SendSecurityCode_Information">You should verify yourself to login. Please select a verification
            type. A code will be sent based on selected verification type.
        </text>
        <text name="SendSecurityCodeErrorMessage">Security code could not be sent!</text>
        <text name="VerifySecurityCodeNotLoggedInErrorMessage">You should be logged in first, in order to verify
            yourself! Probably, your login has been timed out. Please go to the login page and re-try it.
        </text>
        <text name="VerificationCode">Verification code</text>
        <text name="VerifySecurityCode">Verify Security Code</text>
        <text name="VerifySecurityCode_Information">Please enter the verification code sent to you.</text>
        <text name="RememberThisBrowser">Remember this browser</text>
        <text name="InvalidSecurityCode">Invalid security code!</text>
        <text name="PhoneNumber">Phone number</text>
        <text name="Unlock">Unlock</text>
        <text name="UnlockedTheUser">Unlocked the user {0}</text>
        <text name="UserLockOut">User Lock Out</text>
        <text name="EnableUserAccountLockingOnFailedLoginAttempts">Enable user account locking on failed login
            attempts
        </text>
        <text name="MaxFailedAccessAttemptsBeforeLockout">Maximum number of failed login attempt count before locking
            the account
        </text>
        <text name="DefaultAccountLockoutDurationAsSeconds">Account locking duration (as seconds)</text>
        <text name="TwoFactorLogin">Two Factor Login</text>
        <text name="EnableTwoFactorLogin">Enable two factor user login.</text>
        <text name="IsEmailVerificationEnabled">Enable email verification.</text>
        <text name="IsSmsVerificationEnabled">Enable SMS verification.</text>
        <text name="AllowToRememberBrowserForTwoFactorLogin">Allow to remember browser. If you allow this, users can
            select to remember browser to skip second time two factor login for the same browser.
        </text>
        <text name="CurrentTenant">Current tenant</text>
        <text name="NotSelected">Not selected</text>
        <text name="Change">Change</text>
        <text name="ChangeTenant">Switch tenant</text>
        <text name="UnknownTenantId{0}">Unknown tenant id: {0}</text>
        <text name="TenantIdIsNotActive{0}">Inactive tenant id: {0}</text>
        <text name="TimeoutPleaseTryAgain">Timeout! Please try again.</text>
        <text name="RemainingTime">Remaining time</text>
        <text name="SecondShort{0}">{0} sec</text>
        <text name="ResetCode">Reset code</text>
        <text name="IsTwoFactorEnabled">Two factor authentication enabled</text>
        <text name="IsLockoutEnabled">Lockout enabled</text>
        <text name="IsLockoutEnabled_Hint">User is locked for a while after a certain amount of failed login attempts.
        </text>
        <text name="TenantRegistrationLoginInfo">Click here to login</text>
        <text name="CouldNotValidateExternalUser">Could not validate external user login</text>
        <text name="LeaveEmptyToSwitchToHost">Leave empty to switch to the host</text>
        <text name="PleaseWaitToConfirmYourEmailMessage">Please wait while confirming your email address...</text>
        <text name="ChatIsConnecting">Chat is connecting...</text>
        <text name="Note_RefreshPageForPermissionChanges">If you are changing your own permissions, you may need to
            refresh page (F5) to take effect of permission changes on your own screen!
        </text>
        <text name="ApplicationLogo">Application Logo</text>
        <text name="ApplicationLightLogo">Application Light Logo</text>
        <text name="ApplicationLightIcon">Application Light Icon</text>
        <text name="ApplicationDarkLogo">Application Dark Logo</text>
        <text name="ApplicationDarkIcon">Application Dark Icon</text>
        <text name="CustomCSS">Custom CSS</text>
        <text name="Upload">Upload</text>
        <text name="UploadImage">Upload image</text>
        <text name="Download">Download</text>
        <text name="UploadLogo_Info">Select a JPG/JPEG/PNG/GIF file with a maximum of 100KB size and 512x128 pixel
            resolution.
        </text>
        <text name="UploadLogoIcon_Info">Select a JPG/JPEG/PNG/GIF file with a maximum of 50KB size and 128x128 pixel
            resolution.
        </text>
        <text name="UploadCSS_Info">Select a .css file with a maximum of 1MB size.</text>
        <text name="Browse">Browse</text>
        <text name="Appearance">Appearance</text>
        <text name="ClearedSuccessfully">Cleared successfully</text>
        <text name="File_Empty_Error">Please select a file!</text>
        <text name="File_SizeLimit_Error">Size of the file exceeds allowed limits!</text>
        <text name="File_Invalid_Type_Error">Invalid file type!</text>
        <text name="MaximumUserCount">Maximum user count</text>
        <text name="MaximumUserCount_Description">0 = unlimited</text>
        <text name="MaximumUserCount_Error_Message">Reached maximum allowed user count!</text>
        <text name="MaximumUserCount_Error_Detail">This tenant is allowed to have a maximum of {0} users.</text>
        <text name="UnlockedTenandAdmin">Unlocked admin user for {0}</text>
        <text name="Email">Email</text>
        <text name="EmailSecurityCodeSubject">Security Code</text>
        <text name="EmailSecurityCodeBody">Your security code is: {0}</text>
        <text name="Sms">Sms</text>
        <text name="SmsSecurityCodeMessage">Your security code is: {0}</text>
        <text name="PasswordComplexity_RequireDigit_Hint">Passwords must have at least one digit ('0'-'9').</text>
        <text name="PasswordComplexity_RequireLowercase_Hint">Passwords must have at least one lowercase ('a'-'z').
        </text>
        <text name="PasswordComplexity_RequireNonAlphanumeric_Hint">Passwords must have at least one non-alphanumeric
            character.
        </text>
        <text name="PasswordComplexity_RequireUppercase_Hint">Passwords must have at least one uppercase ('A'-'Z').
        </text>
        <text name="PasswordComplexity_RequiredLength_Hint">Passwords must be at least {0} characters.</text>
        <text name="PasswordComplexity_RequireDigit">Require digit</text>
        <text name="PasswordComplexity_RequireLowercase">Require lowercase</text>
        <text name="PasswordComplexity_RequireNonAlphanumeric">Require non-alphanumeric</text>
        <text name="PasswordComplexity_RequireUppercase">Require uppercase</text>
        <text name="PasswordComplexity_RequiredLength">Required length</text>
        <text name="HostDashboardHeaderInfo">General statistics and recent activities</text>
        <text name="DashboardSampleStatistics">Sample statistics</text>
        <text name="DashboardSampleStatisticsHelpText">Progress</text>
        <text name="NewTenants">New tenants</text>
        <text name="NewSubscriptionAmount">New subscription amount</text>
        <text name="Income">Income</text>
        <text name="IncomeStatistics">Income statistics</text>
        <text name="ChangeDateRange">Change date range</text>
        <text name="Daily">Daily</text>
        <text name="Weekly">Weekly</text>
        <text name="Monthly">Monthly</text>
        <text name="DateFormatShort">MM.dd.yyyy</text>
        <text name="EditionStatistics">Edition statistics</text>
        <text name="SubscriptionExpiringTenants">Subscription expiring tenants</text>
        <text name="RemainingDay">Remaining day</text>
        <text name="SeeAllRecords">See all records</text>
        <text name="RecentTenants">Recent tenants</text>
        <text name="RecentTenantsHelpText">Last {1} customers registered in {0} days...</text>
        <text name="ExpiringTenantsHelpText">First {1} clients expiring in {0} days...</text>
        <text name="NoData">No data</text>
        <text name="SubscriptionEndDate">Subscription end date</text>
        <text name="TenantNameOrTenancyCode">Company name</text>
        <text name="IncomeWithAmount">Income: {0}</text>
        <text name="Subscription">Subscription</text>
        <text name="Extend">Extend</text>
        <text name="Upgrade">Upgrade</text>
        <text name="PaymentPeriodType">Payment period type</text>
        <text name="PaymentInfo">Payment info</text>
        <text name="MonthlyPrice">Monthly price</text>
        <text name="AnnualPrice">Annual price</text>
        <text name="StartUsingFree">Start using free</text>
        <text name="TryIt">Try it</text>
        <text name="Days">Days</text>
        <text name="PerMonth">Per month</text>
        <text name="PerYear">Per year</text>
        <text name="TestCheckFeature">Test check feature</text>
        <text name="TestCheckFeature2">Test check feature 2</text>
        <text name="Unlimited">Unlimited</text>
        <text name="LatestPaymentAmount">Latest payment amount</text>
        <text name="Payment">Payment</text>
        <text name="Next">Next</text>
        <text name="Start">Start</text>
        <text name="FreeTrial">Free trial</text>
        <text name="BuyNow">Buy now</text>
        <text name="WhatWillDoneAfterSubscriptionExpiry">What will be done after subscription expiry</text>
        <text name="DeactiveTenant">Deactive tenant</text>
        <text name="AssignToAnotherEdition">Assign to another edition</text>
        <text name="SubscriptionPrice">Subscription price</text>
        <text name="Free">Free</text>
        <text name="Paid">Paid</text>
        <text name="IsTrialActive">Is trial active</text>
        <text name="TrialDayCount">Trial day count</text>
        <text name="WaitAfterSubscriptionExpireDate">Wait after subscription expire date</text>
        <text name="WaitingDayAfterExpire">Waiting day after expire</text>
        <text name="DisplayOrder">DisplayOrder</text>
        <text name="Annual">Annual</text>
        <text name="SubscriptionStartDate">Subscription start date</text>
        <text name="Pricing">Pricing</text>
        <text name="ClientIsRequestingYourPermission">{0} is requesting your permission</text>
        <text name="ApplicationAccess">Application Access</text>
        <text name="RememberMyDecision">Remember my decision</text>
        <text name="Allow">Allow</text>
        <text name="DoNotAllow">Do not allow</text>
        <text name="Your user identifier">Your user identifier</text>
        <text name="Required">Required</text>
        <text name="User profile">User profile</text>
        <text name="Your user profile information (first name, last name, etc.)">Your user profile information (first
            name, last name, etc.)
        </text>
        <text name="Your email address">Your email address</text>
        <text name="Your phone number">Your phone number</text>
        <text name="Your postal address">Your postal address</text>
        <text name="AllFunctionalityYouHaveInTheApplication">All functionality you have in the application</text>
        <text name="Default (all) API">Default (all) API</text>
        <text name="Offline Access">Offline Access</text>
        <text name="Access to your applications and resources, even when you are offline">Access to your applications
            and resources, even when you are offline
        </text>
        <text name="Purchase">Purchase</text>
        <text name="Trial">Trial</text>
        <text name="IsDisabled">Is disabled</text>
        <text name="ChartDateFormat">%m/%d/%Y</text>
        <text name="ThereIsNoLoggedInUser">There is no logged in user.</text>
        <text name="IsInTrialPeriod">Is in trial period?</text>
        <text name="FailedSubscriptionTerminations_Email_Subject">Failed Subscription Terminations</text>
        <text name="FailedSubscriptionTerminations_Email_Body">Subscription termination is failed for following tenants
            {0} at {1}. You can find detailed error messages in system logs.
        </text>
        <text name="SubscriptionExpire_Email_Subject">Your RealtoCrm subscription is expired</text>
        <text name="SubscriptionExpire_Email_Body">Your RealtoCrm subscription is expired at {0}.</text>
        <text name="UnlimitedTimeSubscription">Unlimited time subscription</text>
        <text name="SubscriptionEndDateUtc">Subscription end date (Utc)</text>
        <text name="ExpiringEditionMustBeAFreeEdition">Expiring edition must be a free edition</text>
        <text name="ThisEditionIsUsedAsAnExpiringEdition">This edition is used as an expiring edition for another
            edition(s). If you want to make this edition paid, you should first remove it from other edition's.
        </text>
        <text name="UpgradedTo">Upgraded to {0} edition</text>
        <text name="ExtendedEdition">Extended {0} edition</text>
        <text name="TrialSubscriptionNotification">You are in Trial period for {0} edition, {1} to purchase.</text>
        <text name="SubscriptionExpireNotification">Your subscription will expire in {0} day(s).</text>
        <text name="TrialExpireNotification">Your trial period will expire in {0} day(s).</text>
        <text name="CanNotUpgradeSubscriptionSinceTenantHasNoEditionAssigned">Can not upgrade subscription since tenant
            has no edition assigned.
        </text>
        <text name="PaymentCouldNotCompleted">Payment couldn't complete</text>
        <text name="TenantEditionIsNotAssigned">Tenant edition is not assigned</text>
        <text name="FreeEditionsCannotHaveTrialVersions">Free editions cannot have trial versions</text>
        <text name="Total">Total</text>
        <text name="Extend_Edition_Description">Extend {0} Edition</text>
        <text name="Upgrade_Edition_Description">Upgrade to {0} Edition</text>
        <text name="PaymentMightBeExpiredWarning">Your operation might be timed out, please contact us if you already
            made the payment.
        </text>
        <text name="GoToSubscriptionManagement">Go to subscription</text>
        <text name="DemoPayPalAccount">Demo PayPal Account</text>
        <text name="IsEnabled">Is enabled</text>
        <text name="IsGoogleAuthenticatorEnabled">Enable Google Authenticator</text>
        <text name="GoogleAuthenticatorReferrerLink">Not sure what this screen means? You may need to check this</text>
        <text name="ScanQrCodeWithYourMobileApp">Scan this QR code with your mobile app</text>
        <text name="Profile">Profile</text>
        <text name="Enable">Enable</text>
        <text name="GoogleAuthenticatorIsNotEnabled">Google Authenticator is not enabled for your account. You must
            first enable it in your settings.
        </text>
        <text name="SubscriptionExpiringSoon_Email_Subject">Your RealtoCrm subscription expiration notice</text>
        <text name="SubscriptionExpiringSoon_Email_Body">Your RealtoCrm subscription will expire at {0}.</text>
        <text name="SubscriptionExpire_Title">RealtoCrm subscription expire</text>
        <text name="SubscriptionExpire_SubTitle">This email is sent you to notify about your subscription expiration
        </text>
        <text name="SubscriptionAssignedToAnother_Email_Body">Your RealtoCrm subscription is expired at {1}. Your
            current edition assigned to {0}.
        </text>
        <text name="FailedSubscriptionTerminations_Title">Failed subscription terminations</text>
        <text name="FailedSubscriptionTerminations_SubTitle">Subscription termination is failed for some tenants</text>
        <text name="SubscriptionExpiringSoon_Title">RealtoCrm subscription expiration notice</text>
        <text name="SubscriptionExpiringSoon_SubTitle">This email is sent you to notify about your subscription
            expiration.
        </text>
        <text name="AdminRoleCannotRemoveFromAdminUser">Admin role can not be removed from admin user</text>
        <text name="YouCannotRemoveUserRolePermissionsFromAdminRole">You can not remove User/Role permission editing
            from the admin role
        </text>
        <text name="YouCannotRemoveUserRolePermissionsFromAdminUser">You can not remove User/Role permission editing
            from the admin user
        </text>
        <text name="Notification">Notification</text>
        <text name="TotalRecordsCount">Total: {0}</text>
        <text name="SubscriptionInformation">Subscription information</text>
        <text name="PaymentHistory">Payment history</text>
        <text name="Gateway">Gateway</text>
        <text name="Amount">Amount</text>
        <text name="Status">Status</text>
        <text name="Period">Period</text>
        <text name="DayCount">Day count</text>
        <text name="LastModificationTime">Last modification time</text>
        <text name="PaymentId">Payment id</text>
        <text name="SubscriptionPaymentGatewayType_Paypal">Paypal</text>
        <text name="SubscriptionPaymentGatewayType_Stripe">Stripe</text>
        <text name="SubscriptionPaymentStatus_Completed">Completed</text>
        <text name="SubscriptionPaymentStatus_NotPaid">Not Paid</text>
        <text name="SubscriptionPaymentStatus_Paid">Paid</text>
        <text name="SubscriptionPaymentStatus_Failed">Failed</text>
        <text name="SubscriptionPaymentStatus_Cancelled">Cancelled</text>
        <text name="PaymentPeriodType_Annual">Annual</text>
        <text name="PaymentPeriodType_Daily">Daily</text>
        <text name="PaymentPeriodType_Monthly">Monthly</text>
        <text name="PaymentPeriodType_Weekly">Weekly</text>
        <text name="ProcessTime">Process time</text>
        <text name="SmsVerificationEnabled">Phone number verification enabled (via SMS)</text>
        <text name="SmsVerificationMessage">Your verification code: {0}</text>
        <text name="Verify">Verify</text>
        <text name="YourCode">Your Code</text>
        <text name="VerifyYourCode">Verify Your Code</text>
        <text name="YourPhoneNumberIsVerified">Your phone number is verified</text>
        <text name="YourPhoneNumberIsNotVerified">Your phone number is not verified</text>
        <text name="WrongSmsVerificationCode">Wrong verification code!</text>
        <text name="SelectUsers">Select users</text>
        <text name="OrganizationUnits">Organization Units</text>
        <text name="YouCanManageUsersOrganizationUnitsFromOrganizationUnitsPage">You can manage User's organization
            units From Organization Units page.
        </text>
        <text name="DemoUiComponents">Demo UI Components</text>
        <text name="DateAndTimePickers">Date and Time Pickers</text>
        <text name="FileUpload">File Upload</text>
        <text name="PostedValue">Posted value</text>
        <text name="SelectDate">Select date</text>
        <text name="SelectFile">Select file</text>
        <text name="UploadedFile">Uploaded file</text>
        <text name="PostedData">Posted data</text>
        <text name="Remove">Remove</text>
        <text name="OrganizationUnitNotFoundForThisUser">There is no Organization Unit For this user.</text>
        <text name="DemoUiComponents_Info">
            This page shows a few useful UI components those can be used in your application.
            Components in this page are implemented as full stack (includes the server side).
        </text>
        <text name="DemoUiComponents_Info_Metronic_Link_Text">Check Metronic theme for more UI components</text>
        <text name="Image">Image</text>
        <text name="File">File</text>
        <text name="Link">Link</text>
        <text name="Billing">Billing</text>
        <text name="LegalName">Legal Name</text>
        <text name="Address">Address</text>
        <text name="Tax/VatNo">Tax/VatNo</text>
        <text name="Invoice">Invoice</text>
        <text name="TotalAmount">Total Amount</text>
        <text name="Print">Print</text>
        <text name="ShowInvoice">Show Invoice</text>
        <text name="InvoiceInformation">Invoice Information</text>
        <text name="InvoiceNo">Invoice No</text>
        <text name="Date">Date</text>
        <text name="ThisInvoiceIsNotYours">This invoice is not yours!</text>
        <text name="UiCustomizationHeaderInfo">Change the look of UI</text>
        <text name="UiCustomization_Layout">Layout</text>
        <text name="UiCustomization_LayoutType">Layout type</text>
        <text name="UiCustomization_Fluid">Fluid</text>
        <text name="UiCustomization_Boxed">Boxed</text>
        <text name="UiCustomization_Disabled">Disabled</text>
        <text name="UiCustomization_Default">Default</text>
        <text name="UiCustomization_SpinnerMessage">Spinner message</text>
        <text name="UiCustomization_ContentSkin">Content skin</text>
        <text name="UiCustomization_LightGrey">Light grey</text>
        <text name="UiCustomization_White">White</text>
        <text name="UiCustomization_DesktopFixedHeader">Fixed header</text>
        <text name="UiCustomization_None">None</text>
        <text name="UiCustomization_Hide">Hide</text>
        <text name="UiCustomization_MobileFixedHeader">Fixed header</text>
        <text name="UiCustomization_DisplayHeaderMenu">Display header menu</text>
        <text name="UiCustomization_Light">Light</text>
        <text name="UiCustomization_Dark">Dark</text>
        <text name="UiCustomization_DropdownSkin">Dropdown skin</text>
        <text name="UiCustomization_Menu">Menu</text>
        <text name="UiCustomization_MenuPosition">Position</text>
        <text name="UiCustomization_Left">Left</text>
        <text name="UiCustomization_Top">Top</text>
        <text name="UiCustomization_MenuSkin">Menu skin</text>
        <text name="UiCustomization_FixedMenu">Fixed menu</text>
        <text name="UiCustomization_AllowAsideMinimizing">Allow aside minimizing</text>
        <text name="UiCustomization_DefaultMinimizedAside">Default minimized aside</text>
        <text name="UiCustomization_AllowAsideHiding">Allow aside hiding</text>
        <text name="UiCustomization_DropdownSubmenuSkin">Dropdown submenu skin</text>
        <text name="UiCustomization_Inherit">Inherit</text>
        <text name="UiCustomization_Footer">Footer</text>
        <text name="UiCustomization_FixedFooter">Fixed footer</text>
        <text name="UiCustomization_PushFooter">Push footer</text>
        <text name="UiCustomization_Header">Header bar</text>
        <text name="UiCustomization_Theme">Theme</text>
        <text name="VisualSettings">Visual Settings</text>
        <text name="SaveAsSystemDefault">Save as system default</text>
        <text name="UseSystemDefaults">Use system defaults</text>
        <text name="NewNotifications">New notifications</text>
        <text name="Subscriptions">Subscriptions</text>
        <text name="UiCustomization_DefaultHiddenAside">Default hidden aside</text>
        <text name="LoadWithThreeDot">Loading...</text>
        <text name="NoInternetConnection">You are currently not connected to internet</text>
        <text name="Warning">Warning</text>
        <text name="RequestTimedOut">Your request timed out, please try again later.</text>
        <text name="AnErrorOccurred">An error occurred, please try again later.</text>
        <text name="AnHttpErrorOccured">There was an error communicating with the server.</text>
        <text name="ChangePasswordToLogin">You must change your password before logging on!</text>
        <text name="NoPermission">You do not have any permission. Contact your administrator.</text>
        <text name="NoCamera">No available camera found!</text>
        <text name="PickFromGallery">Pick from gallery</text>
        <text name="TakePhoto">Take a photo</text>
        <text name="Rotate">Rotate</text>
        <text name="PasswordsDontMatch">Passwords do not match!</text>
        <text name="RequiredField">{0} field is required.</text>
        <text name="StringLengthDoesntMatch">The field {0} must be a minimum length of {1} and a maximum length of
            {2}.
        </text>
        <text name="LessThanMinStringLength">The field {0} must be a minimum length of {1}.</text>
        <text name="InvalidPhoneNumber">The field {0} is not a valid phone number</text>
        <text name="InvalidRegularExpression">The field {0} is not valid.</text>
        <text name="LengthNotInRange">The length of field {0} must be between {1} and {2}.</text>
        <text name="UiCustomization_Mobile">Mobile</text>
        <text name="UiCustomization_Desktop">Desktop</text>
        <text name="TwoFactorLoginIsNotSupported">Two factor login is not currently supported!</text>
        <text name="LinkToCurrentPage">Link to current page</text>
        <text name="ThisFieldIsRequired">This field is required.</text>
        <text name="PleaseEnterAtLeastNCharacter">Please enter at least {0} characters.</text>
        <text name="PleaseEnterNoMoreThanNCharacter">Please enter no more than {0} characters.</text>
        <text name="SelectTwoFactorProvider">Select two factor authentication provider</text>
        <text name="InvoiceInfoIsMissingOrNotCompleted">In order to create an invoice, you must define the invoice
            information ('Legal Name', 'Address' and 'Tax/VATNo') under Settings->Invoice.
        </text>
        <text name="InvoiceTo">Invoice to</text>
        <text name="OperationLogs">Operation logs</text>
        <text name="ChangeLogs">Change logs</text>
        <text name="Object">Object</text>
        <text name="Property">Property</text>
        <text name="OriginalValue">Original value</text>
        <text name="NewValue">New value</text>
        <text name="Detail">Detail</text>
        <text name="CreatedAtByUser"><![CDATA[Created at <b>{0}</b> by <b>{1}</b>]]></text>
        <text name="RealtoCrm.MultiTenancy.Tenant">Tenant</text>
        <text name="Abp.Organizations.OrganizationUnit">Organization unit</text>
        <text name="Search">Search</text>
        <text name="Pin">Pin</text>
        <text name="TopMenu">Top menu</text>
        <text name="LeftMenu">Left menu</text>
        <text name="UserAccounts">User accounts</text>
        <text name="Blue">Blue</text>
        <text name="Green">Green</text>
        <text name="Pink">Pink</text>
        <text name="Yellow">Yellow</text>
        <text name="UiCustomization_HeaderSkin">Skin</text>
        <text name="TenantsCannotCreateLanguage">Tenants cannot create language.</text>
        <text name="Toggle">Toggle</text>
        <text name="UserAccount">User account</text>
        <text name="Expand">Expand</text>
        <text name="Attach">Attach</text>
        <text name="Collapse">Collapse</text>
        <text name="AllOfThesePermissionsMustBeGranted">Required permissions are not granted. All of these permissions
            must be granted: {0}
        </text>
        <text name="AtLeastOneOfThesePermissionsMustBeGranted">Required permissions are not granted. At least one of
            these permissions must be granted: {0}
        </text>
        <text name="OnlyLockedUsers">Only locked users</text>
        <text name="DefaultFromSenderEmailAddress">Default from (sender) email address</text>
        <text name="DefaultFromSenderDisplayName">Default from (sender) display name</text>
        <text name="DefaultLanguage">Default language</text>
        <text name="CurrentUserDidNotLoginToTheApplication">Current user did not login to the application!</text>
        <text name="MainMenu">Main menu</text>
        <text name="IncorrectImageFormat">Incorrect image format!</text>
        <text name="NotificationDeleteWarningMessage">This notification will be deleted</text>
        <text name="ProcessingWithThreeDot">Processing...</text>
        <text name="ThereIsNoSuchImageFileWithGivenToken">There is no image with given token</text>
        <text name="YouAreNotLinkedToAnyAccount">You are not linked to any account</text>
        <text name="DownloadCollectedData">Download collected data</text>
        <text name="GdprDataPrepareStartedNotification">We are preparing your data. You will be notified when your data
            is ready.
        </text>
        <text name="GdprDataPreparedNotificationMessage">Your data is prepared, click here to download.</text>
        <text name="ChatMessage_From">From</text>
        <text name="ChatMessage_To">To</text>
        <text name="ReadState">Read state</text>
        <text name="You">You</text>
        <text name="Anonymous">Anonymous</text>
        <text name="Messages">Messages</text>
        <text name="ThisNotificationDoesntBelongToYou">This notification doesn't belong to you.</text>
        <text name="CookieConsent_Message">This website uses cookies to ensure you get the best experience on our
            website.
        </text>
        <text name="CookieConsent_Dismiss">Got it!</text>
        <text name="UiCustomization_Wide">Wide</text>
        <text name="RealtoCrm.Authorization.Roles.Role">Role</text>
        <text name="History">History</text>
        <text name="IsCookieConsentEnabled">Cookie consent enabled</text>
        <text name="CookieConsent">Cookie consent</text>
        <text name="AutomaticallyBillMyAccount">Enable recurring payments</text>
        <text name="CheckoutWithStripe">Checkout with Stripe</text>
        <text name="CheckoutWithPaypal">Checkout with PayPal</text>
        <text name="DisableRecurringPayments">Don't bill my account automatically</text>
        <text name="EnableRecurringPayments">Bill my account automatically</text>
        <text name="ExpiringEdition">Expiring edition</text>
        <text name="Price">Price</text>
        <text name="ThereAreTenantsSubscribedToThisEdition">There are tenants subscribed to this edition. Please assign
            a different edition to them and then delete this edition.
        </text>
        <text name="MoveTenantsToAnotherEdition">Move tenants to another edition</text>
        <text name="TenantsMovedToEditionNotificationMessage">All tenants of {sourceEditionName} edition moved to
            {targetEditionName} edition.
        </text>
        <text name="MoveTenantsOfEdition">Move Tenants to Another Edition</text>
        <text name="MoveTenantsOfEditionDescription">{0} tenants will be moved</text>
        <text name="MoveTenantsToAnotherEditionStartedNotification">We are moving tenants to selected edition. You will
            be notified when the operation is complete.
        </text>
        <text name="BuyNow_Edition_Description">{0} Edition</text>
        <text name="NewRegistration_Edition_Description">{0} Edition</text>
        <text name="Item">Item</text>
        <text name="RecurringSubscriptionUpgradeNote">The price difference between your current edition and the one you
            are upgrading will be charged from your account.
        </text>
        <text name="Theme_Default">Default</text>
        <text name="Theme_Theme1">Theme 1</text>
        <text name="Theme_Theme2">Theme 2</text>
        <text name="Theme_Theme3">Theme 3</text>
        <text name="Theme_Theme4">Theme 4</text>
        <text name="Theme_Theme5">Theme 5</text>
        <text name="Theme_Theme6">Theme 6</text>
        <text name="Theme_Theme7">Theme 7</text>
        <text name="Theme_Theme8">Theme 8</text>
        <text name="Theme_Theme9">Theme 9</text>
        <text name="Theme_Theme10">Theme 10</text>
        <text name="Theme_Theme11">Theme 11</text>
        <text name="Theme_Theme12">Theme 12</text>
        <text name="SelectATheme">Select a Theme</text>
        <text name="QuickThemeSelection">Quick theme selection</text>
        <text name="IsQuickThemeSelectEnabled">Is quick theme select enabled</text>
        <text name="EmailMessage_CopyTheLinkBelowToYourBrowser">If the button above doesn't work, paste this into your
            browser:
        </text>
        <text name="AddRole">Add role</text>
        <text name="SelectRoles">Select roles</text>
        <text name="SelectARole">Select a role</text>
        <text name="SelectAnOrganizationUnitToSeeRoles">Select an organization unit to see roles</text>
        <text name="RemoveRoleFromOuWarningMessage">Are you sure to remove role {0} from organization unit {1}?</text>
        <text name="ManagingRoles">Managing roles</text>
        <text name="UiCustomization_LayoutFixedContent">Fixed content (Desktop)</text>
        <text name="UiCustomization_LayoutMobileFixedContent">Fixed content (Mobile)</text>
        <text name="ExcelOperations">Excel operations</text>
        <text name="ImportFromExcel">Import from excel</text>
        <text name="ExportToExcel">Export to excel</text>
        <text name="ImportToExcelSampleFileDownloadInfo">{0} to download sample import file.</text>
        <text name="ImportOffersProcessStart">Offer import process has begun.</text>
        <text name="ImportOffersUploadFailed">Offer import process has failed.</text>
        <text name="ImportUsersProcessStart">User import process has begun.</text>
        <text name="ImportUsersUploadFailed">User import process has failed.</text>
        <text name="AllUsersSuccessfullyImportedFromExcel">User import process has been completed successfully. All
            users in file are imported.
        </text>
        <text name="AllОffersSuccessfullyImportedFromExcel">Offer import process has been completed successfully. All
            offers in file are imported.
        </text>
        <text name="FileCantBeConvertedToUserList">User import process has failed. File is invalid.</text>
        <text name="FileCantBeConvertedToOfferList">Offer import process has failed. File is invalid.</text>
        <text name="{0}IsInvalid">"{0}" is invalid</text>
        <text name="Exception">Exception</text>
        <text name="InvalidUserImports">Invalid user imports</text>
        <text name="ClickToSeeInvalidUsers">User import process has been completed. There are some invalid data. Click
            to see Invalid user imports.
        </text>
        <text name="ClickToSeeInvalidOffers">Offer import process has been completed. There are some invalid data. Click
            to see Invalid offer imports.
        </text>
        <text name="YouAreAlreadyLoggedInWithUser">You are already logged in with user</text>
        <text name="RequiresPasswordChange">Requires password change</text>
        <text name="RequiresTwoFactorAuth">Requires two factor authentication</text>
        <text name="Online">Online</text>
        <text name="Offline">Offline</text>
        <text name="Reply">Reply</text>
        <text name="UiCustomization_FixedSubHeader">Fixed Subheader</text>
        <text name="UiCustomization_SubHeaderStyle">Subheader style</text>
        <text name="UiCustomization_SubHeaderStyle_Solid">Solid</text>
        <text name="UiCustomization_SubHeaderStyle_Transparent">Transparent</text>
        <text name="UiCustomization_SubmenuToggle">Submenu toggle</text>
        <text name="UiCustomization_SubmenuToggle_Accordion">Accordion</text>
        <text name="UiCustomization_SubmenuToggle_Dropdown">Dropdown</text>
        <text name="UiCustomization_SubHeader">Subheader</text>
        <text name="UiCustomization_Fixed">Fixed</text>
        <text name="UiCustomization_HeaderMinimizeType">Header minimize type</text>
        <text name="UiCustomization_HeaderMinimizeType_All">Topbar and Menu</text>
        <text name="UiCustomization_HeaderMinimizeType_Topbar">Topbar</text>
        <text name="UiCustomization_HeaderMinimizeType_Menu">Menu</text>
        <text name="UiCustomization_HeaderMenuArrows">Menu arrows</text>
        <text name="InvalidPattern">Invalid</text>
        <text name="SwitchToTenant">Switch to tenant</text>
        <text name="TenancyNameRequired">Tenancy name is required!</text>
        <text name="EnterTenancyName">Enter tenancy name...</text>
        <text name="SwitchToTheTenant">Switch to the tenant</text>
        <text name="SwitchToTheHost">Switch to the host</text>
        <text name="DeleteListedNotifications">Delete Listed Notifications</text>
        <text name="DeleteListedNotificationsWarningMessage">All listed notifications will be deleted.</text>
        <text name="YourSessionIsAboutToExpire">Your Session is About to Expire</text>
        <text name="SessionExpireRedirectingInXSecond">Your session will expire because you have been inactive for a
            long time. Provide any mouse/keyboard input on the site to stop session termination. Redirecting in {0}sec.
        </text>
        <text name="SessionTimeOut">Session Timeout Control</text>
        <text name="IsSessionTimeOutEnabled">Session Time Out Control Enabled</text>
        <text name="SessionTimeOutSecond">Timeout Second.</text>
        <text name="ShowTimeOutNotificationSecond">Countdown Modal Wait Time (Seconds)</text>
        <text name="OneConcurrentLoginPerUser">Only one concurrent login per user</text>
        <text name="OneConcurrentLoginPerUserActive">Disable concurrent login for a user. If a user logins with a second
            device, the first session is automatically closed.
        </text>
        <text name="Disable">Disable</text>
        <text name="UseCaptchaOnLogin">Use security image question (captcha) on login.</text>
        <text name="XCountPermissionFiltered">{0} permission filtered.</text>
        <text name="SelectPermissions">Select Permissions</text>
        <text name="DailyPrice">Daily Price</text>
        <text name="WeeklyPrice">Weekly Price</text>
        <text name="PerDay">Per day</text>
        <text name="PerWeek">Per week</text>
        <text name="ThereIsNoEditionToUpgrade">There is no edition to upgrade</text>
        <text name="RoleIsInheritedFromOrganizationUnit">This role is inherited from one of users organizations</text>
        <text name="AddWidget">Add widget</text>
        <text name="AddPage">Add page</text>
        <text name="PageName">Page name</text>
        <text name="NewPage">New page</text>
        <text name="DeletePage">Delete page</text>
        <text name="RenamePage">Rename page</text>
        <text name="NewPageName">Name of new page</text>
        <text name="PageNewName">New name of page</text>
        <text name="EditMode">Edit mode</text>
        <text name="PageDeleteWarningMessage">Are you sure to remove "{0}" page?</text>
        <text name="WidgetDeleteWarningMessage">Are you sure to remove "{0}" widget from "{1}" page?</text>
        <text name="NoWidgetAvailableMessage">No widget available!</text>
        <text name="ThereIsNoViewConfigurationForX">There is no view configuration for {0}</text>
        <text name="UnknownDashboard">Unknown Dashboard: {0}.</text>
        <text name="FilterDateRangePicker">Date Range</text>
        <text name="WidgetDailySales">Daily Sales</text>
        <text name="WidgetGeneralStats">General Stats</text>
        <text name="WidgetProfitShare">Profit Share</text>
        <text name="WidgetMemberActivity">Member Activity</text>
        <text name="WidgetRegionalStats">Regional Stats</text>
        <text name="WidgetSalesSummary">Sales Summary</text>
        <text name="WidgetTopStats">General Summary</text>
        <text name="WidgetIncomeStatistics">Income Statistics</text>
        <text name="WidgetEditionStatistics">Edition Statistics</text>
        <text name="WidgetSubscriptionExpiringTenants">Subscription Expiring Tenants</text>
        <text name="WidgetRecentTenants">Recent Tenants</text>
        <text name="BackToDefaultPage">Back To Default</text>
        <text name="BackToDefaultPageWarningMessage">Are you sure to remove "{0}" page and back to default?</text>
        <text name="DashboardFilters">Dashboard Filters</text>
        <text name="PageNameCanNotBeEmpty">Page name cannot be empty.</text>
        <text name="ShowMenuSearchInput">Show search bar in the menu</text>
        <text name="veryWeak">Very Weak</text>
        <text name="weak">Weak</text>
        <text name="normal">Normal</text>
        <text name="medium">Medium</text>
        <text name="strong">Strong</text>
        <text name="veryStrong">Very Strong</text>
        <text name="QuickNav">Quick Navigation</text>
        <text name="AutomaticBilling">Automatic Billing</text>
        <text name="ThereIsNoStripeSessionIdOnPayment">Stripe session information for the payment transaction could not
            be found.
        </text>
        <text name="ReceivingPaymentResult">Receiving payment result.</text>
        <text name="ThankYou">Thank You</text>
        <text name="PleaseWait">Please Wait</text>
        <text name="YourPaymentHasBeenCompleted">Your payment has been completed.</text>
        <text name="ExternalPaymentId">External Payment Id</text>
        <text name="CostOfProration">Price difference during the period</text>
        <text name="WebhookEvent">Webhook Event</text>
        <text name="Webhooks">Webhooks</text>
        <text name="WebhookSubscriptions">Webhook Subscriptions</text>
        <text name="WebhookSubscriptionsInfo">Webhook Subscription Info</text>
        <text name="AddNewWebhookSubscription">Add New Webhook Subscription</text>
        <text name="EditWebhookSubscription">Edit Webhook Subscription</text>
        <text name="WebhookEvents">Webhook Events</text>
        <text name="IsActive">Is Active</text>
        <text name="WebhookEventDetail">Webhook Event Detail</text>
        <text name="Data">Data</text>
        <text name="WebhookSendAttempts">Webhook Send Attempts</text>
        <text name="HttpStatusCode">Http Status Code</text>
        <text name="Response">Response</text>
        <text name="Disabled">Disabled</text>
        <text name="WebhookEndpoint">Webhook Endpoint</text>
        <text name="AdditionalWebhookHeaders">Additional Webhook Headers</text>
        <text name="WebhookSecret">Webhook Secret</text>
        <text name="ViewWebhookSecret">View Webhook Secret</text>
        <text name="HeaderKey">Header Key</text>
        <text name="HeaderValue">Header Value</text>
        <text name="WebhookEventId">Webhook Event Id</text>
        <text name="WebhookEventWillBeSendWithSameParameters">Webhook event will be send with same parameters</text>
        <text name="WebhookSendAttemptInQueue">Webhook send attempt processing queued. The process will be carried out
            as soon as possible.
        </text>
        <text name="DeactivateSubscriptionWarningMessage">Webhook subscription will be deactivated and your endpoint
            will no longer get webhooks
        </text>
        <text name="ActivateSubscriptionWarningMessage">Webhook subscription will be activated and your endpoint will
            get related webhooks
        </text>
        <text name="SuccessfullyDeactivated">Successfully Deactivated</text>
        <text name="SuccessfullyActivated">Successfully Activated</text>
        <text name="Deactivate">Deactivate</text>
        <text name="Activate">Activate</text>
        <text name="HeaderKeyAndValueCanNotBeNull">Header key and value can not be null</text>
        <text name="HeaderKeysMustBeUnique">Header keys must be unique</text>
        <text name="ViewWebhookEvent">View Webhook Event</text>
        <text name="GoToSubscription">Go To Subscription</text>
        <text name="WebhookSubscriptionDetail">Webhook Subscription Detail</text>
        <text name="Resend">Resend</text>
        <text name="YouHaveToSubscribeToTestWebhookToReceiveTestEvent">You have to subscribe to test webhook to receive
            test event
        </text>
        <text name="CreatingWebhooks">Creating new webhook</text>
        <text name="EditingWebhooks">Editing webhook</text>
        <text name="ChangingWebhookActivity">Activate/Deactivate webhook</text>
        <text name="DetailingSubscription">See webhook detail page</text>
        <text name="ListingSendAttempts">Listing webhook send attempt history</text>
        <text name="ResendingWebhook">Resending a webhook</text>
        <text name="Add">Add</text>
        <text name="ShowResponse">Show Response</text>
        <text name="ShowData">Show Data</text>
        <text name="GoToLoginPage">Go to login page</text>
        <text name="ShowLockScreenWhenTimedOut">Show Lock Screen When Timed Out</text>
        <text name="YourSessionIsLocked">Your Session is Locked</text>
        <text name="WebhookSubscriptionId">Webhook Subscription Id</text>
        <text name="ParameterName">Parameter Name</text>
        <text name="InputType">Input Type</text>
        <text name="Values">Values</text>
        <text name="Value">Value</text>
        <text name="SelectAPermission">Select A Permission</text>
        <text name="EntityFullName">Entity Full Name</text>
        <text name="SelectAnEntity">Select An Entity</text>
        <text name="EntityRowId">Entity Row Id</text>
        <text name="DynamicEntityParameters">Dynamic Entity Parameters</text>
        <text name="XCanNotBeNullOrEmpty">{0} Can not be null or empty</text>
        <text name="DeleteDynamicPropertyMessage">Dynamic parameter and its relationships will be deleted completely.
            (This cannot be undo!)
        </text>
        <text name="DeleteDynamicPropertyValueMessage">Dynamic parameter value and its relationships will be deleted
            completely. (This cannot be undo!)
        </text>
        <text name="DynamicEntityPropertyValues">Dynamic Entity Property Values</text>
        <text name="CreatingDynamicProperties">Creating Dynamic Properties</text>
        <text name="EditingDynamicProperties">Editing Dynamic Properties</text>
        <text name="DeletingDynamicProperties">Deleting Dynamic Properties</text>
        <text name="DynamicPropertyValue">Dynamic Property Value</text>
        <text name="CreatingDynamicPropertyValue">Creating Dynamic Property Value</text>
        <text name="EditingDynamicPropertyValue">Editing Dynamic Property Value</text>
        <text name="DeletingDynamicPropertyValue">Deleting Dynamic Property Value</text>
        <text name="CreatingDynamicEntityProperties">Creating Entity Dynamic Properties</text>
        <text name="EditingDynamicEntityProperties">Editing Entity Dynamic Properties</text>
        <text name="DeletingDynamicEntityProperties">Deleting Entity Dynamic Properties</text>
        <text name="EntityDynamicPropertyValue">Entity Dynamic Property Value</text>
        <text name="CreatingDynamicEntityPropertyValue">Creating Entity Dynamic Property Value</text>
        <text name="EditingDynamicEntityPropertyValue">Editing Entity Dynamic Property Value</text>
        <text name="DeletingDynamicEntityPropertyValue">Deleting Entity Dynamic Property Value</text>
        <text name="UnknownEntityType">Unknown Entity Type {0}</text>
        <text name="TrialWithoutEndDateErrorMessage">Trial accounts must have subscription end date</text>
        <text name="ManageUserDelegations">Manage authority delegations</text>
        <text name="UserDelegations">User Delegations</text>
        <text name="DelegateNewUser">Delegate New User</text>
        <text name="StartTime">Start Time</text>
        <text name="EndTime">End Time</text>
        <text name="UserDelegationDeleteWarningMessage">Delegation to user {0} will be deleted.</text>
        <text name="SwitchToDelegatedUserWarningMessage">Are you sure to switch to user {0} ?</text>
        <text name="SwitchToUser">Switch to user</text>
        <text name="ExpiresAt">Expires at {0}</text>
        <text name="ThereIsNoActiveUserDelegationBetweenYourUserAndCurrentUser">There is no active user delegation
            between your user and current user !
        </text>
        <text name="Parameter">Parameter</text>
        <text name="Entity">Entity</text>
        <text name="Definitions">Definitions</text>
        <text name="EntityId">Entity Id</text>
        <text name="SelfUserDelegationErrorMessage">You can't delegate authorization to yourself !</text>
        <text name="Severity">Severity</text>
        <text name="Fatal">Fatal</text>
        <text name="Info">Info</text>
        <text name="AppId">App Id</text>
        <text name="AppSecret">App Secret</text>
        <text name="ClientId">Client Id</text>
        <text name="ClientSecret">Client Secret</text>
        <text name="UserInfoEndpoint">User Info Endpoint</text>
        <text name="ConsumerKey">Consumer Key</text>
        <text name="ConsumerSecret">Consumer Secret</text>
        <text name="Show">Show</text>
        <text name="ExternalLoginSettings">External Login Settings</text>
        <text name="UseHostSettings">Use Host Settings</text>
        <text name="Hide">Hide</text>
        <text name="OpenIdClaimsCleanListToRevertToDefaultClaimsMessage">Clean list to revert to default claims</text>
        <text name="ClaimsMapping">Claims Mapping</text>
        <text name="ValidateIssuer">Validate Issuer</text>
        <text name="Authority">Authority</text>
        <text name="ClaimKey">Claim Key</text>
        <text name="ClaimValue">Claim Value</text>
        <text name="SelfTenantRegistrationIsDisabledMessage_Detail">Self tenant registration is disabled. Please contact
            the system administrator to register.
        </text>
        <text name="Tenant">Tenant</text>
        <text name="MetaDataAddress">Meta data address</text>
        <text name="Wtrealm">Wtrealm</text>
        <text name="AllowUsingGravatarProfilePicture">Allow users to use Gravatar profile picture</text>
        <text name="UseGravatarProfilePicture">Use Gravatar profile picture</text>
        <text name="PleaseSelectAPicture">Please select a picture!</text>
        <text name="BackToMyAccount_Description">Go back to your own account</text>
        <text name="ManageLinkedAccounts_Description">Manage accounts linked to your account</text>
        <text name="ManageUserDelegations_Description">Manage authorized accounts</text>
        <text name="ChangePassword_Description">Change your account's password</text>
        <text name="LoginAttempts_Description">See recent login attempts for your account</text>
        <text name="ChangeProfilePicture_Description">Change profile picture of your account</text>
        <text name="MySettings_Description">Change your account settings</text>
        <text name="VisualSettings_Description">Change look of the application</text>
        <text name="DownloadCollectedData_Description">Download data belongs to your account</text>
        <text name="New">New</text>
        <text name="NotAMemberYet">Not a member yet ?</text>
        <text name="DynamicEntityProperties">Dynamic Entity Properties</text>
        <text name="DynamicProperty">Dynamic Property</text>
        <text name="DynamicProperties">Dynamic Properties</text>
        <text name="AddNewDynamicProperty">Add New Dynamic Property</text>
        <text name="AddNewDynamicEntityProperty">Add New Dynamic Entity Property</text>
        <text name="EditDynamicProperty">Edit Dynamic Property</text>
        <text name="DynamicPropertyManagement">Dynamic Property Management</text>
        <text name="SelectADynamicProperty">Select a Dynamic Property</text>
        <text name="PropertyName">Property Name</text>
        <text name="AddNewDynamicPropertyValue">Add New Dynamic Property Value</text>
        <text name="EditDynamicPropertyValue">Edit Dynamic Property Value</text>
        <text name="EditValues">Edit Values</text>
        <text name="FullName">Full Name</text>
        <text name="FirstName">First Name</text>
        <text name="UiCustomization_HoverableAside">Expand menu when hovered</text>
        <text name="SMTPSettingsNotProvidedWarningText">SMTP settings not provided, Please provided SMTP settings.
        </text>
        <text name="SocialLoginDeactivate_Description">This external login option will be disabled for your tenant.
        </text>
        <text name="DisplayName">Display Name</text>
        <text name="AssignDynamicPropertiesToEntity">Assign Dynamic Properties To Entity</text>
        <text name="SendEmailWithSavedSettingsWarning">You haven't saved the settings, do you want to send the test mail
            with the previously saved settings?
        </text>
        <text name="DoYouWantToRemoveTheFile">Do you want to remove the file?</text>
        <text name="CurrentFile">Current File</text>
        <text name="Dashboard_NoPageDefined_Warning">There is no page defined for this dashboard. Please switch to edit
            mode and add a page!
        </text>
        <text name="Filter">Filter</text>
        <text name="LoginAttemptsHeaderInfo">List and filter login attempts</text>
        <text name="AbpLoginResultType_Success">Success</text>
        <text name="AbpLoginResultType_InvalidUserNameOrEmailAddress">Invalid username or email address</text>
        <text name="AbpLoginResultType_InvalidPassword">Invalid password</text>
        <text name="AbpLoginResultType_UserIsNotActive">User is not active</text>
        <text name="AbpLoginResultType_InvalidTenancyName">Invalid tenancy name</text>
        <text name="AbpLoginResultType_TenantIsNotActive">Tenant is not active</text>
        <text name="AbpLoginResultType_UserEmailIsNotConfirmed">Email is not confirmed</text>
        <text name="AbpLoginResultType_UnknownExternalLogin">Unknown external login</text>
        <text name="AbpLoginResultType_LockedOut">Locked out</text>
        <text name="AbpLoginResultType_UserPhoneNumberIsNotConfirmed">Phone number is not confirmed</text>
        <text name="Result">Result</text>
        <text name="ChangeDefaultLanguage">Change default language</text>
        <text name="ActivationMailSentIfEmailAssociatedMessage">If the email address is associated to a known user
            account then an activation link will be sent to you
        </text>
        <text name="ChooseAFile">Choose A File</text>
        <text name="YouAreNotLoggedInAsAHostUser">You are not logged in as a host user!</text>
        <text name="WhatsIncludedInYourPlan">What's included in your plan</text>
        <text name="NoFeaturesInYourPlan">There is no feature in your plan!</text>
        <text name="YourPlan">Your Plan</text>
        <text name="WebhookPageInfo">Webhooks allow external services to be notified when certain events happen. When
            the specified events happen, we’ll send a POST request to each of the URLs you provide.
        </text>
        <text name="UiCustomization_DarkMode">Dark Mode</text>
        <text name="Theme_Theme13">Theme 13</text>
        <text name="EnableCheckingLastXPasswordWhenPasswordChangeSettingLabel">Prevented user's new password from being
            the same as any of the last x passwords
        </text>
        <text name="CheckingLastXPasswordCountSettingLabel">The user's new password must different from how many last
            passwords?
        </text>
        <text name="NewPasswordMustBeDifferentThenLastXPassword">New password must be different then last {0} password
        </text>
        <text name="EnablePasswordExpirationSettingLabel">Enable Password Expiration</text>
        <text name="PasswordExpirationDayCountSettingLabel">Password expiration day count</text>
        <text name="PasswordResetCodeExpirationHoursSettingLabel">Password reset code expiration hour count</text>
        <text name="WidgetNotFound">Widget Not Found</text>
        <text name="WidgetCanNotBePlacedMoreThanOnceInAPageWarning">You can not place selected widget more than once in
            a page.
        </text>
        <text name="SubscriptionPaymentNotCompleted_Title">RealtoCrm uncompleted payment notice</text>
        <text name="SubscriptionPaymentNotCompleted_SubTitle">Complete your payment</text>
        <text name="SubscriptionPaymentNotCompleted_Email_Body">We are writing to inform you that, you have created a
            tenant but didn't complete its payment. You can complete the payment by following the URL {0} and finish the
            registration process.
        </text>
        <text name="SubscriptionPaymentNotCompleted_Email_Subject">Your RealtoCrm subscription uncompleted payment
            notice
        </text>
        <text name="UpdateUsersProfilePicture">Update users' profile picture</text>
        <text name="AdminName">Admin User's Name</text>
        <text name="AdminSurname">Admin User's Surname</text>
        <text name="UnknownWidgetId">Unknown WidgetId: {0}</text>
        <text name="AllowedUserNameCharactersInfoText">Allowed characters: {0}</text>
        <text name="NotificationName">Notification Name</text>
        <text name="IsPublished">Is Published</text>
        <text name="CreateNewMassNotification">Create New Mass Notification</text>
        <text name="Enum_NotificationSeverity_0">Info</text>
        <text name="Enum_NotificationSeverity_1">Success</text>
        <text name="Enum_NotificationSeverity_2">Warn</text>
        <text name="Enum_NotificationSeverity_3">Error</text>
        <text name="Enum_NotificationSeverity_4">Fatal</text>
        <text name="TargetNotifiers">Target Notifiers</text>
        <text name="SearchUser">Search User</text>
        <text name="SearchOrganizationUnit">Search Organization Unit</text>
        <text name="User">User</text>
        <text name="OrganizationUnit">Organization Unit</text>
        <text name="RealtoCrm.Notifications.SmsRealTimeNotifier">SMS</text>
        <text name="RealtoCrm.Notifications.EmailRealTimeNotifier">Email</text>
        <text name="Abp.AspNetCore.SignalR.Notifications.SignalRRealTimeNotifier">In-App Notification</text>
        <text name="SendMassNotification">Send Mass Notification</text>
        <text name="SendMassNotificationWarningMessage">Notification will be send (This action cannot be undone).</text>
        <text name="Inbox">Inbox</text>
        <text name="MassNotifications">Mass Notifications</text>
        <text name="MassNotificationCreate">Send Mass Notification</text>
        <text name="ItemsSelected">Items Selected</text>
        <text name="ResponseType">Response Type</text>
        <text name="ResponseType_Description">You can use code, id_token, token or combination of those values.</text>
        <text name="ShowFullData">Show Full Data</text>
        <text name="ShowHTMLData">Show HTML Data</text>
        <text name="NotificationTargetNotifierSmsLengthInfo">Max sms length is 160 characters. Texts with more than 160
            characters will be divided into 160 characters and sent via multiple sms.
        </text>
        <text name="MassNotificationNoUsersFoundInOrganizationUnitMessage">There is no user in the selected organization
            units. You should at least select one user or organization unit contains user to send mass notification.
        </text>
        <text name="MassNotificationUserOrOrganizationUnitFieldIsRequiredMessage">Please select a user or organization
            unit to send a mass notification!
        </text>
        <text name="MassNotificationMessageFieldIsRequiredMessage">Please write a message!</text>
        <text name="SendNewVersionNotification">Send New Version Notification</text>
        <text name="SendNewVersionNotificationWarningMessage">New version notification will be send. (This action cannot
            be undone)
        </text>
        <text name="NewVersionAvailableNotification">Would you like to update your app to the latest version?</text>
        <text name="NewVersionAvailableNotificationMessage">A new version of the application has been released. Please
            update your app to stay up to date!
        </text>
        <text name="SuccessfullySentNewVersionNotification">The message has been successfully sent to all users.</text>
        <text name="AllowAsideMinimizing_HoverableAside_Warning">HoverableAside must be enabled when
            AllowAsideMinimizing is enabled !
        </text>
        <text name="ValueMustBeBiggerThanOrEqualToX">Value must be bigger than {0}!</text>
        <text name="InvalidVerificationCode">Invalid verification code</text>
        <text name="YourAuthenticatorCode">Your authenticator code</text>
        <text name="AuthenticatorAppEnabled">Authenticator App Enabled</text>
        <text name="AuthenticatorAppEnabledDescription">Two-factor authenticators (2FA App for short) are a good way to
            add an extra layer of security to your account to make sure that only you have to ability to log in.
        </text>
        <text name="ViewRecoveryCodes">View Recovery Codes</text>
        <text name="DisableAuthenticatorApp">Disable</text>
        <text name="EnableAuthenticatorApp">Enable Authenticator App</text>
        <text name="EnableAuthenticatorAppDescription">Two-factor authentication adds an extra layer of security to your
            account. To log in, in addition you'll need to provide a 6 digit code
        </text>
        <text name="YourCodeOrBackupCode">Your authenticator code or recovery code</text>
        <text name="TwoFactorAuthentication">Two Factor Authentication</text>
        <text name="AuthenticatorAppTitle">Authentication verification</text>
        <text name="SaveRecoveryCodesTitle">Save your security codes</text>
        <text name="AuthenticatorAppScan">Enter the code from your authenticator app</text>
        <text name="AuthenticatorAppScanHelp">After you have scanned the QR code, the app will generate a six-digit code
            that you can enter below.
        </text>
        <text name="AuthenticatorAppEnabledHelp">The next time you log in, you will be asked to enter a code from your
            authenticator app.
        </text>
        <text name="Continue">Continue</text>
        <text name="Copy">Copy</text>
        <text name="Done">Done</text>
        <text name="TwoFactorAuthenticationEnabled">Two-factor authentication is enabled</text>
        <text name="TwoFactorAuthenticationDisabled">Two-factor authentication is disabled</text>
        <text name="UiCustomization_Toolbar_DesktopFixedHeader">Desktop fixed header</text>
        <text name="UiCustomization_Toolbar_MobileFixedHeader">Mobile fixed header</text>
        <text name="UiCustomization_Toolbar">Toolbar</text>
        <text name="IncorrectImageDimensions">Image size exceeds allowed limits</text>
        <text name="YourEmailIsChangedMessage">Your email is changed successfully</text>
        <text name="InvalidToken">Invalid Token</text>
        <text name="UserNotFound">User not found</text>
        <text name="EmailChangeRequest_Title">RealtoCrm change email request</text>
        <text name="EmailChangeRequest_SubTitle">This email is sent you to approve changing email.</text>
        <text name="EmailChangeRequest_ClickTheLinkBelowToChangeYourEmail">Please click the link below to approve
            changing your email:
        </text>
        <text name="EmailChangeRequest_Subject">RealtoCrm change email request</text>
        <text name="ChangeEmailRequestSentMessage">Change email request sent successfully</text>
        <text name="DoYouWantToGrantAccessToYourData">Do you want to grant {0} access to your data</text>
        <text name="ScopesRequested">Scopes requested</text>
        <text name="Accept">Accept</text>
        <text name="Deny">Deny</text>
        <text name="Authorization">Authorization</text>
        <text name="EmailAddressesDidNotMatch">Old email address not match!</text>
        <text name="NewEmailAddress">New email address</text>
        <text name="AdministrativeDivision">Administrative division</text>
        <text name="Nomenclatures">Nomenclatures</text>
        <text name="NomenclatureCreatedSuccessfully">The nomenclature was created successfully!</text>
        <text name="NomenclatureUpdatedSuccessfully">The nomenclature was successfully updated!</text>
        <text name="NomenclatureDeletedSuccessfully">The nomenclature was successfully deleted!</text>
        <text name="EntityDeleteWarningMessage{0}">Are you sure you want to delete {0}?</text>
        <text name="EntityDeletedSuccessfully">The entity was successfully deleted!</text>
        <text name="EntityUpdatedSuccessfully">The entity was successfully updated!</text>
        <text name="EntityCreatedSuccessfully">The entity was successfully created!</text>
        <text name="SelectedOffers{0}">{0} offers are selected</text>
        <text name="SelectedOffer{0}">{0} offer is selected</text>
        <text name="OfferToClient">Offer to client</text>
        <text name="PhotoOf">Photo of</text>
        <text name="SearchClientByEmailOrPhonePlaceholder">Search client by phone or email</text>
        <text name="AddFavorites">Add to favorites</text>
        <text name="SquareMetres">m²</text>
        <text name="AttachDocument">Attach document</text>
        <text name="UploadNewDocument">Upload new document</text>
        <text name="TypeOfContract">Contract type</text>
        <text name="DropFileHere">Drop file here</text>
        <text name="AddDocument">Add new document</text>
        <text name="PublishInPortals">Publish in portals</text>
        <text name="AddSearch">Add search</text>
        <text name="TermRent">Term rent</text>
        <text name="EstateSubCategory">Estate subcategory</text>
        <text name="ShowOnMap">Show on map</text>
        <text name="BuildingInformation">Building information</text>
        <text name="BuildingYear">Year of construction</text>
        <text name="DealMotive">Deal motive</text>
        <text name="RelatedPerson">Related person</text>
        <text name="AddRelatedPerson">Add related person</text>
        <text name="AddConsultant">Add consultant</text>
        <text name="AddMoreDetails">Add more details to make the offer active</text>
        <text name="ImagesAndMedia">Images and media</text>
        <text name="GoBack">Go back</text>
        <text name="ImageTitle">Image title</text>
        <text name="FileTitle">File title</text>
        <text name="description">Description</text>
        <text name="PriceFrom">Price from</text>
        <text name="PriceTo">Price to</text>
        <text name="CreateEmployee">Create employee</text>
        <text name="EditEmployee">Edit employee</text>
        <text name="OrganizationalUnits">Organizational units</text>
        <text name="IsEmailConfirmed">Email confirmed</text>
        <text name="GeneratePassword">Generate password</text>
        <text name="AssistantConsultant">Assistant consultant</text>
        <text name="AssistantManager">Assistant manager</text>
        <text name="ShowAll">Show all</text>
        <text name="ExternalAgencies">External agencies</text>
        <text name="ViewingStatuses">Viewing statuses</text>
        <text name="DepositStatuses">Deposit statuses</text>
        <text name="MatchStatuses">Match statuses</text>
        <text name="RelatedClientTypes">Related Client Types</text>
        <text name="RelatedClientType">Related Client Type</text>
        <text name="LegalPerson">Legal Person</text>
        <text name="VatRegistered">Reg. VAT</text>
        <text name="RelatedCompany">Related company</text>
        <text name="AddRelatedCompany">Add related company</text>
        <text name="SubSource">Sub source</text>
        <text name="DetailSource">Source details</text>
        <text name="CorrespondenceAddress">Correspondence Address</text>
        <text name="ClientPreference">Client Preference</text>
        <text name="MeetingStatus">Meeting status</text>
        <text name="HasContactMatch{0}">Matched with contact #{0}</text>
        <text name="SeeProfile">See Profile</text>
        <text name="EstatesAndOffers">Estates and offers</text>
        <text name="ContactPerson">Contact person</text>
        <text name="AddMeeting">Add meeting</text>
        <text name="ChooseDate">Choose date</text>
        <text name="ChooseHour">Choose hour</text>
        <text name="MeetingCreatedSuccessfully">The meeting was created successfully!</text>
        <text name="AddViewing">Add viewing</text>
        <text name="OfferViewing">Offer viewing</text>
        <text name="ChooseOffer">Choose offering site...</text>
        <text name="OfferId">Offer id</text>
        <text name="ChooseSearch">Choose searching site...</text>
        <text name="SearchId">Search id</text>
        <text name="ViewingCreatedSuccessfully">The viewing was created successfully!</text>
        <text name="AddComment">Add note</text>
        <text name="SaveComment">Save note</text>
        <text name="PrivateComment">Private note</text>
        <text name="AttachTo">Attach to</text>
        <text name="CommentCreatedSuccessfully">The comment was created successfully!</text>
        <text name="RolesAndPermissions">Roles and permissions</text>
        <text name="EditPermissions">Edit permissions</text>
        <text name="tenantName">Company</text>
        <text name="companyName">Company</text>
        <text name="CustomPermissions">Custom permissions</text>
        <text name="RolePermissions">Role permissions</text>
        <text name="GeneralSettings">General settings</text>
        <text name="LastContact">Last contact</text>
        <text name="StatusHead">Status</text>
        <text name="DateHead">Date</text>
        <text name="AddTask">Add task</text>
        <text name="SaveTask">Save task</text>
        <text name="TaskCreatedSuccessfully">The task was created successfully!</text>
        <text name="AddOneMore">Add one more</text>
        <text name="OutgoingCall">Оutgoing</text>
        <text name="FailedCall">Failed</text>
        <text name="IncomingCall">Incoming</text>
        <text name="MissedCall">Missed</text>
        <text name="RecentCalls">Recent Calls</text>
        <text name="SearchContactByPhoneOrEmail">Search contact by phone number or email</text>
        <text name="CheckAndContinue">Check and continue</text>
        <text name="AddToMyClients">Add to my clients</text>
        <text name="SmallLogo">Small logo</text>
        <text name="ExpiringAdvantages">Expiring advantages</text>
        <text name="SeeMatches">See Matches</text>
        <text name="JobPositions">Job positions</text>
        <text name="EditOffer">Edit offer</text>
        <text name="AddContact">Add contact</text>
        <text name="LoginInTenant">Login in company</text>
        <text name="UserPasswordResetDisabled">User password reset is disabled</text>
        <text name="EstateSettings">Estates settings</text>
        <text name="BuildingClassId">Building Class</text>
        <text name="ZeroCommission">Zero Commission</text>
        <text name="IsMansard">Mansard</text>
        <text name="IsEquipped">Equipped</text>
        <text name="HeatingId">Heating</text>
        <text name="FurnitureId">Furniture</text>
        <text name="HasSanitationRoom">Sanitation Room</text>
        <text name="HasCellar">Cellar</text>
        <text name="GarageId">Garage</text>
        <text name="HasLevel">Level</text>
        <text name="ConstructionTypeId">Construction type</text>
        <text name="HasElevator">Elevator</text>
        <text name="BedroomsCount">Bedrooms</text>
        <text name="HasInternet">Internet</text>
        <text name="HasPhone">Phone</text>
        <text name="HasAttic">Attic</text>
        <text name="HasSolarPanels">Solar Panels</text>
        <text name="IsEnergyEfficient">Energy efficient</text>
        <text name="HasParking">Parking</text>
        <text name="WindowHeight">Window height</text>
        <text name="HasPool">Pool</text>
        <text name="HasElectricity">Electricity</text>
        <text name="HasStore">Store</text>
        <text name="HasWater">Water</text>
        <text name="HasMortgage">Mortgage</text>
        <text name="HasBuildingVisa">Building visa</text>
        <text name="HasCableTv">Cable tv</text>
        <text name="IsRegulated">Regulated</text>
        <text name="IsHeated">Heated</text>
        <text name="HasGas">Gas</text>
        <text name="IsGuarded">Guarded</text>
        <text name="HasFreightPlatform">Freight platform</text>
        <text name="BathroomsCount">Bathrooms</text>
        <text name="HasFitness">Fitness</text>
        <text name="RoomsCount">Rooms</text>
        <text name="FacingDirectionId">Facing Direction</text>
        <text name="IsConfidential">Confidential</text>
        <text name="IsCultivable">Cultivable</text>
        <text name="CompletionLevelId">Completion level</text>
        <text name="ConditionId">Condition</text>
        <text name="IsTransitional">Transitional</text>
        <text name="HasTirAccess">Tir Access</text>
        <text name="Transparent">Transparent</text>
        <text name="HasCanal">Canal</text>
        <text name="HasDetailedSpatialPlan">Detailed spatial plan</text>
        <text name="IsGatedComplex">Gated complex</text>
        <text name="HasAirConditionSystem">Air condition system</text>
        <text name="HasSpecialPrice">Special price</text>
        <text name="HasSot">Sot</text>
        <text name="HasBar">Bar</text>
        <text name="UsefulFloorArea">Useful floor area</text>
        <text name="HasPublicTransportNearby">Public transport nearby</text>
        <text name="LifestyleId">Lifestyle</text>
        <text name="HasRailwayAccess">Railway access</text>
        <text name="DensityPercent">Density percent</text>
        <text name="IsPetFriendly">Pet friendly</text>
        <text name="WindowJoineryId">Window joinery</text>
        <text name="HasView">View</text>
        <text name="HasGarden">Garden</text>
        <text name="HasSewage">Sewage</text>
        <text name="HasRestaurant">Restaurant</text>
        <text name="IsSuitableForInvestment">Suitable for investment</text>
        <text name="IsUnderground">Underground</text>
        <text name="PageSize">Page size options</text>
        <text name="CertificateNumber">Certificate Number</text>
        <text name="ContactEstatesCount">This contact has {0} existing estates</text>
        <text name="WelcomeMessage">Welcome, {0}!</text>
        <text name="DescriptionAdvantages">Описание - advantages</text>
        <text name="DescriptionBuilding">Описание - building</text>
        <text name="DescriptionLocation">Описание - location</text>
        <text name="Find">Find</text>
        <text name="OrderBy">Order by</text>
        <text name="FromAtoZ">From A to Z</text>
        <text name="RoomCount">Room count</text>
        <text name="GoBackToMatches">Back to matches</text>
        <text name="AddClient">Add client</text>
        <text name="UpiNumber">Upi number</text>
        <text name="UpiTom">Upi tom</text>
        <text name="YoutubeVideoId">YouTube video Id</text>
        <text name="360VideoWalk">360 video walk</text>
        <text name="OfferedPrice">Offered price</text>
        <text name="SigningEndDate">Signing end date</text>
        <text name="DepositDate">Deposit date</text>
        <text name="ImportExport">Import/Export</text>
        <text name="AddDeposit">Add deposit</text>
        <text name="DesiredConfessionDate">Desired date for confession</text>
        <text name="OtherConditions">Other conditions</text>
        <text name="RejectedOne">Rejected</text>
        <text name="ForApproval">For approval</text>
        <text name="ForProcessing">For processing</text>
        <text name="FillOtherSide">Fill other side</text>
        <text name="PaidOrPartiallyPaid">Paid / partially paid</text>
        <text name="ByPriorAgreement">By prior agreement</text>
        <text name="EditDeal">Edit deal</text>
        <text name="NextSevenDays">Next seven days</text>
        <text name="UpcomingEvents">Upcoming events</text>
        <text name="ActiveClients">Active clients</text>
        <text name="ClientFailedCall">Failed call</text>
        <text name="ClientMissedCall">Missed call from client</text>
        <text name="ClientOutgoingCall">Outgoing call to client</text>
        <text name="ClientIncomingCall">Incoming call from client</text>
        <text name="ClientPhoneCall">Phone call with client</text>
        <text name="AddedTask">Task added</text>
        <text name="AddedNote">Note Added</text>
        <text name="AddProject">Add project</text>
        <text name="EstateGroup">Estate group</text>
        <text name="ProjectName">Project name</text>
        <text name="ProjectEdit">Project edit</text>
        <text name="ContractDate">Contract date</text>
        <text name="OfferShowProjectDescription">Show description for projects</text>
        <text name="AwaitsSecondSide">Awaits second side</text>
        <text name="AwaitsAdminApproval">Awaits admin approval</text>
        <text name="AwaitsFinanceApproval">Awaits finance approval</text>
        <text name="AwaitsPayment">Awaits payment</text>
        <text name="IsComplete">Complete</text>
        <text name="IsAnnulled">Annulled</text>
        <text name="DealId">Deal number</text>
        <text name="DealDate">Deal date</text>
        <text name="ArchivedDate">Archived date</text>
        <text name="ArchiveReason">Archive reason</text>
        <text name="FromDate">From date</text>
        <text name="ТоDate">To date</text>
        <text name="TotalNumberUnits">Total number units</text>
        <text name="ForSaleByInvestor">For sale by the investor</text>
        <text name="PriceOnRequestProject">Price on request</text>
        <text name="BuildStages">Build stages</text>
        <text name="CompletionDate">Completion date</text>
        <text name="PermissionToBuildDate">Permission to Build date</text>
        <text name="Akt14Date">Akt 14 Date</text>
        <text name="Akt15Date">Akt 15 Date</text>
        <text name="Akt16Date">Akt 16 Date</text>
        <text name="UpiDistrict">Upi district</text>
        <text name="InnerYard">Inner yard</text>
        <text name="OverheadCosts">Overhead costs</text>
        <text name="CertificateText">Certificate text</text>
        <text name="AddBuilding">Add building</text>
        <text name="BuildingName">Building name</text>
        <text name="BuildingPurpose">Building purpose</text>
        <text name="TotalFloors">Total floors</text>
        <text name="BuildingArea">Building area</text>
        <text name="PlatIdentification">Plat identification</text>
        <text name="AddressNotes">Address notes</text>
        <text name="OneRoom">One room</text>
        <text name="TwoRooms">Two rooms</text>
        <text name="ThreeRooms">Three rooms</text>
        <text name="FourRooms">Four rooms</text>
        <text name="MultiRooms">Multi rooms</text>
        <text name="ParkingSpot">Parking spot</text>
        <text name="ExclusiveContract">Exclusive contract</text>
        <text name="CommissionContract">Commission contract</text>
        <text name="ZeroCommissionContract">Zero commission contract</text>
        <text name="AttachedContract">Attached contract</text>
        <text name="AttachContract">Attach contract</text>
        <text name="DifferentTenantErrorMessage">You cannot edit an object from another company</text>
        <text name="AddContract">Add contract</text>
        <text name="RelatedPeople">Related people</text>
        <text name="360Link">360 link</text>
        <text name="CertificateImage">Certificate image</text>
        <text name="ArchiveOffers">Archive offers</text>
        <text name="ArchiveSearches">Archive searches</text>
        <text name="NoFutureDateErrorMessage">You cannot add a date in the future</text>
        <text name="NoLessThan18YearsErrorMessage">Are you sure the added date is correct ?</text>
        <text name="ForeignDocumentNumber">Document number</text>
        <text name="NoTenantErrorMessage">You must be in the company to perform this action</text>
        <text name="DivisionName">Division</text>
        <text name="DepartmentName">Department</text>
        <text name="AssignedBy">Assigned by</text>
        <text name="AddedOn">Added on</text>
        <text name="OfferConsultant">Offer consultant</text>
        <text name="SearchConsultant">Search consultant</text>
        <text name="NextViewing">Next viewing</text>
        <text name="ContactFilterAll">Filter all contacts</text>
        <text name="LastMeeting">Last meeting</text>
        <text name="PhoneExists">Number {0} already exists with client: {1} (№ {2})</text>
        <text name="SelectAll">Select all</text>
        <text name="SelectAllFromGroup">Select all from group</text>
        <text name="NotKnow">"Not know</text>
        <text name="PerformedViewing">Viewing performed for estate #{0}</text>
        <text name="EmployeeMoreOffers">See all offers</text>
        <text name="EmployeeMoreSearches">See all searches</text>
        <text name="ArrowButtonsTotal">1 of {0}</text>
        <text name="IsApprovedProject">With approved project</text>
        <text name="IsForCompensation">For compensation</text>
        <text name="PlotArea">Plot area</text>
        <text name="InvalidSumErrorMessage">Sum must be equal to {0}</text>
        <text name="ChoosePaymentTypeErrorMessage">Please choose payment type</text>
        <text name="ShowDocumentsTab">Show documents tab</text>
        <text name="CyrillicLettersErrorMessage">Please enter only cyrillic letters</text>
        <text name="CyrillicAndSpacesErrorMessage">Please enter only cyrillic letters and spaces</text>
        <text name="CyrillicAndSpecialCharactersErrorMessage">Please enter only cyrillic letters and special symbols:
            {0}
        </text>
        <text name="CyrillicSpacesAndSpecialCharactersErrorMessage">Please enter only cyrillic letters, spaces and
            special symbols: {0}
        </text>
        <text name="LinkedUsers">Linked users</text>
        <text name="AddedDeal">Added deal</text>
        <text name="DealBetweenSearchAndOffer">Deal between Search #{0} and Offer #{1}</text>
        <text name="CeilingHeightFrom">Ceiling height from</text>
        <text name="CeilingHeightTo">Ceiling height to</text>
        <text name="DepositDetails">Deposit details</text>
        <text name="OfferPrice">Offer price</text>
        <text name="OfferAddress">Offer address</text>
        <text name="GuaranteeContract">Guarantee contract</text>
        <text name="GuaranteeContractSigningDate">Guarantee contract signing date</text>
        <text name="GuaranteeContractExpirationDate">Guarantee contract expiration date</text>
        <text name="EstateGroupNotFound">Estate group not found.</text>
        <text name="ClientNotFound">Client not found.</text>
        <text name="ProjectCreationFailed">Project creation failed</text>
        <text name="EnterProjectName">Enter project name</text>
        <text name="ChooseEstateGroup">Choose or enter estate group name</text>
        <text name="OfferToEmployee">Offer to employee</text>
        <text name="OfferToEmployeeSuccessMessage">Offer was suggested successfully</text>
        <text name="TransferToEmployee">Transfer to employee</text>
        <text name="TransferOfferToEmployee">The offer was transferred successfully to {0}</text>
        <text name="OfferTeamEdit">Edit offer in team</text>
        <text name="OfferDepartmentEdit">Edit offer in department</text>
        <text name="OfferDivisionEdit">Edit offer in division</text>
        <text name="AdministrationFullAccess">Administration full access</text>
        <text name="DescriptionDirection">Description direction</text>
        <text name="ZonedPropertyNumber">Zoned property number</text>
        <text name="ZonedProperty">Zoned property</text>
        <text name="DensityArea">Density</text>
        <text name="HasKey">Key</text>
        <text name="AllNewSearches">See all new searches</text>
        <text name="SearchToEmployeeSuccessMessage">Search was suggested successfully</text>
        <text name="ArchiveSearch">Archive search #{0}</text>
        <text name="ChangeArchiveReason">Change archive reason</text>
        <text name="SearchArchivedSuccessfully">Search #{0} was successfully archived.</text>
        <text name="ArchiveDate">Archive date</text>
        <text name="ActivateSearchConfirmation">Are you sure, you want to activate the search</text>
        <text name="ContractAdd">Upload new contract</text>
        <text name="CommissionPercentage">Commission %</text>
        <text name="FacingDirection">Facing direction</text>
        <text name="LeaseTerm">Lease term</text>
        <text name="BuildupArea">Buildup area</text>
        <text name="AdvertisementBanner">Advertisement banner</text>
        <text name="OfferDelete">Delete offer</text>
        <text name="DescriptionFacade">Description - facade</text>
        <text name="DescriptionJoinery">Description - joinery</text>
        <text name="DescriptionIsolation">Description - isolation</text>
        <text name="DescriptionSewage">Description - sewage</text>
        <text name="InsertData">Insert data</text>
        <text name="OnlyDigitsErrorMessage">Only digits are allowed.</text>
        <text name="DateToCall">Date to call</text>
        <text name="OfferDeal">Offer deal</text>
        <text name="SearchDeal">Search deal</text>
        <text name="DealEstatePrice">Deal estate price</text>
        <text name="ApprovedByManager">Approved by manager</text>
        <text name="RentalAgreementDate">Rental agreement date</text>
        <text name="PaymentType">Payment type</text>
        <text name="Participants">Participants</text>
        <text name="NotaryDate">Notary date</text>
        <text name="OwnFunds">Own funds</text>
        <text name="DifferenceNote">Difference note</text>
        <text name="CreditCenter">Credit center</text>
        <text name="EmployeeIsMiddleNameRequired">Required field 'Middle name' in Employee edit</text>
        <text name="EmployeeIsIdentificationNumberRequired">Required field 'Identification number' in Employee edit
        </text>
        <text name="ContractExistenceForOfferToBecomeActive">Existence of a Contract for an Offer to become active
        </text>
        <text name="RemoveContract">Remove contract</text>
        <text name="OfferName">Header</text>
        <text name="OfferProjectId">Part of project</text>
        <text name="OfferTeamAccess">Access to the team's offers</text>
        <text name="OfferArchiveAccess">Access to all archived offers</text>
        <text name="OfferFreeAccess">Access to all free offers</text>
        <text name="SearchTeamAccess">Access to the team's searches</text>
        <text name="SearchArchiveAccess">Access to all archived searches</text>
        <text name="ContactTeamAccess">Access to the team's contacts</text>
        <text name="EmployeeIsTeamRequired">Required field 'Team' in Employee edit</text>
        <text name="CreatePayment">Create payment</text>
        <text name="ChangeOwner">Change owner</text>
        <text name="CreateUser">Create user</text>
        <text name="LinkedProjects">Linked projects</text>
        <text name="LinkedProjectsCreate">Create linked projects</text>
        <text name="BackToDetails">Back to details</text>
        <text name="EditClient">Edit contact</text>
        <text name="EmailExists">Email {0} already exists with client: {1} (№ {2})</text>
        <text name="ArchiveOffer">Archive offer #{0}</text>
        <text name="OfferArchivedSuccessfully">Offer #{0} was successfully archived.</text>
        <text name="OfferActivateMessage">Are you sure, you want to activate the offer</text>
        <text name="PublicComment">Public comment</text>
        <text name="NoCardTypesPlaceholder">There is not card types yet</text>
        <text name="NotEntered">Not entered</text>
        <text name="EstateAddNewUnit">Add new unit</text>
        <text name="EstateCreatedSuccessfully">Estate was successfully created!</text>
        <text name="EstateUnitsInProject">Units in project</text>
        <text name="DepositBetweenSearchAndOffer">Deposit between Search #{0} and Offer #{1}</text>
        <text name="AddedDeposit">Added deposit</text>
        <text name="DepositId">Deposit id</text>
        <text name="AddCheckForWeight">Add check for weight</text>
        <text name="HasWeight">Has weight</text>
        <text name="ChooseDocumentType">Choose document type</text>
        <text name="SaveWeight">Save weight</text>
        <text name="AddWeights">Add weights</text>
        <text name="SuccessfullyAddedWeights">Successfully added weights</text>
        <text name="ArchivedOn">Archived On</text>
        <text name="WithoutRights">Without Rights</text>
        <text name="RedirectDeposit">Redirect deposit</text>
        <text name="AcceptDeposit">Accept deposit</text>
        <text name="TurnIntoDeal">Turn into a deal</text>
        <text name="ReturnDeal">Return deal</text>
        <text name="EditDeposit">Edit deposit</text>
        <text name="ConfirmAdoptMessage">Would you like to adopt the deposit?</text>
        <text name="DepositAdoptedSuccessfully">The deposit was successfully adopted</text>
        <text name="DepositAdoptedErrorMessage">The deposit cannot be adopted</text>
        <text name="ConfirmTransferToOwnerMessage">Would you like to transfer the deposit to the seller?</text>
        <text name="DepositTransferredToOwnerSuccessfully">The deposit was transferred to the owner</text>
        <text name="DepositTransferredToOwnerErrorMessage">The deposit cannot be transferred to the owner</text>
        <text name="AnnexDate">Annex date</text>
        <text name="AnnexDeposit">Annex deposit</text>
        <text name="DepositAnnexedSuccessfully">The deposit was successfully annexed</text>
        <text name="DepositAnnexedErrorMessage">The deposit cannot be annexed</text>
        <text name="RejectionDate">RejectionDate</text>
        <text name="RejectionComment">Rejection reasons</text>
        <text name="RejectDeposit">Reject deposit</text>
        <text name="DepositRejectedSuccessfully">The deposit was successfully rejected</text>
        <text name="DepositRejectedErrorMessage">The deposit cannot be rejected</text>
        <text name="DepositActions">Deposit actions</text>
        <text name="DealExistsErrorMessage{0}">There is already match for deal #{0}</text>
        <text name="OfferAllAccess">Access to all offers</text>
        <text name="SearchFreeAccess">Access to all free searches</text>
        <text name="DeleteAllCustomPermissions">Delete all custom permissions</text>
        <text name="CustomPermissionsDeletedSuccessfully">Custom permissions were successfully deleted</text>
        <text name="ApartmentsImportedSuccessfully">{0} estates were imported</text>
        <text name="ApartmentsImporter">Apartments importer</text>
        <text name="GaragesImporter">Garages importer</text>
        <text name="OfficesImporter">Offices importer</text>
        <text name="HousesImporter">Houses importer</text>
        <text name="RemoveBuilding">Remove building</text>
        <text name="OfferArchiveAction">Access to Archive</text>
        <text name="OfferPlanViewingAction">Access to Plan viewing</text>
        <text name="OfferUploadContract">Access to Upload contract</text>
        <text name="OfferOfferToEmployeeAction">Access to Offer to...</text>
        <text name="OfferTransferToEmployeeAction">Access to Transfer to...</text>
        <text name="OfferGenerateSpaAction">Access to Generate SPA</text>
        <text name="OfferChangeOwnerAction">Access to Change owner</text>
        <text name="OfferAddEncumbrancesAction">Access to Add encumbrances</text>
        <text name="Approval">Approve</text>
        <text name="Annulled">Cancel</text>
        <text name="ConfirmDealApproveMessage">Are you sure you want to approve the deal?</text>
        <text name="DealApprovedSuccessfully">The deal was successfully approved</text>
        <text name="DealApprovedErrorMessage">The deal cannot be approved</text>
        <text name="ConfirmDealCancelMessage">Are you sure you want to cancel the deal?</text>
        <text name="DealCanceledSuccessfully">The deal was successfully cancelled</text>
        <text name="DealCanceledErrorMessage">The deal cannot be cancelled</text>
        <text name="DealAllSidesAccess">Access all sides in deal edit</text>
        <text name="AddAtLeastOneBuilding">Add at least one building</text>
        <text name="PercentageOnContractPaymentType">{0}% of preliminary contract</text>
        <text name="PercentageOnNotaryPaymentType">{0}% of notarial confession</text>
        <text name="ContractCommission">Contract commission (€)</text>
        <text name="ContractCommissionPercentage">Contract commission (%)</text>
        <text name="SaveBuildings">Save buildings</text>
        <text name="BuildingsSavedSuccessfully">Buildings were saved successfully</text>
        <text name="BuildingsSavedErrorMessage">Buildings cannot be saved</text>
        <text name="EnergyEfficiencyCertificate">Energy efficiency certificate</text>
        <text name="PortalHeaderTitle">{0} - manage synchronization of property #{1}</text>
        <text name="PortalInfoBoxMessage">You can publish the offer on {0} by checking the publish box.</text>
        <text name="PublishOfferInPortal">Publish offer on {0}</text>
        <text name="DuplicatePhoneNumbersNotAllowed">Duplicated phone numbers are not allowed</text>
        <text name="DuplicateEmailAddressesNotAllowed">Duplicated email addresses are not allowed</text>
        <text name="SignDate">Sign date</text>
        <text name="SearchPlanViewing">Access to Plan viewing</text>
        <text name="SearchTransferTo">Access to Transfer to...</text>
        <text name="SearchChangeOwner">Access to Change owner</text>
        <text name="SearchArchive">Access to Archive</text>
        <text name="SearchActivate">Access to Activate</text>
        <text name="SearchDelete">Access to Delete</text>
        <text name="SearchTeamEdit">Edit search in team</text>
        <text name="SearchDepartmentEdit">Edit search in department</text>
        <text name="SearchDivisionEdit">Edit search in division</text>
        <text name="DepositDateOldest">Sort by deposit date (oldest)</text>
        <text name="DepositDateNewest">Sort by deposit date (newest)</text>
        <text name="DepositEndDateOldest">Sort by deposit end date (oldest)</text>
        <text name="DepositEndDateNewest">Sort by deposit end date (newest)</text>
        <text name="OrderByPriceDifference">Sort by % discount</text>
        <text name="PriceDifferencePercentAscending">Sort by % discount</text>
        <text name="PriceDifferenceAmountAscending">Sort by amount discount</text>
        <text name="PriceToAreaAscending">Sort by price per sq.m (high to low)</text>
        <text name="PriceToAreaDescending">Sort by price per sq.m (low to high)</text>
        <text name="AreaAscending">Sort by area (large to small)</text>
        <text name="AreaDescending">Sort by area (small to large)</text>
        <text name="SearchActivatedSuccessfully">Are you sure, that you want to activate the search</text>
        <text name="DealConsultantAccess">Consultant filter access in deals</text>
        <text name="ProjectIsContractRequired">Is contract required in Project edit</text>
        <text name="TemplateForImport">Template for import</text>
        <text name="EmployeeIsDivisionRequired">Is division required in employee edit</text>
        <text name="DescriptionTransportation">Description - transportation</text>
        <text name="Coordinates">Location</text>
        <text name="ReCenter">Recenter</text>
    </texts>
</localizationDictionary>
