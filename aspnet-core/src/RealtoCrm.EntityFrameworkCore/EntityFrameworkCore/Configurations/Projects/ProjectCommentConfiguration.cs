namespace RealtoCrm.EntityFrameworkCore.Configurations.Projects;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Projects;

internal class ProjectCommentConfiguration : IEntityTypeConfiguration<ProjectComment>
{
    public void Configure(EntityTypeBuilder<ProjectComment> builder)
    {
        builder
            .HasKey(pc => new { pc.ProjectId, pc.CommentId });

        builder
            .HasOne(pc => pc.Project)
            .WithMany(p => p.ProjectsComments)
            .HasForeignKey(pc => pc.ProjectId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(pc => pc.Comment)
            .WithMany(c => c.ProjectsComments)
            .HasForeignKey(pc => pc.CommentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}