namespace RealtoCrm.EntityFrameworkCore.Configurations.Projects;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Projects;

internal class ProjectEmployeeConfiguration : IEntityTypeConfiguration<ProjectEmployee>
{
    public void Configure(EntityTypeBuilder<ProjectEmployee> builder)
    {
        builder
            .HasKey(pe => new { pe.ProjectId, pe.EmployeeId });

        builder
            .HasOne(pe => pe.Project)
            .WithMany(p => p.ProjectsEmployees)
            .HasForeignKey(pe => pe.ProjectId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(pe => pe.Employee)
            .WithMany(e => e.ProjectsEmployees)
            .HasForeignKey(pe => pe.EmployeeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}