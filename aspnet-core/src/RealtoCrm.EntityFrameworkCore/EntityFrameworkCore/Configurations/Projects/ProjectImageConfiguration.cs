namespace RealtoCrm.EntityFrameworkCore.Configurations.Projects;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Projects;

internal class ProjectImageConfiguration : IEntityTypeConfiguration<ProjectImage>
{
    public void Configure(EntityTypeBuilder<ProjectImage> builder)
    {
        builder
            .HasKey(pi => new { pi.ProjectId, pi.ImageId });

        builder
            .HasOne(pi => pi.Project)
            .WithMany(p => p.ProjectsImages)
            .HasForeignKey(pi => pi.ProjectId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(pi => pi.Image)
            .WithMany(i => i.ProjectsImages)
            .HasForeignKey(pi => pi.ImageId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}