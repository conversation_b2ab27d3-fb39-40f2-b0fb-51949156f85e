namespace RealtoCrm.EntityFrameworkCore.Configurations.Projects;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Projects;

internal class ProjectTaskConfiguration : IEntityTypeConfiguration<ProjectTask>
{
    public void Configure(EntityTypeBuilder<ProjectTask> builder)
    {
        builder
            .HasKey(pt => new { pt.ProjectId, pt.TaskId });

        builder
            .HasOne(pt => pt.Project)
            .WithMany(p => p.ProjectsTasks)
            .HasForeignKey(pt => pt.ProjectId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(pt => pt.Task)
            .WithMany(t => t.ProjectsTasks)
            .HasForeignKey(pt => pt.TaskId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}