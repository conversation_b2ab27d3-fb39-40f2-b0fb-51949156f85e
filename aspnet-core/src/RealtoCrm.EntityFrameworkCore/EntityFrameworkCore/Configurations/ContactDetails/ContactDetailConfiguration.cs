namespace RealtoCrm.EntityFrameworkCore.Configurations.ContactDetails;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.ContactDetails;
using static ModelConstants.Common;

internal class ContactDetailConfiguration : IEntityTypeConfiguration<ContactDetail>
{
    public void Configure(EntityTypeBuilder<ContactDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(cd => cd.Id);

        builder
            .Property(cd => cd.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();

        builder
            .HasOne(cd => cd.Type)
            .WithMany(t => t.ContactDetails)
            .HasForeignKey(cd => cd.TypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
    }
}