namespace RealtoCrm.EntityFrameworkCore.Configurations.ExternalSearches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.ExternalSearches;

internal class ExternalSearchConfiguration : IEntityTypeConfiguration<ExternalSearch>
{
    public void Configure(EntityTypeBuilder<ExternalSearch> builder)
    {
        builder
            .HasKey(es => es.Id);

        builder
            .Property(es => es.Name)
            .IsRequired();

        builder
            .HasOne(es => es.ExternalAgency)
            .WithMany(ea => ea.ExternalSearches)
            .HasForeignKey(es => es.ExternalAgencyId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}