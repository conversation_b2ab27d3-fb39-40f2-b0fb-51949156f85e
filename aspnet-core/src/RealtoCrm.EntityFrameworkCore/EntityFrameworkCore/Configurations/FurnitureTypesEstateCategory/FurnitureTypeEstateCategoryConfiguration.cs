using RealtoCrm.FurnitureTypesEstateCategories;

namespace RealtoCrm.EntityFrameworkCore.Configurations.FurnitureTypesEstateCategory;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class FurnitureTypeEstateCategoryConfiguration : IEntityTypeConfiguration<FurnitureTypeEstateCategory>
{
    public void Configure(EntityTypeBuilder<FurnitureTypeEstateCategory> builder)
    {
        builder.HasKey(ftec => new {ftec.FurnitureId, ftec.EstateCategoryId});
        
        builder
            .HasOne(ftec => ftec.Furniture)
            .WithMany(s => s.FurnitureTypesEstateCategories)
            .HasForeignKey(ftec => ftec.FurnitureId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(ftec => ftec.EstateCategory)
            .WithMany(p => p.FurnitureTypesEstateCategories)
            .HasForeignKey(ftec => ftec.EstateCategoryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}