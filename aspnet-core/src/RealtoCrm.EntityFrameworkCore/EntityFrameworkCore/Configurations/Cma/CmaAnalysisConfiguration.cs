namespace RealtoCrm.EntityFrameworkCore.Configurations.Cma;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Cma;
using RealtoCrm.Offers;

public class CmaAnalysesConfiguration : IEntityTypeConfiguration<CmaAnalysis>
{
    public void Configure(EntityTypeBuilder<CmaAnalysis> builder)
    {
        builder
            .HasOne(c => c.BaseOffer)
            .WithMany(o => o.CmaAnalysesForBaseOffer);

        builder.HasIndex(c => c.BaseOfferId)
            .IsUnique(false);

        builder
            .HasMany(c => c.SelectedOffers)
            .WithMany(o => o.CmaAnalysesForOffers)
            .UsingEntity(join => join.ToTable("CmaAnalysesOffers"));

        builder
            .HasMany(c => c.SelectedOffersWithDeals)
            .WithMany(o => o.CmaAnalysesForOffersWithDeals)
            .UsingEntity(join => join.ToTable("CmaAnalysesOffersWithDeals"));

        builder
            .HasMany(c => c.SelectedUnfulfilledOffers)
            .WithMany(o => o.CmaAnalysesForUnfulfilledOffers)
            .UsingEntity(join => join.ToTable("CmaAnalysesUnfulfilledOffers"));
        
        builder
            .HasMany(c => c.SelectedSearches)
            .WithMany(s => s.CmaAnalyses)
            .UsingEntity(join => join.ToTable("CmaAnalysesSearches"));
    }
}