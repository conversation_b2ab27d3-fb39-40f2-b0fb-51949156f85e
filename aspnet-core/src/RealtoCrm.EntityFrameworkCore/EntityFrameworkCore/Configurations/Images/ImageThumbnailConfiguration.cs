namespace RealtoCrm.EntityFrameworkCore.Configurations.Images;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Images;

public class ImageThumbnailConfiguration : IEntityTypeConfiguration<ImageThumbnail>
{
    public void Configure(EntityTypeBuilder<ImageThumbnail> builder)
    {
        builder
            .HasKey(it => it.Id);

        builder
            .Property(i => i.Name)
            .IsRequired();

        builder
            .Property(i => i.Source)
            .IsRequired();

        builder
            .HasOne(it => it.Image)
            .WithMany(i => i.Thumbnails)
            .HasForeignKey(it => it.ImageId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(i => i.Tenant)
            .WithMany()
            .HasForeignKey(i => i.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}