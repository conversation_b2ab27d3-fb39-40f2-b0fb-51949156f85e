namespace RealtoCrm.EntityFrameworkCore.Configurations.Images;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Images;

internal class ImageConfiguration : IEntityTypeConfiguration<Image>
{
    public void Configure(EntityTypeBuilder<Image> builder)
    {
        builder
            .HasKey(i => i.Id);

        builder
            .Property(i => i.Name)
            .IsRequired();

        builder
            .Property(i => i.Source)
            .IsRequired();

        builder
            .HasOne(i => i.Category)
            .WithMany(c => c.Images)
            .HasForeignKey(i => i.CategoryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(i => i.Tenant)
            .WithMany()
            .HasForeignKey(i => i.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}