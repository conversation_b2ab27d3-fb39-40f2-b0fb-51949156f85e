namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

public class ClientAddressConfiguraton : IEntityTypeConfiguration<ClientAddress>
{
    public void Configure(EntityTypeBuilder<ClientAddress> builder)
    {
        builder.<PERSON><PERSON><PERSON>(ca => ca.Id);
        
        builder
            .HasOne(ca => ca.Client)
            .WithMany(c => c.ClientAddresses)
            .HasForeignKey(ca => ca.ClientId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder
            .HasOne(ca => ca.Address)
            .WithMany(c => c.ClientAddresses)
            .HasForeignKey(ca => ca.AddressId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(ca => ca.AddressType)
            .WithMany(at => at.ClientAddresses)
            .HasForeignKey(ca => ca.AddressTypeId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();
    }
}