namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

internal class ClientConfiguration : IEntityTypeConfiguration<Client>
{
    private const int ClientTypeFirstId = 1;

    public void Configure(EntityTypeBuilder<Client> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(c => c.Id);

        builder
            .HasOne(c => c.Nationality)
            .WithMany(n => n.Clients)
            .HasForeignKey(c => c.NationalityId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.LegalEntity)
            .WithOne(l => l.Client)
            .HasForeignKey<ClientLegalEntity>(c => c.ClientId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.PersonalData)
            .WithOne(p => p.Client)
            .HasForeignKey<ClientPersonalData>(c => c.ClientId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.TypeId)
            .HasDefaultValue(ClientTypeFirstId);

        builder.HasOne(c => c.Type)
            .WithMany(ct => ct.Clients)
            .HasForeignKey(c => c.TypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.EstateGroup)
            .WithMany(eg => eg.Clients)
            .HasForeignKey(c => c.EstateGroupId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.Tenant)
            .WithMany()
            .HasForeignKey(c => c.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}