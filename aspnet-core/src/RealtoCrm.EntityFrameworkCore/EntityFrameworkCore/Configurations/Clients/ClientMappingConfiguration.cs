namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

public class ClientMappingConfiguration : IEntityTypeConfiguration<ClientMapping>
{
    public void Configure(EntityTypeBuilder<ClientMapping> builder)
    {
        builder
            .HasKey(cm => cm.Id);
        
        builder
            .HasOne(c => c.Client)
            .WithOne(c => c.ClientMapping)
            .HasForeignKey<ClientMapping>(cm => cm.ClientId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Property(pm => pm.AsiId)
            .IsRequired(false);

        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}