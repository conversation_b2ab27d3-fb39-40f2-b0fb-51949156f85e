namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

public class ClientClientPreferenceConfiguration: IEntityTypeConfiguration<ClientClientPreference>
{
    public void Configure(EntityTypeBuilder<ClientClientPreference> builder)
    {
        builder
            .Has<PERSON>ey(cw => new { cw.ClientId, cw.ClientPreferenceId });

        builder
            .HasOne(cw => cw.Client)
            .WithMany(c => c.ClientsPreferences)
            .HasForeignKey(cw => cw.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(cw => cw.ClientPreference)
            .WithMany(c => c.ClientsPreferences)
            .HasForeignKey(cw => cw.ClientPreferenceId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}