namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

internal class ClientTagConfiguration : IEntityTypeConfiguration<ClientTags>
{
    public void Configure(EntityTypeBuilder<ClientTags> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(ctc => new { ctc.ClientId, ctc.TagId });

        builder
            .HasOne(ctc => ctc.Client)
            .WithMany(c => c.ClientsTags)
            .HasForeignKey(ctc => ctc.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(ctc => ctc.Tag)
            .WithMany(c => c.ClientsTags)
            .HasForeignKey(ctc => ctc.TagId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

    }
}