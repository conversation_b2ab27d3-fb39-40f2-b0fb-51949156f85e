namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

internal class ClientTaskConfiguration : IEntityTypeConfiguration<ClientTask>
{
    public void Configure(EntityTypeBuilder<ClientTask> builder)
    {
        builder
            .HasKey(ct => new { ct.ClientId, ct.TaskId });

        builder
            .HasOne(ct => ct.Client)
            .WithMany(c => c.ClientsTasks)
            .HasForeignKey(ct => ct.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(ct => ct.Task)
            .WithMany(t => t.ClientsTasks)
            .HasForeignKey(ct => ct.TaskId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}