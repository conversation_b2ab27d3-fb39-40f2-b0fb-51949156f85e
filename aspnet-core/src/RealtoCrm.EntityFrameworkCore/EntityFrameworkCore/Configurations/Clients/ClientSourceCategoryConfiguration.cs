namespace RealtoCrm.EntityFrameworkCore.Configurations.Clients;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Clients;

internal class ClientSourceCategoryConfiguration : IEntityTypeConfiguration<ClientSourceCategory>
{
    public void Configure(EntityTypeBuilder<ClientSourceCategory> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(cs => cs.Id);

        builder
            .HasOne(cs => cs.Client)
            .WithMany(c => c.ClientsSourceCategories)
            .HasForeignKey(cs => cs.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(cs => cs.SourceCategory)
            .WithMany(sc => sc.ClientsSourceCategories)
            .HasForeignKey(cs => cs.SourceCategoryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(cs => cs.Employee)
            .WithMany(c => c.ClientsSourceCategories)
            .HasForeignKey(cs => cs.EmployeeId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}