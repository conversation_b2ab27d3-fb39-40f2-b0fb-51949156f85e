namespace RealtoCrm.EntityFrameworkCore.Configurations.Files;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Files;
using static ModelConstants.Common;
using static RealtoCrm.Files.ModelConstants.File;

internal class FileConfiguration : IEntityTypeConfiguration<File>
{
    public void Configure(EntityTypeBuilder<File> builder)
    {
        builder
            .Has<PERSON>ey(f => f.Id);

        builder
            .Property(f => f.FileName)
            .HasMaxLength(MaxFileNameLength)
            .IsRequired();

        builder
            .Property(f => f.Source)
            .HasMaxLength(MaxUrlLength)
            .IsRequired();

        builder
            .HasOne(f => f.Category)
            .WithMany(c => c.Files)
            .HasForeignKey(f => f.CategoryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
        
        builder
            .HasOne(f => f.Tenant)
            .WithMany(c => c.Files)
            .HasForeignKey(f => f.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.Client)
            .WithMany(c => c.Files)
            .HasForeignKey(c => c.ClientId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}