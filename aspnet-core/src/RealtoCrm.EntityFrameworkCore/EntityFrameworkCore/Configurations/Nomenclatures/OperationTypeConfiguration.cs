namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class OperationTypeConfiguration : IEntityTypeConfiguration<OperationType>
{
    public void Configure(EntityTypeBuilder<OperationType> builder)
        => builder.HasNomenclature();
}