namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class BuildingClassConfiguration : IEntityTypeConfiguration<BuildingClass>
{
    public void Configure(EntityTypeBuilder<BuildingClass> builder)
        => builder.HasNomenclature();
}