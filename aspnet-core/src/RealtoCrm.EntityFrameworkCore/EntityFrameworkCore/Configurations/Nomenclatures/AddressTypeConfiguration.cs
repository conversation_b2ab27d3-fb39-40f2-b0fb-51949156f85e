namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.EntityFrameworkCore.Extensions;
using RealtoCrm.Nomenclatures;

public class AddressTypeConfiguration : IEntityTypeConfiguration<AddressType>
{
    public void Configure(EntityTypeBuilder<AddressType> builder)
        => builder.HasNomenclature();
}