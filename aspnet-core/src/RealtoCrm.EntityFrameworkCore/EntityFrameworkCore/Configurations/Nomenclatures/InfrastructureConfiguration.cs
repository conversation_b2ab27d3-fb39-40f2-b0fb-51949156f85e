namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class InfrastructureConfiguration : IEntityTypeConfiguration<Infrastructure>
{
    public void Configure(EntityTypeBuilder<Infrastructure> builder)
        => builder.HasNomenclature();
}