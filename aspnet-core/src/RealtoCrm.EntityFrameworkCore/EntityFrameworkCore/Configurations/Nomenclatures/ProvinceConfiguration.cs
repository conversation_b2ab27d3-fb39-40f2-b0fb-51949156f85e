namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.Nomenclatures.ModelConstants.Address;

internal class ProvinceConfiguration : IEntityTypeConfiguration<Province>
{
    public void Configure(EntityTypeBuilder<Province> builder)
    {
        builder
            .HasNomenclature();

        builder
            .Property(p => p.Code)
            .HasMaxLength(MaxCodeLength)
            .IsRequired();

        builder
            .HasOne(p => p.Country)
            .WithMany(c => c.Provinces)
            .HasForeignKey(p => p.CountryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}