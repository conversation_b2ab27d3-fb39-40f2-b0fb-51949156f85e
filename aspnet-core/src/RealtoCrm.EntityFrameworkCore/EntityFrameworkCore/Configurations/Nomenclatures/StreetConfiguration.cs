namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class StreetConfiguration : IEntityTypeConfiguration<Street>
{
    public void Configure(EntityTypeBuilder<Street> builder)
    {
        builder
            .HasNomenclature();
        
        builder
            .Property(n => n.IsVerified)
            .IsRequired();

        builder
            .HasOne(s => s.PopulatedPlace)
            .WithMany(p => p.Streets)
            .HasForeignKey(s => s.PopulatedPlaceId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(s => s.District)
            .WithMany(n => n.Streets)
            .HasForeignKey(s => s.DistrictId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}