namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.Nomenclatures.ModelConstants.Address;

internal class PopulatedPlaceConfiguration : IEntityTypeConfiguration<PopulatedPlace>
{
    public void Configure(EntityTypeBuilder<PopulatedPlace> builder)
    {
        builder
            .HasNomenclature();

        builder
            .Property(p => p.Code)
            .HasMaxLength(MaxCodeLength)
            .IsRequired();

        builder
            .Property(p => p.Ekatte)
            .HasMaxLength(MaxEkatteLength)
            .IsRequired();

        builder
            .Property(p => p.Type)
            .IsRequired();

        builder
            .Property(p => p.Latitude)
            .IsRequired();

        builder
            .Property(p => p.Longitude)
            .IsRequired();

        builder
            .HasOne(p => p.Municipality)
            .WithMany(m => m.PopulatedPlaces)
            .HasForeignKey(p => p.MunicipalityId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}