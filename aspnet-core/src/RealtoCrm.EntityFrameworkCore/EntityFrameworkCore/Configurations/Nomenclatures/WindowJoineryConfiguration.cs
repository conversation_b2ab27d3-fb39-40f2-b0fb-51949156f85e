namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class WindowJoineryConfiguration : IEntityTypeConfiguration<WindowJoinery>
{
    public void Configure(EntityTypeBuilder<WindowJoinery> builder)
        => builder.HasNomenclature();
}