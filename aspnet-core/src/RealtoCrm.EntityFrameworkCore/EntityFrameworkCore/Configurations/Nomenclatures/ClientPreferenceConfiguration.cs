namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.EntityFrameworkCore.Extensions;
using RealtoCrm.Nomenclatures;

public class ClientPreferenceConfiguration : IEntityTypeConfiguration<ClientPreference>
{
    public void Configure(EntityTypeBuilder<ClientPreference> builder)
        => builder.HasNomenclature();
}