namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

public class SectorMappingConfiguration : IEntityTypeConfiguration<SectorMapping>
{
    public void Configure(EntityTypeBuilder<SectorMapping> builder)
    {
        builder.HasKey(pm => pm.Id);
        
        builder
            .HasOne(o => o.Sector)
            .WithMany()
            .HasForeignKey(o => o.SectorId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pm => pm.AsiId)
            .IsRequired(false);
        
        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}