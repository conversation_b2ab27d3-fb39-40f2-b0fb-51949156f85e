using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.EntityFrameworkCore.Extensions;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

public class BuildingPurposeConfiguration: IEntityTypeConfiguration<BuildingPurpose>
{
    public void Configure(EntityTypeBuilder<BuildingPurpose> builder)
        => builder.HasNomenclature();
}