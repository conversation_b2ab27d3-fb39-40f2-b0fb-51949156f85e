namespace RealtoCrm.EntityFrameworkCore.Configurations.AddressesEstateCharacteristics;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.AddressesEstateCharacteristics;
using static ModelConstants.Common;

public class AddressesToEstateCategoryCharacteristicsConfiguration : IEntityTypeConfiguration<AddressToEstateCategoryCharacteristic>
{
    public void Configure(EntityTypeBuilder<AddressToEstateCategoryCharacteristic> builder)
    {
        builder
            .Property(o => o.PropertyName)
            .HasMaxLength(MaxNameLength)
            .IsRequired();
        
        builder
            .HasOne(o => o.Category)
            .WithMany()
            .HasForeignKey(o => o.CategoryId)
            .IsRequired();

        builder
            .Property(o => o.IsVisible)
            .HasDefaultValue(true)
            .IsRequired();
        
        builder
            .HasOne(o => o.Tenant)
            .WithMany()
            .HasForeignKey(o => o.TenantId)
            .IsRequired();
    }
}