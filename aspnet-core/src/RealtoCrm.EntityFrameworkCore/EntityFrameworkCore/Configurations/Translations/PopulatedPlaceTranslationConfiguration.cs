namespace RealtoCrm.EntityFrameworkCore.Configurations.Translations;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Translations;
using static RealtoCrm.Translations.ModelConstants.Common;

internal class PopulatedPlaceTranslationConfiguration : IEntityTypeConfiguration<PopulatedPlaceTranslation>
{
    public void Configure(EntityTypeBuilder<PopulatedPlaceTranslation> builder)
    {

        builder
            .Property(p => p.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();
    }
}