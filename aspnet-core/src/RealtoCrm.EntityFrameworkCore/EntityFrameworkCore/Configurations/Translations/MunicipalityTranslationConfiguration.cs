namespace RealtoCrm.EntityFrameworkCore.Configurations.Translations;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Translations;
using static RealtoCrm.Translations.ModelConstants.Common;

internal class MunicipalityTranslationConfiguration : IEntityTypeConfiguration<MunicipalityTranslation>
{
    public void Configure(EntityTypeBuilder<MunicipalityTranslation> builder)
    {

        builder
            .Property(p => p.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();
    }
}