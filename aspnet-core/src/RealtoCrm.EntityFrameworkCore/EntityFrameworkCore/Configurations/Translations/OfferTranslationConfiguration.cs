namespace RealtoCrm.EntityFrameworkCore.Configurations.Translations;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Translations;
using static RealtoCrm.Translations.ModelConstants.OfferTranslation;

internal class OfferTranslationConfiguration : IEntityTypeConfiguration<OfferTranslation>
{
    public void Configure(EntityTypeBuilder<OfferTranslation> builder)
    {

        builder
            .Property(p => p.Name)
            .HasMaxLength(MaxTitleLength);

        builder
            .Property(p => p.Description)
            .HasMaxLength(MaxDescriptionLength);

        builder
            .Property(p => p.Distribution)
            .HasMaxLength(MaxDistributionLength);

        builder
            .Property(p => p.Advantages)
            .HasMaxLength(MaxAdvantagesLength);

        builder
            .Property(p => p.Landmark)
            .HasMaxLength(MaxLandmarkLength);

        builder
            .Property(p => p.AdditionalInformation)
            .HasMaxLength(MaxAdditionalInformationLength);

        builder
            .Property(p => p.Parking)
            .HasMaxLength(MaxParkingLength);

        builder
            .Property(p => p.Location)
            .HasMaxLength(MaxLocationLength);
        
    }
}