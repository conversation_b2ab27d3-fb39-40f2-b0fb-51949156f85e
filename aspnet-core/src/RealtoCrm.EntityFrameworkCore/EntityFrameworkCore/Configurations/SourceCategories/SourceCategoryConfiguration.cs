namespace RealtoCrm.EntityFrameworkCore.Configurations.SourceCategories;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.SourceCategories;
using static RealtoCrm.SourceCategories.ModelConstants.Source;

internal class SourceCategoryConfiguration : IEntityTypeConfiguration<SourceCategory>
{
    public void Configure(EntityTypeBuilder<SourceCategory> builder)
    {
        builder
            .HasKey(s => s.Id);

        builder
            .Property(s => s.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();

        builder
            .HasOne(s => s.Parent)
            .WithMany(p => p.Children)
            .HasForeignKey(s => s.ParentId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}