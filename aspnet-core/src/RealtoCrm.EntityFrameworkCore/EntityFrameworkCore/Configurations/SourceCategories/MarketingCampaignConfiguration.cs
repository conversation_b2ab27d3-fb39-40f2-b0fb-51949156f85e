namespace RealtoCrm.EntityFrameworkCore.Configurations.SourceCategories;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.SourceCategories;
using static RealtoCrm.SourceCategories.ModelConstants.MarketingCampaign;

internal class MarketingCampaignConfiguration : IEntityTypeConfiguration<MarketingCampaign>
{
    public void Configure(EntityTypeBuilder<MarketingCampaign> builder)
    {
        builder
            .HasKey(m => m.Id);

        builder
            .Property(m => m.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();
    }
}