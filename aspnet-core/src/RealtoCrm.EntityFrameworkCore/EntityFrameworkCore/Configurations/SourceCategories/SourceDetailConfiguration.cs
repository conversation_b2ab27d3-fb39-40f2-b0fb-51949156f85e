namespace RealtoCrm.EntityFrameworkCore.Configurations.SourceCategories;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.SourceCategories;

internal class SourceDetailConfiguration : IEntityTypeConfiguration<SourceDetail>
{
    public void Configure(EntityTypeBuilder<SourceDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(sd => sd.Id);

        builder
            .HasOne(sd => sd.Category)
            .WithMany(c => c.Details)
            .HasForeignKey(sd => sd.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sd => sd.MarketingCampaign)
            .WithMany(c => c.Details)
            .HasForeignKey(sd => sd.DetailMarketingCampaignId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sd => sd.SocialMedia)
            .WithMany(c => c.Details)
            .HasForeignKey(sd => sd.DetailSocialMediaId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sd => sd.DetailClient)
            .WithMany(c => c.Details)
            .HasForeignKey(sd => sd.DetailClientId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sd => sd.Employee)
            .WithMany(c => c.Details)
            .HasForeignKey(sd => sd.DetailEmployeeId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sd => sd.Website)
            .WithMany(c => c.Details)
            .HasForeignKey(sd => sd.DetailWebsiteId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);
    }
}