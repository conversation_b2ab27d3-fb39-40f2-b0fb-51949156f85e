namespace RealtoCrm.EntityFrameworkCore.Configurations.Deals;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deals;

internal class DealParticipantConfiguration : IEntityTypeConfiguration<DealParticipant>
{
    public void Configure(EntityTypeBuilder<DealParticipant> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(dp => dp.Id);

        builder
            .HasOne(dp => dp.Participation)
            .WithMany(p => p.DealParticipants)
            .HasForeignKey(dp => dp.ParticipationId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(dp => dp.Employee)
            .WithMany(e => e.DealParticipants)
            .HasForeignKey(dp => dp.EmployeeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(dp => dp.Percentage)
            .IsRequired();

        builder
            .OwnsMoney(dp => dp.Commission);

        builder
            .Property(dp => dp.IsLead)
            .IsRequired();

        builder
            .HasOne(dp => dp.Tenant)
            .WithMany()
            .HasForeignKey(dp => dp.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}