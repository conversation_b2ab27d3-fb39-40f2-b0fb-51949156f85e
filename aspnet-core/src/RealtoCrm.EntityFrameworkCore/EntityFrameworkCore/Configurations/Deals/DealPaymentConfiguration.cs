namespace RealtoCrm.EntityFrameworkCore.Configurations.Deals;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deals;

internal class DealPaymentConfiguration : IEntityTypeConfiguration<DealPayment>
{
    public void Configure(EntityTypeBuilder<DealPayment> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(dp => dp.Id);

        builder
            .OwnsMoney(dp => dp.Price);

        builder
            .HasOne(dp => dp.Employee)
            .WithMany(e => e.DealPayments)
            .HasForeignKey(dp => dp.EmployeeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(dp => dp.DealParticipation)
            .WithMany(dp => dp.DealPayments)
            .HasForeignKey(dp => dp.DealParticipationId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}