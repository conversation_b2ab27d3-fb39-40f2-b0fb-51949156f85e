namespace RealtoCrm.DealMotiveOperationTypes;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class DealMotiveOperationTypeConfiguration : IEntityTypeConfiguration<DealMotiveOperationType>
{
    public void Configure(EntityTypeBuilder<DealMotiveOperationType> builder)
    {
        builder.<PERSON><PERSON><PERSON>(dmop => new { dmop.DealMotiveId, dmop.OperationTypeId });

        builder.HasOne(dmop => dmop.DealMotive)
            .WithMany(dm => dm.DealMotiveOperationTypes)
            .HasForeignKey(dmop => dmop.DealMotiveId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(dmop => dmop.OperationType)
            .WithMany(op => op.DealMotiveOperationTypes)
            .HasForeignKey(dmop => dmop.OperationTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}