namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

internal class OfferFileConfiguration : IEntityTypeConfiguration<OfferFile>
{
    public void Configure(EntityTypeBuilder<OfferFile> builder)
    {
        builder
            .HasKey(of => new { of.OfferId, of.FileId });

        builder
            .HasOne(of => of.Offer)
            .WithMany(o => o.OffersFiles)
            .HasForeignKey(of => of.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(of => of.File)
            .WithMany(f => f.OffersFiles)
            .HasForeignKey(of => of.FileId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}