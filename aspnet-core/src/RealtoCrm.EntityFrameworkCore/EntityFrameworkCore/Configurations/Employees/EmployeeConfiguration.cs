namespace RealtoCrm.EntityFrameworkCore.Configurations.Employees;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Employees;
using static ModelConstants.Common;
using static ModelConstants.UserProfile;

internal class EmployeeConfiguration : IEntityTypeConfiguration<Employee>
{
    public void Configure(EntityTypeBuilder<Employee> builder)
    {
        builder
            .HasKey(e => e.Id);

        builder
            .Property(e => e.FirstName)
            .HasMaxLength(MaxNameLength);

        builder
            .Property(e => e.MiddleName)
            .HasMaxLength(MaxNameLength);

        builder
            .Property(e => e.LastName)
            .HasMaxLength(MaxNameLength);

        builder
            .Property(e => e.DisplayName)
            .HasMaxLength(MaxDisplayNameLength);

        builder
            .Property(e => e.PhoneNumber)
            .HasMaxLength(MaxPhoneNumberLength);

        builder
            .Property(e => e.SimCardNumber)
            .HasMaxLength(MaxSimCardNumberLength);

        builder
            .Property(e => e.WorkPosition)
            .HasMaxLength(MaxWorkPositionLength);

        builder
            .Property(e => e.IdentificationNumber)
            .HasMaxLength(MaxIdentificationNumberLength);

        builder
            .HasOne(e => e.UserAccount)
            .WithOne()
            .HasForeignKey<Employee>(e => e.UserAccountId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Office)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.OfficeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Team)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.TeamId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Department)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.DepartmentId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Division)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.DivisionId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.Company)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.CompanyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(e => e.CompanyPosition)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.CompanyPositionId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder
            .HasOne(e => e.Manager)
            .WithMany(o => o.Employees)
            .HasForeignKey(e => e.ManagerId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder
            .HasOne(e => e.AssistsTo)
            .WithMany(o => o.Assistants)
            .HasForeignKey(e => e.AssistsToId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.HasIndex(e => new { e.CompanyId, e.IsSystem })
            .IsUnique()
            .HasFilter("\"IsSystem\" = TRUE");
    }
}