namespace RealtoCrm.EntityFrameworkCore.Configurations.Addresses;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Addresses;
using static RealtoCrm.Addresses.ModelConstants.Address;

internal class AddressConfiguration : IEntityTypeConfiguration<Address>
{
    public void Configure(EntityTypeBuilder<Address> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(a => a.Id);

        builder
            .HasOne(a => a.Country)
            .WithMany(c => c.Addresses)
            .HasForeignKey(a => a.CountryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(a => a.PopulatedPlace)
            .WithMany(p => p.Addresses)
            .HasForeignKey(a => a.PopulatedPlaceId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(a => a.Province)
            .WithMany(p => p.Addresses)
            .HasForeignKey(a => a.ProvinceId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(a => a.Municipality)
            .WithMany(m => m.Addresses)
            .HasForeignKey(a => a.MunicipalityId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(a => a.District)
            .WithMany(n => n.Addresses)
            .HasForeignKey(a => a.DistrictId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(a => a.Street)
            .WithMany(s => s.Addresses)
            .HasForeignKey(a => a.StreetId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(a => a.StreetNumber)
            .HasMaxLength(MaxStreetNumberLength);

        builder
            .Property(a => a.BlockNumber)
            .HasMaxLength(MaxBlockNumberLength);

        builder
            .Property(a => a.EntranceNumber)
            .HasMaxLength(MaxEntranceNumberLength);

        builder
            .Property(a => a.FloorNumber)
            .HasMaxLength(MaxFloorNumberLength);

        builder
            .Property(a => a.ApartmentNumber)
            .HasMaxLength(MaxApartmentNumberLength);

        builder
            .HasOne(a => a.Tenant)
            .WithMany(t => t.Addresses)
            .HasForeignKey(a => a.TenantId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();
    }
}