namespace RealtoCrm.EntityFrameworkCore.Configurations.Viewings;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Viewings;

internal class ViewingCommentConfiguration : IEntityTypeConfiguration<ViewingComment>
{
    public void Configure(EntityTypeBuilder<ViewingComment> builder)
    {
        builder
            .HasKey(vc => new { vc.ViewingId, vc.CommentId });

        builder
            .HasOne(vc => vc.Viewing)
            .WithMany(v => v.ViewingsComments)
            .HasForeignKey(vc => vc.ViewingId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(vc => vc.Comment)
            .WithMany(c => c.ViewingsComments)
            .HasForeignKey(vc => vc.CommentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}