namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchCountryConfiguration : IEntityTypeConfiguration<SearchCountry>
{
    public void Configure(EntityTypeBuilder<SearchCountry> builder)
    {
        builder
            .HasKey(sc => new { sc.SearchId, sc.CountryId });

        builder
            .HasOne(sc => sc.Search)
            .WithMany(s => s.SearchesCountries)
            .HasForeignKey(sc => sc.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sc => sc.Country)
            .WithMany(c => c.SearchesCountries)
            .HasForeignKey(sc => sc.CountryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}