namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

public class SearchFacingDirectionConfiguration : IEntityTypeConfiguration<SearchFacingDirection>
{
    public void Configure(EntityTypeBuilder<SearchFacingDirection> builder)
    {
        builder
            .HasKey(sh => new { sh.SearchId, sh.FacingDirectionId });

        builder
            .HasOne(sh => sh.Search)
            .WithMany(s => s.SearchesFacingDirections)
            .HasForeignKey(sh => sh.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sh => sh.FacingDirection)
            .WithMany(h => h.SearchesFacingDirections)
            .HasForeignKey(sh => sh.FacingDirectionId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}