namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchEstateTypeConfiguration : IEntityTypeConfiguration<SearchEstateType>
{
    public void Configure(EntityTypeBuilder<SearchEstateType> builder)
    {
        builder
            .HasKey(se => new { se.SearchId, se.EstateTypeId });

        builder
            .HasOne(se => se.Search)
            .WithMany(s => s.SearchesEstateTypes)
            .HasForeignKey(se => se.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(se => se.EstateType)
            .WithMany(e => e.SearchesEstateTypes)
            .HasForeignKey(se => se.EstateTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}