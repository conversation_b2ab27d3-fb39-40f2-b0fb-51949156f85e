namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchHeatingConfiguration : IEntityTypeConfiguration<SearchHeating>
{
    public void Configure(EntityTypeBuilder<SearchHeating> builder)
    {
        builder
            .HasKey(sh => new { sh.SearchId, sh.HeatingId });

        builder
            .HasOne(sh => sh.Search)
            .WithMany(s => s.SearchesHeating)
            .HasForeignKey(sh => sh.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sh => sh.Heating)
            .WithMany(h => h.SearchesHeating)
            .HasForeignKey(sh => sh.HeatingId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}