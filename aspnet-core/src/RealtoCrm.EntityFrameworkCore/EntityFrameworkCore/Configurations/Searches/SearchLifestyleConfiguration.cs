namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

public class SearchLifestyleConfiguration : IEntityTypeConfiguration<SearchLifestyle>
{
    public void Configure(EntityTypeBuilder<SearchLifestyle> builder)
    {
        builder
            .HasKey(sh => new { sh.SearchId, sh.LifestyleId });

        builder
            .HasOne(sh => sh.Search)
            .WithMany(s => s.SearchesLifestyles)
            .HasForeignKey(sh => sh.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sh => sh.Lifestyle)
            .WithMany(h => h.SearchesLifestyles)
            .HasForeignKey(sh => sh.LifestyleId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}