namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchTaskConfiguration : IEntityTypeConfiguration<SearchTask>
{
    public void Configure(EntityTypeBuilder<SearchTask> builder)
    {
        builder
            .HasKey(st => new { st.SearchId, st.TaskId });

        builder
            .HasOne(st => st.Search)
            .WithMany(s => s.SearchesTasks)
            .HasForeignKey(st => st.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(st => st.Task)
            .WithMany(t => t.SearchesTasks)
            .HasForeignKey(st => st.TaskId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}