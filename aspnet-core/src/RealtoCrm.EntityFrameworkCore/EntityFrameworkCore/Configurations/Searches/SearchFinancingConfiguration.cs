namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchFinancingConfiguration : IEntityTypeConfiguration<SearchFinancing>
{
    public void Configure(EntityTypeBuilder<SearchFinancing> builder)
    {
        builder
            .HasKey(sf => new { sf.SearchId, sf.FinancingId });

        builder
            .HasOne(sf => sf.Search)
            .WithMany(s => s.SearchesFinancing)
            .HasForeignKey(sf => sf.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sf => sf.Financing)
            .WithMany(f => f.SearchesFinancing)
            .HasForeignKey(sf => sf.FinancingId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}