namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

public class SearchEstateGroupConfiguration : IEntityTypeConfiguration<SearchEstateGroup>
{
    public void Configure(EntityTypeBuilder<SearchEstateGroup> builder)
    {
        builder
            .HasKey(sc => new { sc.SearchId, sc.EstateGroupId });

        builder
            .HasOne(sc => sc.Search)
            .WithMany(s => s.SearchesEstateGroups)
            .HasForeignKey(sc => sc.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sc => sc.EstateGroup)
            .WithMany(c => c.SearchesEstateGroups)
            .HasForeignKey(sc => sc.EstateGroupId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}