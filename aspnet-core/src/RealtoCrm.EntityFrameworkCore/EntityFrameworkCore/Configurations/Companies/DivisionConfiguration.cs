namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;
using static ModelConstants.Common;

internal class DivisionConfiguration : IEntityTypeConfiguration<Division>
{
    public void Configure(EntityTypeBuilder<Division> builder)
    {
        builder
            .HasKey(r => r.Id);

        builder
            .Property(r => r.Name)
            .HasMaxLength(MaxNameLength)
            .IsRequired();

        builder
            .Property(r => r.IsFranchise)
            .HasDefaultValue(false)
            .IsRequired();

        builder
            .Property(t => t.IsActive)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .HasOne(o => o.Company)
            .WithMany(r => r.Divisions)
            .HasForeignKey(o => o.CompanyId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(p => p.Tenant)
            .WithMany(t => t.Divisions)
            .HasForeignKey(p => p.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}