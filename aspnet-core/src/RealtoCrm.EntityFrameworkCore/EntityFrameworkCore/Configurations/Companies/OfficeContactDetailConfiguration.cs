namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;
using static ModelConstants.Common;

internal class OfficeContactDetailConfiguration : IEntityTypeConfiguration<OfficeContactDetail>
{
    public void Configure(EntityTypeBuilder<OfficeContactDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(od => od.Id);

        builder
            .Property(dd => dd.Value)
            .HasMaxLength(MaxUrlLength)
            .IsRequired();

        builder
            .HasOne(od => od.Office)
            .WithMany(o => o.ContactDetails)
            .HasForeignKey(od => od.OfficeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(od => od.ContactDetail)
            .WithMany()
            .HasForeignKey(od => od.ContactDetailId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}