using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

public class OfficeMappingConfiguration : IEntityTypeConfiguration<OfficeMapping>
{
    public void Configure(EntityTypeBuilder<OfficeMapping> builder)
    {
        builder.HasKey(pm => pm.Id);
        
        builder
            .HasOne(o => o.Office)
            .WithMany()
            .HasForeignKey(o => o.OfficeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pm => pm.AsiId)
            .IsRequired(false);
        
        builder.Property(pm => pm.EaId)
            .IsRequired(false);

        builder.Property(pm => pm.AdminId)
            .IsRequired(false);
    }
}