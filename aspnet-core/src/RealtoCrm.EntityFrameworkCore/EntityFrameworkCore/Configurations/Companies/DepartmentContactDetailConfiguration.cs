namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;
using static ModelConstants.Common;

internal class DepartmentContactDetailConfiguration : IEntityTypeConfiguration<DepartmentContactDetail>
{
    public void Configure(EntityTypeBuilder<DepartmentContactDetail> builder)
    {
        builder
            .<PERSON><PERSON><PERSON>(dd => dd.Id);

        builder
            .Property(dd => dd.Value)
            .HasMaxLength(MaxUrlLength)
            .IsRequired();

        builder
            .HasOne(dd => dd.Department)
            .WithMany(d => d.ContactDetails)
            .HasForeignKey(dd => dd.DepartmentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(dd => dd.ContactDetail)
            .WithMany()
            .HasForeignKey(dd => dd.ContactDetailId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}