using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    
    /// <inheritdoc />
    public partial class AddIndexOnDepositsSearchId : Migration
    {
        
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_deposits_searchid_creationtime
                ON ""Deposits"" (""SearchId"", ""CreationTime"" DESC)
                WHERE ""IsDeleted"" = FALSE;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                DROP INDEX IF EXISTS idx_deposits_searchid_creationtime;
            ");
        }
    }
}
