namespace RealtoCrm.EntityFrameworkCore.UnitOfWork.Implementation;

using System;
using System.Threading.Tasks;
using System.Transactions;
using Abp.Domain.Uow;

public class TransactionExecutor(IUnitOfWorkManager unitOfWorkManager)
    : ITransactionExecutor
{
    public Task RunInTransactionAsync(Func<Task> action)
        => this.RunInTransactionAsync(action, null);

    public async Task RunInTransactionAsync(Func<Task> action, TransactionScopeOption? scope)
    {
        using var uow = scope == null
            ? unitOfWorkManager.Begin()
            : unitOfWorkManager.Begin(scope.Value);

        await action();

        await uow.CompleteAsync();
    }

    public Task<T> RunInTransactionAsync<T>(Func<Task<T>> action)
        => this.RunInTransactionAsync(action, null);

    public async Task<T> RunInTransactionAsync<T>(Func<Task<T>> action, TransactionScopeOption? scope)
    {
        using var uow = scope == null
            ? unitOfWorkManager.Begin()
            : unitOfWorkManager.Begin(scope.Value);

        var result = await action();

        await uow.CompleteAsync();
        return result;
    }
}