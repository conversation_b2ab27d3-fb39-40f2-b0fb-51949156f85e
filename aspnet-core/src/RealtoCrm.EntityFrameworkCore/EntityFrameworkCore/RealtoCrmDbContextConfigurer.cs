using System;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;
namespace RealtoCrm.EntityFrameworkCore;

public static class RealtoCrmDbContextConfigurer
{
    private static readonly Action<NpgsqlDbContextOptionsBuilder> SqlServerOptions
        = sqlServer => sqlServer
            .UseQuerySplittingBehavior(QuerySplittingBehavior.SingleQuery)
            .MigrationsAssembly(typeof(RealtoCrmDbContext).Assembly.FullName);

    public static void Configure(DbContextOptionsBuilder<RealtoCrmDbContext> builder,
        string connectionString)
    {
        builder.UseNpgsql(connectionString, SqlServerOptions);
        // .AddDevDiagnostics();
    }

    public static void Configure(DbContextOptionsBuilder<RealtoCrmDbContext> builder,
        DbConnection connection)
    {
        builder.UseNpgsql(connection, SqlServerOptions);
        // .AddDevDiagnostics();
    }
}

public static class DbContextOptionsBuilderExtensions
{
    public static DbContextOptionsBuilder AddDevDiagnostics(this DbContextOptionsBuilder builder)
    {
        // enable extra diagnostics only in DEV
        builder.EnableSensitiveDataLogging()
            .EnableDetailedErrors();

        bool isDesignTime = AppDomain.CurrentDomain.GetAssemblies()
            .Any(a => a.FullName!.StartsWith("Microsoft.EntityFrameworkCore.Design", StringComparison.Ordinal));

        if (!isDesignTime) // runtime ✔ enabled   |   dotnet-ef ❌ disabled
        {
            builder.AddInterceptors(new PgInlineSqlInterceptor());
        }

        return builder;
    }
}

/// <summary>
/// DEV-ONLY helper – dumps every EF Core command with INLINED parameter
/// literals.  Each query is tagged with a sequential #id so you can match
/// the console output to the optional file log.
/// </summary>
public sealed class PgInlineSqlInterceptor : DbCommandInterceptor
{
    private const bool AlsoWriteToFile = true;
    private const string FilePath = "ef-inline.sql";

    private static readonly Regex Positional = new(@"(\$\d+)\b", RegexOptions.Compiled);
    private static readonly StreamWriter? FileWriter;
    private static long _seq;

    static PgInlineSqlInterceptor()
    {
        /* enable Unicode in Windows console */
        try { Console.OutputEncoding = Encoding.UTF8; }
        catch {}

        /* overwrite file each run */
        if (AlsoWriteToFile)
        {
            FileWriter = new StreamWriter(FilePath, append: false, Encoding.UTF8)
            {
                AutoFlush = true
            };
        }
    }

    /* ── sync hooks ── */
    public override InterceptionResult<DbDataReader> ReaderExecuting(
        DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result)
        => Tap(command, result);

    public override InterceptionResult<int> NonQueryExecuting(
        DbCommand command, CommandEventData eventData, InterceptionResult<int> result)
        => Tap(command, result);

    public override InterceptionResult<object> ScalarExecuting(
        DbCommand command, CommandEventData eventData, InterceptionResult<object> result)
        => Tap(command, result);

    /* ── async hooks ── */
    public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
        DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result,
        CancellationToken token = default)
        => new(Tap(command, result));

    public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
        DbCommand command, CommandEventData eventData, InterceptionResult<int> result,
        CancellationToken token = default)
        => new(Tap(command, result));

    public override ValueTask<InterceptionResult<object>> ScalarExecutingAsync(
        DbCommand command, CommandEventData eventData, InterceptionResult<object> result,
        CancellationToken token = default)
        => new(Tap(command, result));

    /* ───────────────── helper ───────────────── */
    private static T Tap<T>(DbCommand cmd, T passthrough)
    {
        Dump(cmd); // log
        return passthrough; // give EF the original result
    }

    private static void Dump(DbCommand cmd)
    {
        long id = Interlocked.Increment(ref _seq);
        string tag = $"#{id:00000}";
        string flat = ToSingleLine(BuildSqlWithLiterals(cmd));

        /* ①  write to console (header + preview) */
        Console.ForegroundColor = ConsoleColor.Cyan;
        Console.Write($"{tag} ");
        Console.ResetColor();
        Console.WriteLine(flat.Length > 120 ? flat[..120] + " …" : flat);

        /* ②  write full text to file */
        if (AlsoWriteToFile && FileWriter is not null)
        {
            FileWriter.WriteLine($"-- {tag} {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            FileWriter.WriteLine(flat);
            FileWriter.WriteLine();
        }
    }

    /// <summary>
    /// Turns the multi-line SQL into a single line that is easy to copy-paste
    /// without changing the statement’s semantics.
    /// </summary>
    private static string ToSingleLine(string multiLine)
    {
        // 1️⃣  Replace every hard line-break with a single space
        var flat = multiLine.Replace("\r", " ")
            .Replace("\n", " ");

        // 2️⃣  Collapse runs of 2+ spaces down to one
        flat = Regex.Replace(flat, @"\s{2,}", " ");

        // 3️⃣  Trim leading / trailing spaces
        return flat.Trim();
    }

    /* inject literals */
    private static string BuildSqlWithLiterals(DbCommand cmd)
    {
        string sql = cmd.CommandText;

        for (int i = 0; i < cmd.Parameters.Count; i++)
        {
            var p = cmd.Parameters[i];
            sql = Positional.Replace(sql,
                m => m.Value == $"${i + 1}" ? Literal(p) : m.Value);
        }

        foreach (DbParameter p in cmd.Parameters)
            sql = sql.Replace(p.ParameterName, Literal(p));

        return sql;
    }

    /* param → PostgreSQL literal */
    private static string Literal(DbParameter p) => p.DbType switch
    {
        DbType.String or DbType.AnsiString or DbType.AnsiStringFixedLength or DbType.StringFixedLength
            => $"'{p.Value.ToString().Replace("'", "''")}'",

        DbType.DateTime or DbType.DateTime2 or DbType.Date
            => $"'{Convert.ToDateTime(p.Value):yyyy-MM-dd HH:mm:ss.fffffff}'",

        DbType.DateTimeOffset => p.Value switch
        {
            DateTimeOffset dto => $"'{dto:yyyy-MM-dd HH:mm:ss.fffffffzzz}'",
            DateTime dt => $"'{new DateTimeOffset(dt):yyyy-MM-dd HH:mm:ss.fffffffzzz}'",
            _ => "NULL"
        },

        DbType.Boolean => (bool) p.Value ? "TRUE" : "FALSE",
        DbType.Guid => $"'{p.Value}'::uuid",
        DbType.Binary => $"'\\x{BitConverter.ToString((byte[]) p.Value).Replace("-", "")}'::bytea",
        _ => p.Value?.ToString() ?? "NULL"
    };
}