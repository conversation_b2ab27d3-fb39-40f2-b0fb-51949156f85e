namespace RealtoCrm.Authorization;

/// <summary>
/// Defines string constants for application's permission names.
/// <see cref="AppAuthorizationProvider"/> for permission definitions.
/// </summary>
public static class AppPermissions
{
    public const string AdministrativeDivisionsRead = "Administrative.Divisions.Read";
    public const string AdministrativeDivisionsUpdate = "Administrative.Divisions.Update";
    public const string AdministrativeDivisionsCreate = "Administrative.Divisions.Create";
    public const string AdministrativeDivisionsDelete = "Administrative.Divisions.Delete";
    public const string EstateRead = "Estate.Read";
    public const string EstateUpdate = "Estate.Update";
    public const string EstateCreate = "Estate.Create";
    public const string EstateDelete = "Estate.Delete";
    public const string TeamRead = "Team.Read";
    public const string TeamUpdate = "Team.Update";
    public const string TeamCreate = "Team.Create";
    public const string TeamDelete = "Team.Delete";
    public const string MarketingRead = "Marketing.Read";
    public const string MarketingUpdate = "Marketing.Update";
    public const string MarketingCreate = "Marketing.Create";
    public const string MarketingDelete = "Marketing.Delete";
    public const string GeneralSettingsRead = "GeneralSettings.Read";
    public const string GeneralSettingsUpdate = "GeneralSettings.Update";
    public const string GeneralSettingsCreate = "GeneralSettings.Create";
    public const string GeneralSettingsDelete = "GeneralSettings.Delete";
    public const string OfferRead = "Offer.Read";
    public const string OfferUpdate = "Offer.Update";
    public const string OfferCreate = "Offer.Create";
    public const string OfferChangeStatus = "Offer.Change.Status";
    public const string OfferPublishToWebsites = "Offer.Publish.To.Websites";
    public const string OfferPublishingAuthorize = "Offer.Publishing.Authorize";
    public const string OfferUnpublishToWebsites = "Offer.Unpublish.To.Websites";
    public const string OfferEstateLocationVerification = "Offer.Estate.Location.Verification";
    public const string OfferEstateAddressVerification = "Offer.Estate.Address.Verification";
    public const string OfferSaveToFavourites = "Offer.Save.To.Favourites";
    public const string OfferSendOverViber = "Offer.Send.Over.Viber";
    public const string OfferSendOverEmail = "Offer.Send.Over.Email";
    public const string OfferSendOverWhatsapp = "Offer.Send.Over.Whatsapp";
    public const string OfferCreatePicturesFor = "Offer.Create.Pictures.For";
    public const string OfferUpdatePicturesFor = "Offer.Update.Pictures.For";
    public const string OfferTeamAccess = "Offer.Team.Access";
    public const string OfferArchiveAccess = "Offer.Archive.Access";
    public const string OfferFreeAccess = "Offer.Free.Access";
    public const string OfferArchiveAction = "Offer.Archive.Action";
    public const string OfferUploadContract = "Offer.UploadContract.Action";
    public const string OfferOfferToEmployeeAction = "Offer.OfferToEmployee.Action";
    public const string OfferTransferToEmployeeAction = "Offer.TransferToEmployee.Action";
    public const string OfferGenerateSpaAction = "Offer.GenerateSpa.Action";
    public const string OfferChangeOwnerAction = "Offer.ChangeOwner.Action";
    public const string OfferAddEncumbrancesAction = "Offer.AddEncumbrances.Action";
    public const string EstateChangeOwner = "Estate.Change.Owner";
    public const string EstateHistoryRead = "Estate.History.Read";
    public const string EstateHistoryUpdate = "Estate.History.Update";
    public const string ContactView = "Contact.View";
    public const string ContactAllAccess = "Contact.All.Access";
    public const string ContactUpdate = "Contact.Update";
    public const string ContactCreate = "Contact.Create";
    public const string ContactBrokerAssign = "Contact.Broker.Assign";
    public const string ContactSoftDelete = "Contact.Soft.Delete";
    public const string ContactTeamAccess = "Contact.Team.Access";
    public const string SearchRead = "Search.Read";
    public const string SearchCreate = "Search.Create";
    public const string SearchUpdate = "Search.Update";
    public const string SearchChangeStatus = "Search.Change.Status";
    public const string SearchAddContract = "Search.Add.Contract";
    public const string SearchAssignTo = "Search.Assign.To";
    public const string SearchTeamAccess = "Search.Team.Access";
    public const string SearchArchiveAccess = "Search.Archive.Access";
    public const string SearchAllAccess = "Search.All.Access";
    public const string SearchesHistoryRead = "Searches.History.Read";
    public const string BuildingProjectRead = "Building.Project.Read";
    public const string BuildingProjectCreate = "Building.Project.Create";
    public const string BuildingProjectUpdate = "Building.Project.Update";
    public const string BuildingProjectAddContract = "Building.Project.Add.Contract";
    public const string BuildingProjectChangeOwner = "Building.Project.Change.Owner";
    public const string BuildingProjectAssignTo = "Building.Project.Assign.To";
    public const string SPACreate = "SPA.Create";
    public const string SPAUpdate = "SPA.Update";
    public const string SPAViewPDF = "SPA.View.PDF";
    public const string SPADownloadPDF = "SPA.Download.PDF";
    public const string NomenclaturesRead = "Nomenclatures.Read";
    public const string NomenclaturesUpdate = "Nomenclatures.Update";
    public const string NomenclaturesCreate = "Nomenclatures.Create";
    public const string NomenclaturesDelete = "Nomenclatures.Delete";
    public const string MatchRead = "Match.Read";
    public const string MatchUpdate = "Match.Update";
    public const string MatchCreate = "Match.Create";
    public const string MatchDelete = "Match.Delete";
    public const string ProjectRead = "Project.Read";
    public const string ProjectUpdate = "Project.Update";
    public const string ProjectCreate = "Project.Create";
    public const string ProjectDelete = "Project.Delete";
    public const string FilterSaveToFavourites = "Filter.Save.To.Favourites";
    public const string TaskRead = "Task.Read";
    public const string TaskUpdate = "Task.Update";
    public const string TaskCreate = "Task.Create";
    public const string TaskAssign = "Task.Assign";
    public const string TaskDone = "Task.Done";
    public const string TaskManagerApproved = "Task.Manager.Approved";
    public const string NoteCreate = "Note.Create";
    public const string NoteRead = "Note.Read";
    public const string NoteUpdate = "Note.Update";
    public const string NoteDelete = "Note.Delete";
    public const string ViewingCreate = "Viewing.Create";
    public const string ViewingRead = "Viewing.Read";
    public const string ViewingUpdate = "Viewing.Update";
    public const string ViewingChangeStatus = "Viewing.Change.Status";
    public const string ViewingAddRecord = "Viewing.Add.Record";
    public const string MeetingCreate = "Meeting.Create";
    public const string MeetingRead = "Meeting.Read";
    public const string MeetingUpdate = "Meeting.Update";
    public const string MeetingChangeStatus = "Meeting.Change.Status";
    public const string DepositRead = "Deposit.Read";
    public const string DepositUpdate = "Deposit.Update";
    public const string DepositCreate = "Deposit.Create";
    public const string DepositChangeStatus = "Deposit.Change.Status";
    public const string DepositAssignToAnotherOffer = "Deposit.Assign.To.Another.Offer";
    public const string DepositAddDocument = "Deposit.Add.Document";
    public const string DepositActions = "Deposit.Actions";
    public const string DealCreate = "Deal.Create";
    public const string DealUpdate = "Deal.Update";
    public const string DealRead = "Deal.Read";
    public const string DealChangeStatus = "Deal.Change.Status";
    public const string DealAddCommissionPayment = "Deal.Add.Commision.Payment";
    public const string DealAddPreliminaryContract = "Deal.Add.Preliminary.Contract";
    public const string DealDoneWithTransaction = "Deal.Done.With.Transaction";
    public const string DealArchiveWithoutTransaction = "Deal.Archive.Without.Transaction";
    public const string DealAllSidesAccess = "Deal.AllSides.Access";
    public const string DealApprovalAction = "Deal.Approval.Action";
    public const string DealAnnulledAction = "Deal.Annulled.Action";
    public const string DealTeamAction = "Deal.Team.Action";
    public const string DealDepartmentAction = "Deal.Department.Action";
    public const string DealDivisionAction = "Deal.Division.Action";
    public const string DealCompanyAction = "Deal.Company.Action";
    public const string NotificationsRead = "Notifications.Read";
    public const string NotificationsUpdate = "Notifications.Update";
    public const string NotificationsClear = "Notifications.Clear";
    public const string KanbanPieChartRead = "Kanban.Pie.Chart.Read";
    public const string KanbanPieChartUpdate = "Kanban.Pie.Chart.Update";
    public const string KanbanPieChartCreate = "Kanban.Pie.Chart.Create";
    public const string UserCreate = "User.Create";
    public const string UserUpdate = "User.Update";
    public const string UserDelete = "User.Delete";
    public const string UserRead = "User.Read";
    public const string UserImpersonate = "User.Impersonate";
    public const string UserPhoneNumberRead = "User.Phonenumber.Read";
    public const string UserRoleCreate = "User.Role.Create";
    public const string UserRoleUpdate = "User.Role.Update";
    public const string UserRoleDelete = "User.Role.Delete";
    public const string UserRoleRead = "User.Role.Read";
    public const string UserRoleCompanyFilter = "User.Role.CompanyFilter";
    public const string EmployeeCreate = "Employee.Create";
    public const string EmployeeUpdate = "Employee.Update";
    public const string EmployeeDelete = "Employee.Delete";
    public const string EmployeeRead = "Employee.Read";
    public const string TrainingsRead = "Trainings.Read";
    public const string TrainingsUpdate = "Trainings.Update";
    public const string TrainingsDelete = "Trainings.Delete";
    public const string RankingListRead = "Ranking.List.Read";
    public const string CallsLogRead = "Calls.Log.Read";
    public const string DashboardRead = "Dashboard.Read";
    public const string DashboardSetting = "Dashboard.Setting";
    public const string ProfilePictureCreate = "Profile.Picture.Create";
    public const string ProfilePictureUpdate = "Profile.Picture.Update";
    public const string DisplayNameCreate = "Display.Name.Create";
    public const string DisplayNameUpdate = "Display.Name.Update";
    public const string SetDefaultCity = "Set.Default.City";
    public const string SetDefaultListSize = "Set.Default.List.Size";
    public const string SetDefaultTheme = "Set.Default.Theme";
    public const string CallCenterSurveyCreate = "Call.Center.Survey.Create";
    public const string CallCenterSurveyUpdate = "Call.Center.Survey.Update";
    public const string InquiryCreate = "Inquiry.Create";
    public const string InquiryUpdate = "Inquiry.Update";
    public const string InquiryRead = "Inquiry.Read";
    public const string InquiryDelete = "Inquiry.Delete";
    public const string InquirySendToEmail = "Inquiry.Send.To.Email";
    public const string ContactsMerge = "Contacts.Merge";
    public const string TargetSetForTeam = "Target.Set.For.Team";
    public const string TargetSetForExternalExport = "Target.Set.For.External.Export";
    public const string OfferTeamEdit = "Offer.Team.Edit";
    public const string OfferDepartmentEdit = "Offer.Department.Edit";
    public const string OfferDivisionEdit = "Offer.Division.Edit";
    public const string AdministrationFullAccess = "Administration.FullAccess";
    public const string OfferDelete = "Offer.Delete";
    public const string DocumentsRead = "Documents.Read";
    public const string DocumentsUpdate = "Documents.Update";
    public const string DocumentsCreate = "Documents.Create";
    public const string DocumentsDelete = "Documents.Delete";
    public const string LinkedProjectsCreate = "LinkedProjects.Create";
    public const string OfferAllAccess = "Offer.All.Access";
    public const string SearchFreeAccess = "Search.Free.Access";
    public const string SearchTransferTo = "Search.TransferTo.Action";
    public const string SearchChangeOwner = "Search.ChangeOwner.Action";
    public const string SearchArchive = "Search.Archive.Action";
    public const string SearchActivate = "Search.Activate.Action";
    public const string SearchDelete = "Search.Delete";
    public const string SearchTeamEdit = "Search.Team.Edit";
    public const string SearchDepartmentEdit = "Search.Department.Edit";
    public const string SearchDivisionEdit = "Search.Division.Edit";
    public const string DealConsultantAccess = "Deal.Consultant.Access";
    public const string ContactCreateSearch = "Contact.Create.Search";
    public const string ContactCreateOffer = "Contact.Create.Offer";
    public const string ContactCreateProject = "Contact.Create.Project";
    public const string ContactUploadDocument = "Contact.Upload.Document";
    public const string ContactActionsTeam = "Contact.Actions.Team";
    public const string ContactActionsDepartment = "Contact.Actions.Department";
    public const string ContactActionsDivision = "Contact.Actions.Division";
    public const string PhonebookRead = "Phonebook.Read";
}