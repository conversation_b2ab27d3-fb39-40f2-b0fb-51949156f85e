namespace RealtoCrm;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;

public abstract class Specification<T>
where T: class
{
    private static readonly ConcurrentDictionary<string, Func<T, bool>> DelegateCache = new();

    private readonly List<string> cacheKey;

    protected Specification()
        => this.cacheKey = new List<string> { this.GetType().Name };

    protected virtual bool Include => true;

    public static implicit operator Expression<Func<T, bool>>(Specification<T> specification)
        => specification.Include
            ? specification.ToExpression()
            : value => true;

    public abstract Expression<Func<T, bool>> ToExpression();

    public virtual bool IsSatisfiedBy(T value)
    {
        if (!this.Include)
        {
            return true;
        }

        var func = DelegateCache.GetOrAdd(
            string.Join(string.Empty, this.cacheKey),
            _ => this.ToExpression().Compile());

        return func(value);
    }

    public Specification<T> And(Specification<T> specification)
    {
        if (!specification.Include)
        {
            return this;
        }

        this.cacheKey.Add($"{nameof(this.And)}{specification.GetType()}");

        return new BinarySpecification(this, specification, true);
    }

    public Specification<T> Or(Specification<T> specification)
    {
        if (!specification.Include)
        {
            return this;
        }

        this.cacheKey.Add($"{nameof(this.Or)}{specification.GetType()}");

        return new BinarySpecification(this, specification, false);
    }
    
    public virtual string ToSql(DbContext dbContext)
    {
        // Create a dummy query using the specification
        var query = dbContext.Set<T>().Where(this.ToExpression());
        
        // Get the SQL using EF Core's ToQueryString method
        var sql = query.ToQueryString();
        
        // Extract just the WHERE clause (optional, depending on your needs)
        var whereClauseMatch = Regex.Match(sql, @"WHERE\s+(.+)(?:\s+ORDER BY|\s+$)", RegexOptions.IgnoreCase | RegexOptions.Singleline);
        
        if (whereClauseMatch.Success)
        {
            return whereClauseMatch.Groups[1].Value.Trim();
        }
        
        return sql;
    }

    private sealed class BinarySpecification : Specification<T>
    {
        private readonly Specification<T> left;
        private readonly Specification<T> right;
        private readonly bool andOperator;

        public BinarySpecification(
            Specification<T> left,
            Specification<T> right,
            bool andOperator)
        {
            this.left = left;
            this.right = right;
            this.andOperator = andOperator;
        }

        public override Expression<Func<T, bool>> ToExpression()
        {
            Expression<Func<T, bool>> leftExpression = this.left;
            Expression<Func<T, bool>> rightExpression = this.right;

            var parameter = leftExpression.Parameters;

            var invokedExpression = Expression.Invoke(rightExpression, parameter);

            Expression body = this.andOperator
                ? Expression.AndAlso(leftExpression.Body, invokedExpression)
                : Expression.OrElse(leftExpression.Body, invokedExpression);

            return Expression.Lambda<Func<T, bool>>(body, parameter);
        }
    }
}