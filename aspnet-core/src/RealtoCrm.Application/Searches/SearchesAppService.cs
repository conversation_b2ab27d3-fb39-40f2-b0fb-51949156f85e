namespace RealtoCrm.Searches;

using System;
using System.Collections.Generic;
using System.Linq;
using Abp.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Abp;
using Abp.Authorization;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Abp.UI;
using Authorization;
using Calls;
using Clients;
using CommentsTasks;
using Common.Attributes;
using Common.Extensions;
using Common.Models;
using Configuration;
using Contracts;
using Deals;
using Employees;
using EntityFrameworkCore;
using Models;
using Expressions;
using Microsoft.AspNetCore.Mvc;
using Models.Permissions;
using Nomenclatures.SearchStatuses;
using Notifications;
using Offers;
using Offers.Models;
using Providers;
using Specifications;
using static CosherConsts.SearchStatuses;

public class SearchesAppService(
    IDateTimeService dateTimeService,
    ICallsAppService callsAppService,
    IOffersAppService offersAppService,
    IContractsAppService contractsAppService,
    IEmployeesAppService employeesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder,
    IAppNotifier appNotifier,
    ISearchStatusesAppService searchStatusesService,
    IPermissionChecker permissionChecker,
    ISearchesPermissionsChecker searchesPermissionsChecker,
    IClientsPermissionsChecker clientsPermissionsChecker)
    : CommentsTasksAppService<
        int,
        Search,
        SearchComment,
        SearchTask,
        SearchCreateRequestModel,
        SearchUpdateRequestModel,
        SearchesPaginatedRequestModel,
        SearchResponseModel,
        SearchListingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder), ISearchesAppService
{
    private const int DealSearchStatusId = 3;
    private const int ActiveSearchStatusId = 1;
    private const int RegisteredSearchPastYears = 3;

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.SearchCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.SearchUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.SearchUpdate)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.SearchRead)
            .ConfigureAuthorize(nameof(this.ArchiveSearchAsync), AppPermissions.SearchArchive)
            .AddCustomAttribute(nameof(this.ArchiveSearchAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetClientRelatedSearchAsync))
            .ConfigureAuthorize(nameof(this.GetEmployeeSearchPermissionsAsync));

    public override async Task<int> CreateAsync(SearchCreateRequestModel request)
    {
        return await CreateSearchAsync(request);
    }
    
    public async Task<int> CreateSearchForClientAsync(SearchCreateRequestModel request)
    {
        if(!await clientsPermissionsChecker.CanPerformAction(
               request.ClientId,
               this.AbpSession.GetUserId(),
               [AppPermissions.ContactCreateSearch]
           ))
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }
        
        return await CreateSearchAsync(request);
    }

    public async Task<IEnumerable<SearchResponseModel>> GetClientRelatedSearchAsync(int clientId)
        => await this.ObjectMapper
            .ProjectTo<SearchResponseModel>(this
                .AllAsNoTracking()
                .Where(o => o.ClientId == clientId)
                .OrderByDescending(x => x.CreationTime))
            .ToListAsync();

    public async Task<IEnumerable<SearchShortListingResponseModel?>> GetLastActiveSearchForClientsAsync(
        IEnumerable<int> clientIds)
    {
        var searches = await this.ObjectMapper.ProjectTo<SearchShortListingResponseModel>(this.AllAsNoTracking()
                .Where(x => clientIds.Contains(x.ClientId) && x.SearchStatusId == ActiveSearchStatusId))
            .ToListAsync();

        return searches.GroupBy(x => x.ClientId)
            .Select(g => g.MaxBy(s => s.LastModificationTime))
            .ToList();
    }

    public async Task<SearchActivityResponseModel?> GetActivityAsync(int searchId)
        => await this.ObjectMapper
            .ProjectTo<SearchActivityResponseModel>(this.AllAsNoTracking()
                .Where(o => o.Id == searchId))
            .FirstOrDefaultAsync();


    public async Task TransferToEmployeeAsync(TransferToEmployeeRequestModel request)
    {
        var search = this.Data.Searches
            .Include(o => o.Client)
            .ThenInclude(c => c.ClientsSourceCategories)
            .FirstOrDefault(x => x.Id == request.EntityId);
        
        if(!await searchesPermissionsChecker.CanPerformAction(
               search.Id,
               this.AbpSession.GetUserId(),
               [AppPermissions.SearchActivate]
           ))
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var newEmployee = this.Data.Employees
            .Include(employee => employee.UserAccount)
            .FirstOrDefault(e => e.Id == request.NewEmployeeId);

        if (newEmployee is null)
        {
            throw new UserFriendlyException($"Employee with ID: {request.NewEmployeeId} does not exists.");
        }

        if (search is null)
        {
            throw new UserFriendlyException($"Offer with ID: {request.EntityId} does not exists.");
        }

        await this.AddConnectionBetweenEmployeeAndClientAsync(search, request.NewEmployeeId);

        search.EmployeeId = request.NewEmployeeId;
        await this.Data.SaveChangesAsync();
        
        await this.SendTransferredSearchNotificationAsync(
            search.Id,
            newEmployee.UserAccount.TenantId!.Value,
            newEmployee.UserAccount.UserId);
    }

    public async Task ArchiveSearchAsync(ArchiveRequestModel request)
    {
        if(!await searchesPermissionsChecker.CanPerformAction(
                request.EntityId,
                this.AbpSession.GetUserId(),
                [AppPermissions.SearchArchive]
           ))
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var search = await this.GetByIdAsync(request.EntityId);
        search.SearchStatusId = await searchStatusesService.GetIdByNameAsync(ArchivedSearchStatusName);

        this.ObjectMapper.Map(request, search);
        await this.Data.SaveChangesAsync();
    }

    public async Task ActivateSearchAsync(int searchId)
    {
        var search = await this.GetByIdAsync(searchId);
        
        if(!await searchesPermissionsChecker.CanPerformAction(
               search.Id,
               this.AbpSession.GetUserId(),
               [AppPermissions.SearchActivate]
           ))
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        search.SearchStatusId = await searchStatusesService.GetIdByNameAsync(ActiveSearchStatusName);
        search.ArchiveReasonId = null;
        search.ArchiveDate = null;

        await this.Data.SaveChangesAsync();
    }

    public async Task ChangeSearchOwnerAsync(ChangeOwnerRequestModel request)
    {
        var search = await this.Data.Searches
            .Include(s => s.Client)
            .ThenInclude(c => c.ClientsSourceCategories)
            .FirstOrDefaultAsync(x => x.Id == request.EntityId);

        if (search is null)
        {
            throw new UserFriendlyException($"Search with id {request.EntityId} not found.");
        }
        
        if(!await searchesPermissionsChecker.CanPerformAction(
               search.Id,
               this.AbpSession.GetUserId(),
               [AppPermissions.SearchChangeOwner]
           ))
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        search.ClientId = request.ClientId;

        await this.Data.SaveChangesAsync();
        
        await this.Data.Entry(search)
            .Reference(o => o.Client)
            .LoadAsync();

        await this.Data.Entry(search.Client)
            .Collection(c => c.ClientsSourceCategories)
            .LoadAsync();

        await this.AddConnectionBetweenEmployeeAndClientAsync(search, search.EmployeeId!.Value);
    }

    [HttpPost]
    public async Task<IEnumerable<SearchesForDealResponseModel>> GetAllForDealCreationAsync(
        SearchesForDealRequestModel request)
        => await this.ObjectMapper
            .ProjectTo<SearchesForDealResponseModel>(this
                .AllAsNoTracking()
                .Where(new SearchByIdSpecification(request.Id)
                    .And(new SearchBySearchTypeSpecification(request.Type))
                    .And(new SearchByEmployeeIdSpecification(request.EmployeeId))
                    .And(new SearchByOwnershipStatusSpecification(request.OwnershipStatus, this.AbpSession.UserId)))
                .Where(this.BuildTenantSpecification())
                .OrderBy(s => s.Id)
                .Take(request.PageSize))
            .ToListAsync();

    protected override IQueryable<Search> BuildDetailsQuery(int id)
        => this
            .AllAsNoTracking()
            .AsSplitQuery()
            .Where(n => n.Id.Equals(id));

    public async Task<SearchEmployeePermissionsResponseModel> GetEmployeeSearchPermissionsAsync(int clientId)
    {
        var permissions = new SearchEmployeePermissionsResponseModel();

        var currentEmployeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var registeredOffersForPastYears = await offersAppService.GetClientRegisteredSellOffersAsync(
            clientId,
            currentEmployeeId);

        var registeredSearchesForPastYears = await this.GetClientRegisteredBuySearchesAsync(
            clientId,
            currentEmployeeId);

        var registeredOffersEmployeeIds = registeredOffersForPastYears
            .Select(o => o.EmployeeId)
            .ToList();

        var registeredSearchesEmployeeIds = registeredSearchesForPastYears
            .Select(o => o.EmployeeId)
            .ToList();

        var employeesIds = registeredOffersEmployeeIds.Concat(registeredSearchesEmployeeIds);

        var hasRegisteredCallForPastMonths = await callsAppService.HasClientCallsWithEmployeesAsync(
            clientId,
            employeesIds);

        var daysForExclusiveContractString = await this.SettingManager.GetSettingValueAsync(
            AppSettings.UiManagement.PotentialContacts
                .ReleaseSearchDaysForExclusiveContractForConsultingActivityFiledPotentialsCorporateClientsSource);

        var daysForExclusiveContract = int.Parse(daysForExclusiveContractString);

        if (registeredOffersForPastYears.Any() &&
            registeredSearchesForPastYears.Any() &&
            hasRegisteredCallForPastMonths)
        {
            return await this.HasSearchRentPermissions(
                permissions,
                clientId,
                currentEmployeeId,
                daysForExclusiveContract);
        }

        return await this.HasSearchBuyPermissions(
            permissions,
            clientId,
            currentEmployeeId,
            daysForExclusiveContract);
    }

    private async Task AddConnectionBetweenEmployeeAndClientAsync(Search search, int employeeId)
    {
        if (search.Client.ClientsSourceCategories.Any(x => x.EmployeeId == employeeId))
        {
            return;
        }

        var relationWithPreviousClient =
            search.Client.ClientsSourceCategories.First(csc => csc.EmployeeId == search.EmployeeId);
        search.Client.ClientsSourceCategories.Add(new ClientSourceCategory
        {
            EmployeeId = employeeId,
            SourceCategoryId = relationWithPreviousClient.SourceCategoryId,
        });

        await this.Data.SaveChangesAsync();
    }

    private async Task<Search> GetByIdAsync(int searchId)
    {
        var search = await this.GetAsync(searchId);

        if (search is null)
        {
            throw new UserFriendlyException($"Search with id {searchId} not found.");
        }

        return search;
    }

    private async Task<SearchEmployeePermissionsResponseModel> HasSearchRentPermissions(
        SearchEmployeePermissionsResponseModel permissions,
        int clientId,
        int? currentEmployeeId,
        int daysForExclusiveContract)
    {
        var activeRentSearches = await this.GetClientActiveRentSearchesAsync(clientId, currentEmployeeId);

        if (!activeRentSearches.Any())
        {
            permissions.CanCreateBuySearch = false;
            permissions.CanCreateRentSearch = true;

            return permissions;
        }

        foreach (var activeRentSearch in activeRentSearches)
        {
            permissions.CanCreateRentSearch = true;
            permissions.CreateRentSearchWithoutEstateCategoryId = activeRentSearch.EstateCategoryId;

            var registerSearchRentDaysString = await this.SettingManager.GetSettingValueAsync(
                AppSettings.UiManagement.PotentialContacts.RegisterSearchRentDays);

            var registerSearchRentDays = int.Parse(registerSearchRentDaysString);

            var daysSinceActiveRentSearchCreation = dateTimeService.Now.Subtract(activeRentSearch.CreationTime).Days;

            if (daysSinceActiveRentSearchCreation > registerSearchRentDays)
            {
                var contract = await contractsAppService.GetCreationDateByClientAndSearchAsync(
                    clientId,
                    activeRentSearch.Id);

                if (contract is null)
                {
                    permissions.CreateRentSearchWithoutEstateCategoryId = null;
                }
                else
                {
                    var contractCreationTime = contract.CreationTime;
                    var daysSinceContractCreation = dateTimeService.Now.Subtract(contractCreationTime).Days;

                    var hasCallAfterContract = await callsAppService.HasCallAfterContractDateAsync(
                        clientId,
                        activeRentSearch.EmployeeId,
                        contractCreationTime);

                    var hasViewingAfterContract = await this.HasViewingAfterContractDateAsync(
                        clientId,
                        activeRentSearch.Id,
                        contractCreationTime);

                    if (daysSinceContractCreation > daysForExclusiveContract &&
                        hasCallAfterContract &&
                        hasViewingAfterContract)
                    {
                        permissions = await this.HasSearchBuyPermissions(
                            permissions,
                            clientId,
                            currentEmployeeId,
                            daysForExclusiveContract);

                        permissions.CanCreateRentSearch = true;
                        permissions.CreateRentSearchWithoutEstateCategoryId = activeRentSearch.EstateCategoryId;

                        return permissions;
                    }
                }
            }
        }

        return permissions;
    }

    private async Task<SearchEmployeePermissionsResponseModel> HasSearchBuyPermissions(
        SearchEmployeePermissionsResponseModel permissions,
        int clientId,
        int? currentEmployeeId,
        int daysForExclusiveContract)
    {
        var activeBuySearches = await this.GetClientActiveBuySearchesAsync(clientId, currentEmployeeId);

        if (!activeBuySearches.Any())
        {
            permissions.CanCreateBuySearch = true;
            permissions.CanCreateRentSearch = true;

            return permissions;
        }

        foreach (var activeBuySearch in activeBuySearches)
        {
            var daysSinceActiveBuySearchCreation = dateTimeService.Now.Subtract(activeBuySearch.CreationTime).Days;

            var registerSearchBuyDaysString = await this.SettingManager.GetSettingValueAsync(
                AppSettings.UiManagement.PotentialContacts.RegisterSearchBuyDays);

            var registerSearchBuyDays = int.Parse(registerSearchBuyDaysString);

            if (daysSinceActiveBuySearchCreation < registerSearchBuyDays)
            {
                permissions = await this.HasSearchRentPermissions(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForExclusiveContract);

                permissions.CanCreateBuySearch = false;

                return permissions;
            }

            if (activeBuySearch.Immunity)
            {
                permissions = await this.HasSearchRentPermissions(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForExclusiveContract);

                permissions.CanCreateBuySearch = true;
                permissions.CreateBuySearchWithoutEstateCategoryId = activeBuySearch.EstateCategoryId;

                return permissions;
            }

            var clientContract = await contractsAppService.GetCreationDateByClientAndSearchAsync(
                clientId,
                activeBuySearch.Id);

            if (clientContract is null)
            {
                permissions = await this.HasSearchRentPermissions(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForExclusiveContract);

                permissions.CanCreateBuySearch = true;

                return permissions;
            }

            var contractCreationTime = clientContract.CreationTime;
            var daysSinceContractCreation = dateTimeService.Now.Subtract(contractCreationTime).Days;

            var hasCallAfterContract = await callsAppService.HasCallAfterContractDateAsync(
                clientId,
                activeBuySearch.EmployeeId,
                contractCreationTime);

            var hasViewingAfterContract = await this.HasViewingAfterContractDateAsync(
                clientId,
                activeBuySearch.Id,
                contractCreationTime);

            permissions = await this.HasSearchRentPermissions(
                permissions,
                clientId,
                currentEmployeeId,
                daysForExclusiveContract);

            permissions.CanCreateBuySearch = true;

            if (daysSinceContractCreation > daysForExclusiveContract && hasCallAfterContract && hasViewingAfterContract)
            {
                permissions.CreateBuySearchWithoutEstateCategoryId = activeBuySearch.EstateCategoryId;

                return permissions;
            }
        }

        return permissions;
    }

    public override async Task<int> UpdateAsync(int id, SearchUpdateRequestModel updateRequest)
    {
        var entity = await this.GetAsync(id);

        if (entity is null)
        {
            throw new EntityNotFoundException($"Search with id '{id}' is not found.");
        }

        updateRequest.EmployeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        await this.DeleteRelatedSearches(entity.Id);
        await this.DeleteRelatedSourceDetails(entity.Id);

        this.ObjectMapper.Map(updateRequest, entity);

        await this.Data.SaveChangesAsync();

        return entity.Id;
    }

    public async Task<SearchClientEmployeeResponseModel?> GetClientAndEmployeeIdsAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<SearchClientEmployeeResponseModel>(this
                .AllAsNoTracking()
                .Where(s => s.Id == id))
            .FirstOrDefaultAsync();

    public async Task<SearchClientContractTypeResponseModel?> GetClientAndContractTypeIdsAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<SearchClientContractTypeResponseModel>(this
                .AllAsNoTracking()
                .Where(s => s.Id == id))
            .FirstOrDefaultAsync();

    public async Task<List<SearchClientRegisteredSearchResponseModel>> GetClientRegisteredBuySearchesAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<SearchClientRegisteredSearchResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(s =>
                    s.ClientId == clientId &&
                    s.EmployeeId != currentEmployeeId &&
                    s.Type == SearchType.Buy &&
                    s.SearchStatusId == DealSearchStatusId &&
                    s.Deals.Any(d =>
                        d.SearchId == s.Id &&
                        d.DealStatus == DealStatus.IsComplete &&
                        (d.EmployeeId == s.EmployeeId || d.OppositeSideEmployeeId == s.EmployeeId) &&
                        d.CreationTime >= dateTimeService.Now.SubtractYears(RegisteredSearchPastYears))))
            .ToListAsync();

    protected override async Task<Specification<Search>> GetSpecificationAsync(SearchesPaginatedRequestModel request)
        => new SearchByIdSpecification(request.Id)
            .And(new SearchByEmployeeIdSpecification(request.EmployeeId))
            .And(new SearchByListingStatusSpecification(request.SearchListingStatus, this.AbpSession.GetUserId(), request.Type))
            .And(new SearchByAreaSpecification(request.AreaFrom, request.AreaTo))
            .And(new SearchByPriceSpecification(request.MoneyFrom, request.MoneyTo))
            .And(new SearchByPricePerSquareMetres(request.SquareMetrePriceFrom, request.SquareMetrePriceTo))
            .And(new SearchBySearchTypeSpecification(request.Type))
            .And(new SearchByEstateTypeSpecification(request.EstateType))
            .And(new SearchByCreationTimeRangeSpecification(request.DateFrom, request.DateTo))
            .And(new SearchByCountryIdSpecification(request.CountryId))
            .And(new SearchByPopulatedPlaceIdSpecification(request.PopulatedPlaceId))
            .And(new SearchByDistrictIdsSpecification(request.DistrictIds))
            .And(new SearchByDepositSpecification(request.DepositId))
            .And(new SearchByAdvancedFiltersSpecification(request.AdvancedFilters))
            .And(new SearchByOwnershipStatusSpecification(request.OwnershipStatus, this.AbpSession.UserId,
                await permissionChecker.IsGrantedAsync(AppPermissions.SearchArchiveAccess),
                request.EmployeeId, request.CurrentUserEmployeeId));


    private async Task<List<ClientActiveRentSearchResponseModel>> GetClientActiveRentSearchesAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<ClientActiveRentSearchResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(s =>
                    s.ClientId == clientId &&
                    s.EmployeeId != currentEmployeeId &&
                    s.Type == SearchType.Rent &&
                    s.SearchStatusId == ActiveSearchStatusId))
            .ToListAsync();

    private async Task<List<ClientActiveBuySearchResponseModel>> GetClientActiveBuySearchesAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<ClientActiveBuySearchResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(s =>
                    s.ClientId == clientId &&
                    s.EmployeeId != currentEmployeeId &&
                    s.Type == SearchType.Buy &&
                    s.SearchStatusId == ActiveSearchStatusId))
            .ToListAsync();

    private async Task<bool> HasViewingAfterContractDateAsync(int clientId, int searchId, DateTime contractDate)
        => await this
            .Data
            .Viewings
            .AsNoTracking()
            .AnyAsync(v => (v.OfferClientId == clientId || v.SearchClientId == clientId) &&
                           v.SearchId == searchId &&
                           v.StartDate >= contractDate.AddDays(30));

    private async Task DeleteRelatedSearches(int searchId)
    {
        await this.Data
            .SearchesCountries
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesEstateTypes
            .Where(et => et.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesPopulatedPlaces
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesDistricts
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesDealMotives
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesFinancing
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesSourceDetails
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesOffers
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesRegulations
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesHouseTypes
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesConditions
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesHeating
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesFurniture
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesCompletionLevels
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesLifestyles
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesFacingDirections
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchDetails
            .Where(sc => sc.Id == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesEstateGroups
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesGarages
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();

        await this.Data
            .SearchesConstructionTypes
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();
    }

    private async Task DeleteRelatedSourceDetails(int searchId)
    {
        await this.Data
            .SearchesSourceDetails
            .Where(sc => sc.SearchId == searchId)
            .ExecuteDeleteAsync();
    }
    
    private async Task SendTransferredSearchNotificationAsync(
        int searchId,
        int employeeTenantId,
        long employeeUserId)
    {
        var notificationUser = new UserIdentifier(
            employeeTenantId,
            employeeUserId);

        var message = this.L("NewSearchTransferredNotificationMessage", searchId);

        await appNotifier.SendImportantMessageAsync(
            notificationUser,
            message);
    }
    
    public async Task<int> CreateSearchAsync(SearchCreateRequestModel request)
    {
        request.EmployeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());
        request.TenantId = this.GetTenantId();
        request.SearchStatusId = ActiveSearchStatusId;

        return await base.CreateAsync(request);
    }
}
