namespace RealtoCrm.Searches;

using System.Collections.Generic;
using System.Threading.Tasks;
using CommentsTasks;
using Common.Models;
using Models;
using Models.Permissions;
using Offers.Models;

public interface ISearchesAppService : ICommentsTasksAppService<
    int,
    Search,
    SearchComment,
    SearchTask,
    SearchCreateRequestModel,
    SearchUpdateRequestModel,
    SearchesPaginatedRequestModel,
    SearchResponseModel,
    SearchListingResponseModel>
{
    Task<SearchClientEmployeeResponseModel?> GetClientAndEmployeeIdsAsync(int id);

    Task<SearchClientContractTypeResponseModel?> GetClientAndContractTypeIdsAsync(int id);

    Task<IEnumerable<SearchResponseModel>> GetClientRelatedSearchAsync(int clientId);

    Task<SearchEmployeePermissionsResponseModel> GetEmployeeSearchPermissionsAsync(int clientId);

    Task<List<SearchClientRegisteredSearchResponseModel>> GetClientRegisteredBuySearchesAsync(
        int clientId,
        int? currentEmployeeId);

    Task<IEnumerable<SearchShortListingResponseModel?>> GetLastActiveSearchForClientsAsync(IEnumerable<int> clientIds);
    
    Task<SearchActivityResponseModel?> GetActivityAsync(int searchId);
    
    Task TransferToEmployeeAsync(TransferToEmployeeRequestModel request);   
    
    Task ArchiveSearchAsync(ArchiveRequestModel request);
    
    Task ActivateSearchAsync(int searchId);
    
    Task ChangeSearchOwnerAsync(ChangeOwnerRequestModel request);

    Task<IEnumerable<SearchesForDealResponseModel>> GetAllForDealCreationAsync(SearchesForDealRequestModel request);

    Task<int> CreateSearchForClientAsync(SearchCreateRequestModel request);
}