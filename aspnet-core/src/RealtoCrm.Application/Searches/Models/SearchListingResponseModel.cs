using RealtoCrm.Calls;
using RealtoCrm.Contracts;
using RealtoCrm.Contracts.Models;
using RealtoCrm.Deals;
using RealtoCrm.Deals.Models;
using RealtoCrm.Deposits;
using RealtoCrm.Deposits.Models;
using RealtoCrm.Viewings;

namespace RealtoCrm.Searches.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Comments.Models;
using ContactDetails.Models;
using Money;

public class SearchListingResponseModel : SearchColorCodingDetailsResponseModel
{
   private const int CommentsCountToDisplay = 3;

    public int Id { get; init; }

    public string Type { get; init; } = default!;

    public double AreaFrom { get; init; }

    public double AreaTo { get; init; }

    public MoneyResponseModel SquareMetrePriceFrom { get; init; } = default!;

    public MoneyResponseModel SquareMetrePriceTo { get; init; } = default!;

    public int ClientId { get; init; }

    public string? ClientName { get; init; }

    public string? ClientEmail { get; init; }

    public string? ClientPhoneNumber { get; init; }

    public int? EmployeeId { get; init; }

    public string? EmployeeName { get; init; }

    public string? EmployeePhoneNumber { get; init; }

    public string? EmployeeEmail { get; init; }

    public double UsefulFloorAreaFrom { get; init; }

    public double UsefulFloorAreaTo { get; init; }

    public string? CreationTime { get; init; }

    public MoneyResponseModel? MoneyFrom { get; init; }

    public MoneyResponseModel? MoneyTo { get; init; }

    public string? PopulatedPlaceName { get; init; }

    public List<string> Districts { get; init; } = default!;

    public string? EstateCategoryName { get; init; }

    public List<string> EstateTypes { get; init; } = default!;

    public int? ViewingsCount { get; init; }

    public DateTime? LastViewingDate { get; init; }

    public long? EmployeeUserId { get; init; }

    public string? SearchStatusName { get; init; }

    public string? ArchiveReasonName { get; init; }

    public DateTime? ArchiveDate { get; init; }

    public int TenantId { get; init; }

    public int? EmployeeTeamId { get; init; }

    public int? EmployeeDepartmentId { get; init; }

    public int? EmployeeDivisionId { get; init; }

    public int? EmployeeTenantId { get; init; }
    
    public int? SearchMappingAdminId { get; init; }

    public List<CommentResponseModel> Comments { get; set; } = new();

    public DateTime? LastContact { get; init; }

    public ContractInSearchListingResponseModel? Contract { get; init; }

    public DepositInSearchListingResponseModel? Deposit { get; init; }

    public DealInSearchListingResponseModel? Deal { get; init; }

    public override void RegisterMappings(IProfileExpression mapper)
    => mapper
        .CreateMap<Search, SearchListingResponseModel>()
        .IncludeBase<Search, SearchColorCodingDetailsResponseModel>()

        // Employee full name
        .ForMember(slm => slm.EmployeeName, cfg => cfg
            .MapFrom(s =>
                string.IsNullOrWhiteSpace(s.Employee.FirstName) && string.IsNullOrWhiteSpace(s.Employee.LastName)
                    ? null
                    : $"{s.Employee.FirstName} {s.Employee.LastName}"))

        // Employee email
        .ForMember(slm => slm.EmployeeEmail, cfg => cfg
            .MapFrom(s => s.Employee != null ? s.Employee.UserAccount.EmailAddress : null))

        // Client full name
        .ForMember(slm => slm.ClientName, cfg => cfg
            .MapFrom(s => $"{s.Client.PersonalData.FirstName} {s.Client.PersonalData.LastName}"))

        // Optimized: Client email (no Any)
        .ForMember(slm => slm.ClientEmail, cfg => cfg
            .MapFrom(s =>
                s.Client.ContactDetails
                    .Where(d => d.ContactDetailId == (int)ContactDetailId.Email)
                    .OrderBy(d => d.Id)
                    .Select(d => d.Value)
                    .FirstOrDefault()))

        // Optimized: Client phone (no Any)
        .ForMember(slm => slm.ClientPhoneNumber, cfg => cfg
            .MapFrom(s =>
                s.Client.ContactDetails
                    .Where(d => d.ContactDetailId == (int)ContactDetailId.Phone)
                    .OrderBy(d => d.Id)
                    .Select(d => d.Value)
                    .FirstOrDefault()))

        // Optimized: PopulatedPlace (no Count > 0)
        .ForMember(slm => slm.PopulatedPlaceName, cfg => cfg
            .MapFrom(s =>
                s.SearchesPopulatedPlaces
                    .OrderBy(sp => sp.SearchId)
                    .Select(sp => sp.PopulatedPlace.Name)
                    .FirstOrDefault()))

        // Optimized: Districts list
        .ForMember(slm => slm.Districts, cfg => cfg
            .MapFrom(s => s.SearchesDistricts
                .Select(sd => sd.District.Name)
                .ToList()))

        // Optimized: Estate types list
        .ForMember(slm => slm.EstateTypes, cfg => cfg
            .MapFrom(s => s.SearchesEstateTypes
                .Select(x => x.EstateType.Name)
                .ToList()))

        // Optimized: EstateCategoryName (no Count > 0)
        .ForMember(slm => slm.EstateCategoryName, cfg => cfg
            .MapFrom(s =>
                s.SearchesEstateTypes
                    .OrderBy(x => x.SearchId)
                    .Select(x => x.EstateType.Category.Name)
                    .FirstOrDefault()))

        // Viewings count
        .ForMember(slm => slm.ViewingsCount, cfg => cfg
            .MapFrom(s => s.Viewings.Count))

        // Optimized: Last viewing date (no Any)
        .ForMember(slm => slm.LastViewingDate, cfg => cfg
            .MapFrom(s =>
                s.Viewings
                    .OrderByDescending(v => v.CreationTime)
                    .Select(v => (DateTime?)v.CreationTime)
                    .FirstOrDefault()))

        // Employee User ID
        .ForMember(slm => slm.EmployeeUserId, cfg => cfg
            .MapFrom(s => s.Employee != null ? s.Employee.UserAccount.UserId : (long?)null))

        // Public comments (unchanged, already optimal)
        .ForMember(slm => slm.Comments, cfg => cfg
            .MapFrom(s => s.SearchesComments
                .Where(sc => sc.Comment != null && !sc.Comment.IsPrivate)
                .OrderByDescending(sc => sc.Comment.CreationTime)
                .Take(CommentsCountToDisplay)
                .Select(sc => sc.Comment)))

        // Optimized: Last contact (no nested OrderBy inside projection)
        .ForMember(slm => slm.LastContact, cfg => cfg
            .MapFrom(s =>
                s.Client.Calls
                    .Where(c => c.ConsultantId == s.EmployeeId)
                    .OrderByDescending(c => c.CreationTime)
                    .Select(c => (DateTime?)c.CreationTime)
                    .FirstOrDefault()))

        
        // Optimized: Contract (reduce sorting logic)
        .ForMember(slm => slm.Contract, cfg => cfg
            .MapFrom(s =>
                s.Contracts
                    .OrderByDescending(c => c.ExpirationDate == null)
                    .ThenByDescending(c => c.CreationTime)
                    .Select(c => new ContractInSearchListingResponseModel
                    {
                        SignDate = c.SignDate,
                        ExpirationDate = c.ExpirationDate,
                        CommissionPercentage = c.CommissionPercentage
                    })
                    .FirstOrDefault()))

        // Optimized: Deposit (no Count check)
        .ForMember(slm => slm.Deposit, cfg => cfg
            .MapFrom(s =>
                s.Deposits
                    .OrderByDescending(c => c.CreationTime)
                    .FirstOrDefault()))

        // Optimized: Deal (no Count check)
        .ForMember(slm => slm.Deal, cfg => cfg
            .MapFrom(s =>
                s.Deals
                    .OrderByDescending(d => d.CreationTime)
                    .FirstOrDefault()))

        // Employee tenant ID
        .ForMember(m => m.EmployeeTenantId, cfg => cfg
            .MapFrom(m => m.Employee != null ? m.Employee.UserAccount.TenantId : null));


}