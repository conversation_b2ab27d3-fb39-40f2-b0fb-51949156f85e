using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Authorization;
using RealtoCrm.Employees.Models;
using RealtoCrm.EntityFrameworkCore;

namespace RealtoCrm.Searches;

public class SearchesPermissionsChecker(IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : RealtoCrmAppServiceBase, ISearchesPermissionsChecker
{
    private RealtoCrmDbContext Data => dbContextProvider.GetDbContext();
    
    public async Task<bool> CanPerformAction(
        int searchId,
        long userId,
        string[] permissions,
        bool checkForSameTenant = true)
    {
        
        if (await this.PermissionChecker.IsGrantedAsync(AppPermissions.AdministrationFullAccess ))
        {
            return true;
        }

        var searchEmployee = await this.GetSearchEmployee(searchId);
        var employee = await this.GetEmployee(userId);

        var hasPermission = permissions.Length == 0 ||
                            (await Task.WhenAll(permissions.Select(p => this.PermissionChecker.IsGrantedAsync(p))))
                            .Any(result => result);

        var areInSameTenant = searchEmployee?.TenantId == employee?.TenantId;

        if (searchEmployee?.EmployeeUserId == this.AbpSession.UserId)
        {
            return checkForSameTenant
                ? areInSameTenant && hasPermission
                : hasPermission;
        }

        var canPerformAction = (
            await this.CanPerformActionInDepartment(searchEmployee?.DepartmentId, employee?.DepartmentId) ||
            await this.CanPerformActionInTeam(searchEmployee?.TeamId, employee?.TeamId) ||
            await this.CanPerformActionInDivision(searchEmployee?.DivisionId, employee?.DivisionId)
        ) && hasPermission;

        return checkForSameTenant ? canPerformAction && areInSameTenant : hasPermission;
    }
    
    private async Task<EmployeeTeamDetailsResponseModel?> GetSearchEmployee(int searchId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeTeamDetailsResponseModel>(this
                .Data
                .Searches
                .AsNoTracking()
                .Where(o => o.Id == searchId))
            .FirstOrDefaultAsync();


    private async Task<EmployeeTeamDetailsResponseModel?> GetEmployee(long userId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeTeamDetailsResponseModel>(this
                .Data
                .Employees
                .AsNoTracking()
                .Where(e => e.UserAccount.UserId == userId))
            .FirstOrDefaultAsync();

    private async Task<bool> CanPerformActionInTeam(int? searchTeamId, int? currentEmployeeTeamId)
        => currentEmployeeTeamId.HasValue && searchTeamId == currentEmployeeTeamId &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.SearchTeamEdit);

    private async Task<bool> CanPerformActionInDepartment(int? searchDepartmentId, int? currentEmployeeDepartmentId)
        => currentEmployeeDepartmentId.HasValue && searchDepartmentId == currentEmployeeDepartmentId &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.SearchDepartmentEdit);

    private async Task<bool> CanPerformActionInDivision(int? searchDivisionId, int? currentEmployeeDivisionId)
        => currentEmployeeDivisionId.HasValue && searchDivisionId == currentEmployeeDivisionId &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.SearchDivisionEdit);
}