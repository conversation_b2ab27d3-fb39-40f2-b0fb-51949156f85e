using System;
using System.Linq.Expressions;
using RealtoCrm.Expressions;

namespace RealtoCrm.Searches.Specifications;

public class SearchByListingStatusSpecification(
    SearchListingStatus? searchListingStatus,
    long? userId,
    int? type) : Specification<Search>
{
    protected override bool Include => searchListingStatus is not null && userId != null && type != null;

    public override Expression<Func<Search, bool>> ToExpression()
    {
        return searchListingStatus switch
        {
            SearchListingStatus.Potential => SearchesExpressions.IsPotentialSearch((SearchType) type!),
            SearchListingStatus.Meeting => SearchesExpressions.IsMeetingSearch((SearchType) type!),
            SearchListingStatus.Contract => SearchesExpressions.IsContractSearch((SearchType) type!),
            SearchListingStatus.Viewing => SearchesExpressions.IsViewingSearch((SearchType) type!),
            SearchListingStatus.Deposit => SearchesExpressions.IsDepositSearch((SearchType) type!),
            SearchListingStatus.Deal => SearchesExpressions.IsDealSearch((SearchType) type!),
            _ => search => false
        };
    }
}