namespace RealtoCrm.Employees.Models;

using AutoMapper;
using Mapping;
using RealtoCrm.Common.SpecificationHelpers;
using RealtoCrm.Expressions;

public class EmployeesClientTypesCount : IMapFrom<Employee>, IMapExplicitly
{
    public int AllClientsCount { get; set; }

    public int PotentialClientsCount { get; set; }

    public int MeetingClientsCount { get; set; }

    public int ContractClientsCount { get; set; }

    public int ViewingClientsCount { get; set; }

    public int DepositsClientsCount { get; set; }

    public int DealsClientsCount { get; set; }


    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Employee, EmployeesClientTypesCount>()
            .ForMember(d => d.AllClientsCount,
                o => o.MapFrom(src => src.ClientsSourceCategories.Count))
            .ForMember(d => d.PotentialClientsCount,
                o => o.MapFrom(
                    ClientExpressions
                        .IsPotentialClient()
                        .ToClientCountExpression()
                ))
            .ForMember(d => d.MeetingClientsCount,
                o => o.MapFrom(
                    ClientExpressions
                        .IsClientWithMeetings()
                        .ToClientCountExpression()
                ))
            .ForMember(d => d.ContractClientsCount,
                o => o.MapFrom(
                    ClientExpressions
                        .IsClientWithContract()
                        .ToClientCountExpression()
                ))
            .ForMember(d => d.ViewingClientsCount,
                o => o.MapFrom(
                    ClientExpressions
                        .IsClientWithViewings()
                        .ToClientCountExpression()
                ))
            .ForMember(d => d.DepositsClientsCount,
                o => o.MapFrom(
                    ClientExpressions
                        .IsClientWithDeposits()
                        .ToClientCountExpression()
                ))
            .ForMember(d => d.DealsClientsCount,
                o => o.MapFrom(
                    ClientExpressions
                        .IsClientWithDeals()
                        .ToClientCountExpression()
                ));
}