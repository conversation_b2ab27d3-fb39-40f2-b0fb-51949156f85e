using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Abp;
using Abp.AspNetZeroCore.Net;
using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.Localization;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Dto;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Storage;

namespace RealtoCrm.Gdpr;

public class ProfileUserCollectedDataProvider(
    UserManager userManager,
    TenantManager tenantManager,
    ITempFileCacheManager tempFileCacheManager,
    IUnitOfWorkManager unitOfWorkManager,
    ILocalizationManager localizationManager)
    : IUserCollectedDataProvider, ITransientDependency
{
    public async Task<List<FileDto>> GetFiles(UserIdentifier user)
    {
        var tenancyName = ".";
        if (user.TenantId.HasValue)
        {
            using (unitOfWorkManager.Current.SetTenantId(null))
            {
                tenancyName = (await tenantManager.GetByIdAsync(user.TenantId.Value)).TenancyName;
            }
        }

        var profileInfo = await userManager.GetUserByIdAsync(user.UserId);

        var content = new List<string>
        {
            this.L("TenancyName")+ ": " + tenancyName,
            this.L("UserName") +": " + profileInfo.UserName,
            this.L("Name") +": " + profileInfo.Name,
            this.L("Surname") +": " + profileInfo.Surname,
            this.L("EmailAddress") +": " + profileInfo.EmailAddress,
            this.L("PhoneNumber") +": " + profileInfo.PhoneNumber
        };

        var profileInfoBytes = Encoding.UTF8.GetBytes(string.Join("\n\r", content));

        var file = new FileDto("ProfileInfo.txt", MimeTypeNames.TextPlain);
        tempFileCacheManager.SetFile(file.FileToken, profileInfoBytes);

        return new List<FileDto> { file };
    }

    private string L(string name)
    {
        return localizationManager.GetString(RealtoCrmConsts.LocalizationSourceName, name);
    }
}