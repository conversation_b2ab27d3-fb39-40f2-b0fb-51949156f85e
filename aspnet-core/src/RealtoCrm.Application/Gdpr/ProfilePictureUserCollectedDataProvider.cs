using System.Collections.Generic;
using System.Threading.Tasks;
using Abp;
using Abp.AspNetZeroCore.Net;
using Abp.Dependency;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Dto;
using RealtoCrm.Storage;

namespace RealtoCrm.Gdpr;

public class ProfilePictureUserCollectedDataProvider(
    UserManager userManager,
    IBinaryObjectManager binaryObjectManager,
    ITempFileCacheManager tempFileCacheManager) : IUserCollectedDataProvider, ITransientDependency
{
    public async Task<List<FileDto>> GetFiles(UserIdentifier user)
    {
        var profilePictureId = (await userManager.GetUserByIdAsync(user.UserId)).ProfilePictureId;
        if (!profilePictureId.HasValue)
        {
            return new List<FileDto>();
        }

        var profilePicture = await binaryObjectManager.GetOrNullAsync(profilePictureId.Value);
        if (profilePicture == null)
        {
            return new List<FileDto>();
        }

        var file = new FileDto("ProfilePicture.png", MimeTypeNames.ImagePng);
        tempFileCacheManager.SetFile(file.FileToken, profilePicture.Bytes);

        return new List<FileDto> {file};
    }
}