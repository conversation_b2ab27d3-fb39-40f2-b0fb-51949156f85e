namespace RealtoCrm.CMA;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.CMA.Models.Common;
using RealtoCrm.CMA.Models.Searches;
using RealtoCrm.CMA.Specifications.Searches;
using RealtoCrm.CMA.ValueResolvers;
using RealtoCrm.Common.Extensions;
using RealtoCrm.DataCrudModels;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Expressions;
using RealtoCrm.Extensions;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Offers;
using RealtoCrm.Searches;
using static RealtoCrm.Nomenclatures.NomenclatureConsts.OperationType;

public class CmaSearchesAppService(IDbContextProvider<RealtoCrmDbContext> dbContextProvider, IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Search,
        NoCreateModel<Search>,
        NoUpdateModel<Search>,
        CmaSearchesPaginatedRequest,
        NoDetailsModel<Search>,
        CmaSearchResponseModel
    >(dbContextProvider, expressionsBuilder)
{
    protected override IQueryable<Search> GetEntityQuery(Specification<Search> specification, Expression<Func<Search, bool>> filtersExpression)
        => base.GetEntityQuery(specification, filtersExpression)
            .Include(s => s.SquareMetrePriceFrom)
            .Include(s => s.SquareMetrePriceTo)
            .Include(s => s.SearchesDistricts)
            .ThenInclude(sd => sd.District)
            .ThenInclude(d => d.PopulatedPlace)
            .Include(s => s.SearchesEstateTypes)
            .ThenInclude(set => set.EstateType)
            .ThenInclude(et => et.Category)
            .Include(s => s.MoneyTo)
            .Include(s => s.MoneyFrom)
            .Include(s => s.Employee);

    private Task<Offer> GetBaseOffer(CmaSearchesPaginatedRequest request)
        => this.Data.Set<Offer>()
            .Include(
                o => o.Estate)
            .ThenInclude(o => o.Address)
            .FirstOrDefaultAsync(o => o.Id == request.OfferId);

    protected override async Task<Specification<Search>> GetSpecificationAsync(CmaSearchesPaginatedRequest request)
    {
        var operationTypes = await this.Data
            .Set<OperationType>()
            .ToListAsync();

        var baseOffer = await this.GetBaseOffer(request);

        var searchType = baseOffer.OperationTypeId == operationTypes.Single(ot => ot.Name == BuyOperationType).Id
            ? SearchType.Buy
            : SearchType.Rent;

        var specifications = new Specification<Search>[]
        {
            new SearchesByOperationTypeSpecification(searchType),
            new SearchesByDistrictSpecification(baseOffer.Estate.Address.DistrictId.Value),
            new SearchesByEstateCategoryAndTypeSpecification(baseOffer.Estate),
            new SearchesByAreaRangeSpecification(baseOffer.Estate.Area.Value)
        };

        return specifications.Aggregate((left, right) => left.And(right));
    }

    public override async Task<PaginatedResponseModel<CmaSearchResponseModel>> GetAllAsync(CmaSearchesPaginatedRequest? request)
    {
        request ??= new CmaSearchesPaginatedRequest();

        var specification = await this.BuildSpecification(request);

        var skip = request.Page * request.PageSize;

        var filtersExpression = await this.BuildDataCrudFiltersExpression(request.Filters);

        var sorterExpressionDefinitions = await this.BuildDataCrudSortByExpression(request);

        var items = await this.GetListingAsync(
            specification,
            filtersExpression,
            sorterExpressionDefinitions,
            request,
            skip,
            take: request.PageSize);

        var total = await this.GetTotal(specification, filtersExpression);

        var totalPages = (int)Math.Ceiling((double)total / request.PageSize);

        return new PaginatedResponseModel<CmaSearchResponseModel>(items, request.Page, total, totalPages);
    }

    private async Task<IEnumerable<CmaSearchResponseModel>> GetListingAsync(
        Specification<Search> specification,
        Expression<Func<Search, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<Search>> sortByExpressions,
        CmaSearchesPaginatedRequest request,
        int skip = 0,
        int take = int.MaxValue)
    {
        var mappings = await this.GetMappingDependenciesForGetAll(request)
            .ToListAsync();
        var data = this
            .GetEntityQuery(specification, filtersExpression)
            .ApplySortByDefinitions(sortByExpressions)
            .Skip(skip)
            .Take(take);
        
        return this.ObjectMapper
            .Map<IEnumerable<CmaSearchResponseModel>>(
                data,
                opt =>
                    mappings.ForEach(x => opt.Items[x.Key] = x.Value))
            .ToList();
    }

    protected virtual async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependenciesForGetAll(CmaSearchesPaginatedRequest request)
    {
        var baseOffer = await this.GetBaseOffer(request);

        return new Dictionary<string, object>
        {
            { SearchEstateTypeByBaseOfferValueResolver.EstateTypeIdItemsKey, baseOffer.Estate.TypeId },
            { SearchEstateCategoryByBaseOfferValueResolver.EstateCategoryIdItemsKey, baseOffer.Estate.CategoryId },
            { SearchDistrictByBaseOfferValueResolver.DistrictIdItemsKey, baseOffer.Estate.Address.DistrictId },
        };
    }
}