using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.BackgroundJobs;
using Abp.Domain.Repositories;
using Abp.Webhooks;
using Abp.Webhooks.BackgroundWorker;
using RealtoCrm.Authorization;
using RealtoCrm.WebHooks.Dto;

namespace RealtoCrm.WebHooks;

[AbpAuthorize]
public class WebhookSendAttemptAppService(
    IWebhookSendAttemptStore webhookSendAttemptStore,
    IBackgroundJobManager backgroundJobManager,
    IWebhookEventAppService webhookEventAppService,
    IWebhookSubscriptionAppService webhookSubscriptionAppService,
    IRepository<WebhookSubscriptionInfo, Guid> subscriptionRepository)
    : RealtoCrmAppServiceBase, IWebhookAttemptAppService
{
    public async Task<PagedResultDto<GetAllSendAttemptsOutput>> GetAllSendAttempts(GetAllSendAttemptsInput input)
    {
        if (string.IsNullOrEmpty(input.SubscriptionId))
        {
            throw new ArgumentNullException(nameof(input.SubscriptionId));
        }

        var list = await webhookSendAttemptStore.GetAllSendAttemptsBySubscriptionAsPagedListAsync(this.AbpSession.TenantId,
            Guid.Parse(input.SubscriptionId),
            input.MaxResultCount,
            input.SkipCount
        );

        return new PagedResultDto<GetAllSendAttemptsOutput>(list.TotalCount, this.ObjectMapper.Map<List<GetAllSendAttemptsOutput>>(list.Items));
    }

    public async Task<ListResultDto<GetAllSendAttemptsOfWebhookEventOutput>> GetAllSendAttemptsOfWebhookEvent(GetAllSendAttemptsOfWebhookEventInput input)
    {
        if (string.IsNullOrEmpty(input.Id))
        {
            throw new ArgumentNullException(nameof(input.Id));
        }

        var list = await webhookSendAttemptStore.GetAllSendAttemptsByWebhookEventIdAsync(this.AbpSession.TenantId,
            Guid.Parse(input.Id)
        );

        var mappedList = this.ObjectMapper.Map<List<GetAllSendAttemptsOfWebhookEventOutput>>(list);
        var subscriptionIds = list.Select(x => x.WebhookSubscriptionId).Distinct().ToArray();

        var subscriptionUrisDictionary = subscriptionRepository.GetAll().Where(subscription => subscriptionIds.Contains(subscription.Id))
            .Select(subscription => new { subscription.Id, subscription.WebhookUri })
            .ToDictionary(s => s.Id, s => s.WebhookUri);

        foreach (var output in mappedList)
        {
            output.WebhookUri = subscriptionUrisDictionary[output.WebhookSubscriptionId];
        }

        return new ListResultDto<GetAllSendAttemptsOfWebhookEventOutput>(mappedList);
    }

    public async Task Resend(string sendAttemptId)
    {
        var webhookSendAttempt = await webhookSendAttemptStore.GetAsync(this.AbpSession.TenantId, Guid.Parse(sendAttemptId));
        var webhookEvent = await webhookEventAppService.Get(webhookSendAttempt.WebhookEventId.ToString());
        var webhookSubscription = await webhookSubscriptionAppService.GetSubscription(webhookSendAttempt.WebhookSubscriptionId.ToString());

        await backgroundJobManager.EnqueueAsync<WebhookSenderJob, WebhookSenderArgs>(new WebhookSenderArgs()
        {
            TenantId = this.AbpSession.TenantId,
            WebhookEventId = webhookSendAttempt.WebhookEventId,
            WebhookSubscriptionId = webhookSendAttempt.WebhookSubscriptionId,
            Data = webhookEvent.Data,
            WebhookName = webhookEvent.WebhookName,
            WebhookUri = webhookSubscription.WebhookUri,
            Headers = webhookSubscription.Headers,
            Secret = webhookSubscription.Secret,
            TryOnce = true
        });
    }
}