using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Localization;
using Abp.Webhooks;
using RealtoCrm.Authorization;
using RealtoCrm.WebHooks.Dto;

namespace RealtoCrm.WebHooks;

using Webhooks;

[AbpAuthorize]
public class WebhookSubscriptionAppService(
    IWebhookSubscriptionManager webHookSubscriptionManager,
    IAppWebhookPublisher appWebhookPublisher,
    IWebhookDefinitionManager webhookDefinitionManager,
    ILocalizationContext localizationContext)
    : RealtoCrmAppServiceBase, IWebhookSubscriptionAppService
{
    public async Task<string> PublishTestWebhook()
    {
        await appWebhookPublisher.PublishTestWebhook();
        return this.L("WebhookSendAttemptInQueue") + "(" + this.L("YouHaveToSubscribeToTestWebhookToReceiveTestEvent") + ")";
    }

    public async Task<ListResultDto<GetAllSubscriptionsOutput>> GetAllSubscriptions()
    {
        var subscriptions = await webHookSubscriptionManager.GetAllSubscriptionsAsync(this.AbpSession.TenantId);
        return new ListResultDto<GetAllSubscriptionsOutput>(this.ObjectMapper.Map<List<GetAllSubscriptionsOutput>>(subscriptions)
        );
    }

    public async Task<WebhookSubscription> GetSubscription(string subscriptionId)
    {
        return await webHookSubscriptionManager.GetAsync(Guid.Parse(subscriptionId));
    }

    public async Task AddSubscription(WebhookSubscription subscription)
    {
        subscription.TenantId = this.AbpSession.TenantId;

        await webHookSubscriptionManager.AddOrUpdateSubscriptionAsync(subscription);
    }

    public async Task UpdateSubscription(WebhookSubscription subscription)
    {
        if (subscription.Id == default)
        {
            throw new ArgumentNullException(nameof(subscription.Id));
        }

        subscription.TenantId = this.AbpSession.TenantId;

        await webHookSubscriptionManager.AddOrUpdateSubscriptionAsync(subscription);
    }

    public async Task ActivateWebhookSubscription(ActivateWebhookSubscriptionInput input)
    {
        await webHookSubscriptionManager.ActivateWebhookSubscriptionAsync(input.SubscriptionId, input.IsActive);
    }

    public async Task<bool> IsSubscribed(string webhookName)
    {
        return await webHookSubscriptionManager.IsSubscribedAsync(this.AbpSession.TenantId, webhookName);
    }

    public async Task<ListResultDto<GetAllSubscriptionsOutput>> GetAllSubscriptionsIfFeaturesGranted(string webhookName)
    {
        var subscriptions = await webHookSubscriptionManager.GetAllSubscriptionsIfFeaturesGrantedAsync(this.AbpSession.TenantId, webhookName);
        return new ListResultDto<GetAllSubscriptionsOutput>(this.ObjectMapper.Map<List<GetAllSubscriptionsOutput>>(subscriptions)
        );
    }

    public async Task<ListResultDto<GetAllAvailableWebhooksOutput>> GetAllAvailableWebhooks()
    {
        var webhooks = webhookDefinitionManager.GetAll();
        var definitions = new List<GetAllAvailableWebhooksOutput>();

        foreach (var webhookDefinition in webhooks)
        {
            if (await webhookDefinitionManager.IsAvailableAsync(this.AbpSession.TenantId, webhookDefinition.Name))
            {
                definitions.Add(new GetAllAvailableWebhooksOutput()
                {
                    Name = webhookDefinition.Name,
                    Description = webhookDefinition.Description?.Localize(localizationContext),
                    DisplayName = webhookDefinition.DisplayName?.Localize(localizationContext)
                });
            }
        }

        return new ListResultDto<GetAllAvailableWebhooksOutput>(definitions.OrderBy(d => d.Name).ToList());
    }
}