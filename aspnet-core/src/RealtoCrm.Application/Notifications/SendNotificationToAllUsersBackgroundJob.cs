using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.Authorization.Users;
using Abp.BackgroundJobs;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Microsoft.EntityFrameworkCore;

namespace RealtoCrm.Notifications;

public class SendNotificationToAllUsersBackgroundJob(
    IRepository<UserAccount, long> userAccountRepository,
    IAppNotifier appNotifier) : AsyncBackgroundJob<SendNotificationToAllUsersArgs>,
    ITransientDependency
{
    private const int MaxUserCount = 1000;

    public override async Task ExecuteAsync(SendNotificationToAllUsersArgs toAllUsersArgs)
    {
        var userCount = await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            await userAccountRepository.GetAll().LongCountAsync()
        );

        if (userCount == 0)
        {
            return;
        }

        var loopCount = userCount / MaxUserCount + 1;

        for (var i = 0; i < loopCount; i++)
        {
            var userIds = await this.UnitOfWorkManager.WithUnitOfWorkAsync(async () =>
            {
                return await userAccountRepository.GetAll().Skip(i * MaxUserCount)
                    .AsNoTracking()
                    .Take(MaxUserCount)
                    .Select(u => new UserIdentifier(u.TenantId, u.UserId))
                    .ToArrayAsync();
            });

            await appNotifier.SendMessageAsync(
                toAllUsersArgs.NotificationName,
                toAllUsersArgs.Message,
                userIds,
                toAllUsersArgs.Severity
            );
        }
    }
}