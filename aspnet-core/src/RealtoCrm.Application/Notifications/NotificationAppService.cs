namespace RealtoCrm.Notifications;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.Application.Services.Dto;
using Abp.Auditing;
using Abp.Authorization;
using Abp.BackgroundJobs;
using Abp.Collections.Extensions;
using Abp.Configuration;
using Abp.Domain.Repositories;
using Abp.Linq;
using Abp.Linq.Extensions;
using Abp.Notifications;
using Abp.Organizations;
using Abp.Runtime.Session;
using Abp.UI;
using Authorization;
using Authorization.Users;
using Dto;
using Microsoft.EntityFrameworkCore;

[AbpAuthorize]
public class NotificationAppService(
    INotificationDefinitionManager notificationDefinitionManager,
    IUserNotificationManager userNotificationManager,
    INotificationSubscriptionManager notificationSubscriptionManager,
    IRepository<User, long> userRepository,
    IRepository<OrganizationUnit, long> organizationUnitRepository,
    IAppNotifier appNotifier,
    INotificationConfiguration notificationConfiguration,
    INotificationStore notificationStore,
    IBackgroundJobManager backgroundJobManager,
    IRepository<NotificationInfo, Guid> notificationRepository,
    IRepository<TenantNotificationInfo, Guid> tenantNotificationRepository)
    : RealtoCrmAppServiceBase, INotificationAppService
{
    private readonly INotificationStore notificationStore = notificationStore;

    public IAsyncQueryableExecuter AsyncQueryableExecuter { get; set; } = NullAsyncQueryableExecuter.Instance;

    [DisableAuditing]
    public async Task<GetNotificationsOutput> GetUserNotifications(GetUserNotificationsInput input)
    {
        var totalCount = await userNotificationManager.GetUserNotificationCountAsync(this.AbpSession.ToUserIdentifier(),
            input.State, input.StartDate, input.EndDate
        );

        var unreadCount = await userNotificationManager.GetUserNotificationCountAsync(
            this.AbpSession.ToUserIdentifier(), UserNotificationState.Unread, input.StartDate, input.EndDate
        );
        var notifications = await userNotificationManager.GetUserNotificationsAsync(this.AbpSession.ToUserIdentifier(),
            input.State, input.SkipCount, input.MaxResultCount, input.StartDate,
            input.EndDate
        );

        return new GetNotificationsOutput(totalCount, unreadCount, notifications);
    }

    [DisableAuditing]
    public async Task<CategorizedNotificationsOutput> GetCategorizedUserTodayNotifications(
        GetUserNotificationsInput input)
    {
        var userIdentifier = this.AbpSession.ToUserIdentifier();

        var userTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Europe/Sofia");
        var localNow = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, userTimeZone);

        var localStart = new DateTime(localNow.Year, localNow.Month, localNow.Day, 0, 0, 0);
        var localEnd = localStart.AddDays(1).AddTicks(-1);

        var utcStart = TimeZoneInfo.ConvertTimeToUtc(localStart, userTimeZone);
        var utcEnd = TimeZoneInfo.ConvertTimeToUtc(localEnd, userTimeZone);

        var allNotifications = await userNotificationManager.GetUserNotificationsAsync(
            userIdentifier, input.State, input.SkipCount, input.MaxResultCount, utcStart, utcEnd);

        var output = new CategorizedNotificationsOutput
        {
            TotalCount =
                await userNotificationManager.GetUserNotificationCountAsync(userIdentifier, input.State, utcStart,
                    utcEnd),
            UnreadCount = await userNotificationManager.GetUserNotificationCountAsync(userIdentifier,
                UserNotificationState.Unread, utcStart, utcEnd)
        };

        foreach (var userNotification in allNotifications)
        {
            this.CategorizeNotification(userNotification, output);
        }

        return output;
    }

    [DisableAuditing]
    public async Task<CategorizedNotificationsOutput> GetCategorizedUserLastWeekNotifications(
        GetUserNotificationsInput input)
    {
        var userIdentifier = this.AbpSession.ToUserIdentifier();


        var today = DateTime.UtcNow.Date;
        var endDate = today.AddDays(-1).AddDays(1).AddTicks(-1);
        var startDate = today.AddDays(-7);

        var allNotifications = await userNotificationManager.GetUserNotificationsAsync(
            userIdentifier, input.State, input.SkipCount, input.MaxResultCount, startDate, endDate);

        var output = new CategorizedNotificationsOutput
        {
            TotalCount =
                await userNotificationManager.GetUserNotificationCountAsync(userIdentifier, input.State, startDate,
                    endDate),
            UnreadCount = await userNotificationManager.GetUserNotificationCountAsync(userIdentifier,
                UserNotificationState.Unread, startDate, endDate)
        };

        foreach (var userNotification in allNotifications)
        {
            this.CategorizeNotification(userNotification, output);
        }

        return output;
    }

    public async Task<bool> ShouldUserUpdateApp()
    {
        var notifications = await userNotificationManager.GetUserNotificationsAsync(this.AbpSession.ToUserIdentifier(),
            UserNotificationState.Unread
        );

        return notifications.Any(x => x.Notification.NotificationName == AppNotificationNames.NewVersionAvailable);
    }

    public async Task<SetNotificationAsReadOutput> SetAllAvailableVersionNotificationAsRead()
    {
        var notifications = await userNotificationManager.GetUserNotificationsAsync(this.AbpSession.ToUserIdentifier(),
            UserNotificationState.Unread
        );

        var filteredNotifications = notifications
            .Where(x => x.Notification.NotificationName == AppNotificationNames.NewVersionAvailable)
            .ToList();

        if (!filteredNotifications.Any())
        {
            return new SetNotificationAsReadOutput(false);
        }

        foreach (var notification in filteredNotifications)
        {
            if (notification.State == UserNotificationState.Read)
            {
                continue;
            }

            await userNotificationManager.UpdateUserNotificationStateAsync(
                notification.TenantId,
                notification.Id,
                UserNotificationState.Read
            );
        }

        return new SetNotificationAsReadOutput(true);
    }

    public async Task SetAllNotificationsAsRead()
    {
        await userNotificationManager.UpdateAllUserNotificationStatesAsync(this.AbpSession.ToUserIdentifier(),
            UserNotificationState.Read
        );
    }

    public async Task<SetNotificationAsReadOutput> SetNotificationAsRead(EntityDto<Guid> input)
    {
        var userNotification =
            await userNotificationManager.GetUserNotificationAsync(this.AbpSession.TenantId, input.Id);
        if (userNotification == null)
        {
            return new SetNotificationAsReadOutput(false);
        }

        if (userNotification.UserId != this.AbpSession.GetUserId())
        {
            throw new Exception(
                $"Given user notification id ({input.Id}) is not belong to the current user ({this.AbpSession.GetUserId()})"
            );
        }

        if (userNotification.State == UserNotificationState.Read)
        {
            return new SetNotificationAsReadOutput(false);
        }

        await userNotificationManager.UpdateUserNotificationStateAsync(this.AbpSession.TenantId, input.Id,
            UserNotificationState.Read);
        return new SetNotificationAsReadOutput(true);
    }

    public async Task<GetNotificationSettingsOutput> GetNotificationSettings()
    {
        var output = new GetNotificationSettingsOutput();

        output.ReceiveNotifications =
            await this.SettingManager.GetSettingValueAsync<bool>(NotificationSettingNames.ReceiveNotifications);

        //Get general notifications, not entity related notifications.
        var notificationDefinitions =
            (await notificationDefinitionManager.GetAllAvailableAsync(this.AbpSession.ToUserIdentifier())).Where(nd =>
                nd.EntityType == null);

        output.Notifications =
            this.ObjectMapper.Map<List<NotificationSubscriptionWithDisplayNameDto>>(notificationDefinitions);

        var subscribedNotifications = (await notificationSubscriptionManager
                .GetSubscribedNotificationsAsync(this.AbpSession.ToUserIdentifier()))
            .Select(ns => ns.NotificationName)
            .ToList();

        output.Notifications.ForEach(n => n.IsSubscribed = subscribedNotifications.Contains(n.Name));

        return output;
    }

    public async Task UpdateNotificationSettings(UpdateNotificationSettingsInput input)
    {
        await this.SettingManager.ChangeSettingForUserAsync(this.AbpSession.ToUserIdentifier(),
            NotificationSettingNames.ReceiveNotifications, input.ReceiveNotifications.ToString());

        foreach (var notification in input.Notifications)
        {
            if (notification.IsSubscribed)
            {
                await notificationSubscriptionManager.SubscribeAsync(this.AbpSession.ToUserIdentifier(),
                    notification.Name);
            }
            else
            {
                await notificationSubscriptionManager.UnsubscribeAsync(this.AbpSession.ToUserIdentifier(),
                    notification.Name);
            }
        }
    }

    public async Task DeleteNotification(EntityDto<Guid> input)
    {
        var notification = await userNotificationManager.GetUserNotificationAsync(this.AbpSession.TenantId, input.Id);
        if (notification == null)
        {
            return;
        }

        if (notification.UserId != this.AbpSession.GetUserId())
        {
            throw new UserFriendlyException(this.L("ThisNotificationDoesntBelongToYou"));
        }

        await userNotificationManager.DeleteUserNotificationAsync(this.AbpSession.TenantId, input.Id);
    }

    public async Task DeleteAllUserNotifications(DeleteAllUserNotificationsInput input)
    {
        await userNotificationManager.DeleteAllUserNotificationsAsync(this.AbpSession.ToUserIdentifier(),
            input.State,
            input.StartDate,
            input.EndDate);
    }

    [AbpAuthorize(AppPermissions.NotificationsUpdate)]
    public async Task CreateMassNotification(CreateMassNotificationInput input)
    {
        if (input.TargetNotifiers.IsNullOrEmpty())
        {
            throw new UserFriendlyException(this.L("MassNotificationTargetNotifiersFieldIsRequiredMessage"));
        }

        var userIds = new List<UserIdentifier>();

        if (!input.UserIds.IsNullOrEmpty())
        {
            userIds.AddRange(input.UserIds.Select(i => new UserIdentifier(this.AbpSession.TenantId, i)));
        }

        if (userIds.Count == 0)
        {
            if (input.OrganizationUnitIds.IsNullOrEmpty())
            {
                // tried to get users from organization, but could not find any user
                throw new UserFriendlyException(this.L("MassNotificationNoUsersFoundInOrganizationUnitMessage"));
            }

            throw new UserFriendlyException(this.L("MassNotificationUserOrOrganizationUnitFieldIsRequiredMessage"));
        }

        var targetNotifiers = new List<Type>();

        foreach (var notifier in notificationConfiguration.Notifiers)
        {
            if (input.TargetNotifiers.Contains(notifier.FullName))
            {
                targetNotifiers.Add(notifier);
            }
        }

        await appNotifier.SendMassNotificationAsync(
            input.Message,
            userIds.DistinctBy(u => u.UserId).ToArray(),
            input.Severity,
            targetNotifiers.ToArray()
        );
    }

    [AbpAuthorize]
    public async Task CreateNewVersionReleasedNotification()
    {
        var args = new SendNotificationToAllUsersArgs
        {
            NotificationName = AppNotificationNames.NewVersionAvailable,
            Message = this.L("NewVersionAvailableNotificationMessage")
        };

        await backgroundJobManager
            .EnqueueAsync<SendNotificationToAllUsersBackgroundJob, SendNotificationToAllUsersArgs>(args);
    }

    public List<string> GetAllNotifiers()
    {
        return notificationConfiguration.Notifiers.Select(n => n.FullName).ToList();
    }

    [AbpAuthorize(AppPermissions.NotificationsRead)]
    public async Task<GetPublishedNotificationsOutput> GetNotificationsPublishedByUser(
        GetPublishedNotificationsInput input)
    {
        var queryForNotPublishedNotifications = notificationRepository.GetAll()
            .Where(n => n.NotificationName == AppNotificationNames.MassNotification);

        if (input.StartDate.HasValue)
        {
            queryForNotPublishedNotifications = queryForNotPublishedNotifications
                .Where(x => x.CreationTime >= input.StartDate);
        }

        if (input.EndDate.HasValue)
        {
            queryForNotPublishedNotifications = queryForNotPublishedNotifications
                .Where(x => x.CreationTime <= input.EndDate);
        }

        var result = new List<GetNotificationsCreatedByUserOutput>();

        var unPublishedNotifications = await this.AsyncQueryableExecuter.ToListAsync(
            queryForNotPublishedNotifications
                .Select(x =>
                    new GetNotificationsCreatedByUserOutput
                    {
                        Data = x.Data,
                        Severity = x.Severity,
                        NotificationName = x.NotificationName,
                        DataTypeName = x.DataTypeName,
                        IsPublished = false,
                        CreationTime = x.CreationTime
                    })
        );

        result.AddRange(unPublishedNotifications);

        var queryForPublishedNotifications = tenantNotificationRepository.GetAll()
            .Where(n => n.NotificationName == AppNotificationNames.MassNotification);

        if (input.StartDate.HasValue)
        {
            queryForPublishedNotifications = queryForPublishedNotifications
                .Where(x => x.CreationTime >= input.StartDate);
        }

        if (input.EndDate.HasValue)
        {
            queryForPublishedNotifications = queryForPublishedNotifications
                .Where(x => x.CreationTime <= input.EndDate);
        }

        queryForPublishedNotifications = queryForPublishedNotifications
            .OrderByDescending(n => n.CreationTime);

        var publishedNotifications = await this.AsyncQueryableExecuter.ToListAsync(queryForPublishedNotifications
            .Select(x =>
                new GetNotificationsCreatedByUserOutput
                {
                    Data = x.Data,
                    Severity = x.Severity,
                    NotificationName = x.NotificationName,
                    DataTypeName = x.DataTypeName,
                    IsPublished = true,
                    CreationTime = x.CreationTime
                })
        );

        result.AddRange(publishedNotifications);
        return new GetPublishedNotificationsOutput(result);
    }

    [AbpAuthorize(AppPermissions.NotificationsRead)]
    public async Task<PagedResultDto<MassNotificationUserLookupTableDto>> GetAllUserForLookupTable(
        GetAllForLookupTableInput input)
    {
        var query = userRepository.GetAll()
            .WhereIf(!string.IsNullOrWhiteSpace(input.Filter),
                e =>
                    (e.Name != null && e.Name.Contains(input.Filter)) ||
                    (e.Surname != null && e.Surname.Contains(input.Filter)) ||
                    (e.EmailAddress != null && e.EmailAddress.Contains(input.Filter))
            );

        var totalCount = await query.CountAsync();

        var userList = await query
            .PageBy(input)
            .ToListAsync();

        var lookupTableDtoList = new List<MassNotificationUserLookupTableDto>();
        foreach (var user in userList)
        {
            lookupTableDtoList.Add(new MassNotificationUserLookupTableDto
            {
                Id = user.Id,
                DisplayName = user.Name + " " + user.Surname + " (" + user.EmailAddress + ")"
            });
        }

        return new PagedResultDto<MassNotificationUserLookupTableDto>(
            totalCount,
            lookupTableDtoList
        );
    }

    [AbpAuthorize(AppPermissions.NotificationsRead)]
    public async Task<PagedResultDto<MassNotificationOrganizationUnitLookupTableDto>>
        GetAllOrganizationUnitForLookupTable(GetAllForLookupTableInput input)
    {
        var query = organizationUnitRepository.GetAll()
            .WhereIf(!string.IsNullOrWhiteSpace(input.Filter),
                e => e.DisplayName != null && e.DisplayName.Contains(input.Filter));

        var totalCount = await query.CountAsync();

        var organizationUnitList = await query
            .PageBy(input)
            .ToListAsync();

        var lookupTableDtoList = new List<MassNotificationOrganizationUnitLookupTableDto>();
        foreach (var organizationUnit in organizationUnitList)
        {
            lookupTableDtoList.Add(new MassNotificationOrganizationUnitLookupTableDto
            {
                Id = organizationUnit.Id,
                DisplayName = organizationUnit.DisplayName
            });
        }

        return new PagedResultDto<MassNotificationOrganizationUnitLookupTableDto>(
            totalCount,
            lookupTableDtoList
        );
    }

    private void CategorizeNotification(UserNotification userNotification, CategorizedNotificationsOutput output)
    {
        switch (userNotification.Notification.NotificationName)
        {
            case AppNotificationNames.ImportantNotification:
                output.Important.Add(userNotification);
                if (userNotification.State == UserNotificationState.Unread)
                {
                    output.ImportantUnreadCount++;
                }

                break;
            case AppNotificationNames.EventNotification:
                output.Event.Add(userNotification);
                if (userNotification.State == UserNotificationState.Unread)
                {
                    output.EventUnreadCount++;
                }

                break;
            case AppNotificationNames.GoalNotification:
                output.Goal.Add(userNotification);
                if (userNotification.State == UserNotificationState.Unread)
                {
                    output.GoalUnreadCount++;
                }

                break;
            case AppNotificationNames.InformationNotification:
                output.Information.Add(userNotification);
                if (userNotification.State == UserNotificationState.Unread)
                {
                    output.InformationUnreadCount++;
                }

                break;
        }
    }
}