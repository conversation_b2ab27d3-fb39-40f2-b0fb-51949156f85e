namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByNameSpecification : Specification<Client>
{
    private readonly string? name;

    public ClientByNameSpecification(string? name) => this.name = name;

    protected override bool Include => !string.IsNullOrWhiteSpace(this.name);

    public override Expression<Func<Client, bool>> ToExpression()
        => client => (client.PersonalData!.FirstName + " " + client.PersonalData.LastName).ToLower()
            .Contains(this.name!.ToLower());
}