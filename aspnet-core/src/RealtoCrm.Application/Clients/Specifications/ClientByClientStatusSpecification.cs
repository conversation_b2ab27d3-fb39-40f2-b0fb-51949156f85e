namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;
using Expressions;

public class ClientByClientStatusSpecification(ClientStatus? clientStatus, ClientOwnership? clientOwnership)
    : Specification<Client>
{
    protected override bool Include => clientStatus != null && clientOwnership is ClientOwnership.My;

    public override Expression<Func<Client, bool>> ToExpression()
    {
        var expression = clientStatus switch
        {
            ClientStatus.Contact => client => true,
            ClientStatus.Potential => ClientExpressions.IsPotentialClient(),
            ClientStatus.Meeting => ClientExpressions.IsClientWithMeetings(),
            ClientStatus.Contract => ClientExpressions.IsClientWithContract(),
            ClientStatus.Viewing => ClientExpressions.IsClientWithViewings(),
            ClientStatus.Deposit => ClientExpressions.IsClientWithDeposits(),
            ClientStatus.Deal => ClientExpressions.IsClientWithDeals(),
            _ => client => false
        };

        return expression;
    }
}