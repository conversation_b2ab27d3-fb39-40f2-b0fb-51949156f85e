namespace RealtoCrm.Clients.Models;

using AutoMapper;
using Mapping;
using System;

public class ClientSourceCategoryResponseModel : IMapFrom<ClientSourceCategory>, IMapExplicitly
{
    public string SourceCategoryName { get; set; } = default!;

    public string EmployeeFirstName { get; set; } = default!;
    
    public string EmployeeLastName { get; set; } = default!;
    
    public string? EmployeePhoneNumber { get; set; }
    
    public int? EmployeeId { get; set; }

    public long? EmployeeUserId { get; set; }

    public string? SourceCategoryParentName { get; set; }

    public bool? EmployeeIsDeleted { get; set; }
    
    public DateTime CreationTime { get; set; }
    
    public int? TeamId { get; set; }
    
    public int? DepartmentId { get; set; }
    
    public int? DivisionId { get; set; }
    
    public int? CompanyId { get; set; }

    public int? ClientTenantId { get; set; }
    
    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<ClientSourceCategory, ClientSourceCategoryResponseModel>()
            .ForMember(dest => dest.EmployeeId, opt
                => opt.MapFrom(src => src.EmployeeId))
            .ForMember(dest => dest.EmployeePhoneNumber, opt
                => opt.MapFrom(src => src.Employee != null ? src.Employee.PhoneNumber : null))
            .ForMember(dest => dest.EmployeeUserId, opt
                => opt.MapFrom(src => src.Employee != null ? src.Employee.UserAccount.UserId : (long?)null))
            .ForMember(dest => dest.EmployeeIsDeleted, opt
                => opt.MapFrom(src => src.Employee != null ? (bool?)src.Employee.IsDeleted : null))
            .ForMember(dest => dest.TeamId, opt 
                => opt.MapFrom(src => src.Employee != null ? src.Employee.TeamId : null))
            .ForMember(dest => dest.DepartmentId, opt 
                => opt.MapFrom(src => src.Employee != null ? src.Employee.DepartmentId : null))
            .ForMember(dest => dest.DivisionId, opt 
                => opt.MapFrom(src => src.Employee != null ? src.Employee.DivisionId : null))
            .ForMember(dest => dest.CompanyId, opt 
                => opt.MapFrom(src => src.Employee != null ? src.Employee.CompanyId : null));
}