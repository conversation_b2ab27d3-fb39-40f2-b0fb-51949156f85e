namespace RealtoCrm.Clients.Models;

using System.Collections.Generic;
using System.Linq;
using Mapping;
using Tenants.Dashboard;
using AutoMapper;
using static CosherConsts.ClientTypes;

public class ClientDetailsResponseModel : IMapFrom<Client>, IMapExplicitly
{
    public int Id { get; set; }

    public int TenantId { get; init; }

    public int? NationalityId { get; init; }

    public string? NationalityName { get; init; }

    public int TypeId { get; init; }

    public string? TypeName { get; init; }

    public ICollection<ClientPreferenceResponseModel> ClientsPreferences { get; init; } =
        new List<ClientPreferenceResponseModel>();

    public ClientLegalEntityResponseModel? LegalEntity { get; init; }

    public ClientPersonalDataResponseModel? PersonalData { get; init; }

    public ClientRefferalRequestResponseModel? ClientRefferal { get; init; }

    public ICollection<ClientSourceCategoryResponseModel> ClientsSourceCategories { get; init; } =
        new List<ClientSourceCategoryResponseModel>();

    public static string LastContact => "Преди 3 седмици";

    public static string ExpiringRights => "След един ден";

    public static byte CommissionPercent => 3;

    public static byte Potential => (byte)DashboardRandomDataGenerator.GetRandomInt(0, 10);

    //TODO: Will be changed when logics for contract, meetings and etc are done.
    public static string Status => "потенциал";
    
    public string? UnifiedIdentificationCode { get; init; } = default!;
    
    public int? ClientMappingAdminId { get; init; }


    public ICollection<ClientContactDetailResponseModel> ContactDetails { get; init; } =
        new List<ClientContactDetailResponseModel>();

    public ICollection<ClientSourceDetailResponseModel> SourceDetails { get; init; } =
        new List<ClientSourceDetailResponseModel>();

    public ICollection<ClientCardTypeResponseModel> ClientsCardTypes { get; init; } =
        new List<ClientCardTypeResponseModel>();

    public ICollection<ClientWorkplaceResponseModel> ClientsWorkplaces { get; init; } =
        new List<ClientWorkplaceResponseModel>();

    public ICollection<ClientTagCategoryResponseModel> ClientsTags { get; init; } =
        new List<ClientTagCategoryResponseModel>();

    public ICollection<ClientsRelatedClientsResponseModel> RelatedClients { get; init; } =
        new List<ClientsRelatedClientsResponseModel>();

    public ICollection<ClientsRelatedClientsResponseModel> RelatedCompanies { get; init; } =
        new List<ClientsRelatedClientsResponseModel>();

    public ICollection<ClientsRelatedClientsResponseModel> RelatedClientOfCompanies { get; init; } =
        new List<ClientsRelatedClientsResponseModel>();

    public ICollection<ClientSectorResponseModel> ClientsSectors { get; init; } = new List<ClientSectorResponseModel>();

    public ICollection<ClientAddressResponseModel> ClientAddresses { get; init; } =
        new List<ClientAddressResponseModel>();

    public ICollection<RelatedClientOfClientsResponseModel> RelatedClientOfClients { get; init; } =
        new List<RelatedClientOfClientsResponseModel>();

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Client, ClientDetailsResponseModel>()
            .ForMember(m => m.RelatedCompanies, cfg => cfg
                .MapFrom(m => m.RelatedClients.Where(x => x.ClientRelatedClient.Type.Name == LegalClientTypeName)))
            .ForMember(m => m.RelatedClientOfCompanies, cfg => cfg
                .MapFrom(m => m.ClientsRelatedClients.Where(x => x.Client.Type.Name == LegalClientTypeName)))
            .ForMember(m => m.RelatedClients, cfg => cfg
                .MapFrom(m => m.RelatedClients.Where(x => x.ClientRelatedClient.Type.Name == PersonalClientTypeName)))
            .ForMember(m => m.ClientsSourceCategories, cfg => cfg
                .MapFrom(m => m.ClientsSourceCategories
                    .Where(c => c.Employee != null && !c.Employee.IsDeleted)))
            .ForMember(m => m.RelatedClientOfClients,
                opt => opt.MapFrom(src =>
                    src.ClientsRelatedClients.Where(x => x.Client.Type.Name == PersonalClientTypeName)))
            .ForMember(dest => dest.UnifiedIdentificationCode, opt 
                => opt.MapFrom(src => src.Type.Name == LegalClientTypeName
                    ? src.LegalEntity!.UnifiedIdentificationCode
                    : null));
}