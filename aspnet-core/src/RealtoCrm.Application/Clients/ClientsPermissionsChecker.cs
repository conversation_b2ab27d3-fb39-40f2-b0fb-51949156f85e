using System.Collections.Generic;

namespace RealtoCrm.Clients;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Authorization;
using RealtoCrm.Employees.Models;
using EntityFrameworkCore;
using Models;


public class ClientsPermissionsChecker(IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : RealtoCrmAppServiceBase, IClientsPermissionsChecker
{
    private RealtoCrmDbContext Data => dbContextProvider.GetDbContext();
    
    public async Task<bool> CanPerformAction(
        int clientId,
        long userId,
        string[] permissions,
        bool checkForSameTenant = true)
    {
        
        if (await this.PermissionChecker.IsGrantedAsync(AppPermissions.AdministrationFullAccess ))
        {
            return true;
        }

        var clientEmployees = await this.GetClientEmployee(clientId);
        var employee = await this.GetEmployee(userId);

        var hasPermission = permissions.Length == 0 ||
                            (await Task.WhenAll(permissions.Select(p => this.PermissionChecker.IsGrantedAsync(p))))
                            .Any(result => result);
        
        var areInSameTenant = clientEmployees?.Any(x => x.ClientTenantId == employee?.TenantId) ?? false;

        if ((clientEmployees?.Any(x => x.EmployeeUserId == this.AbpSession.UserId) ?? false) && areInSameTenant)
        {
            return checkForSameTenant
                ? areInSameTenant && hasPermission
                : hasPermission;
        }
        
        var clientTeamIds = clientEmployees?.Select(x => x.TeamId)
            .Where(id => id.HasValue).Select(id => id.Value).ToList();
        var clientDepartmentIds = clientEmployees?.Select(x => x.DepartmentId)
            .Where(id => id.HasValue).Select(id => id.Value).ToList();
        var clientDivisionIds = clientEmployees?.Select(x => x.DivisionId)
            .Where(id => id.HasValue).Select(id => id.Value).ToList();

        
        var canPerformAction = (
            await this.CanPerformActionInDepartment(clientDepartmentIds, employee?.DepartmentId) ||
            await this.CanPerformActionInTeam(clientTeamIds, employee?.TeamId) ||
            await this.CanPerformActionInDivision(clientDivisionIds, employee?.DivisionId)
        ) && hasPermission;

        return checkForSameTenant ? canPerformAction && areInSameTenant : hasPermission;
    }
    
    private async Task<List<ClientSourceCategoryResponseModel>?> GetClientEmployee(int clientId)
        => await this.ObjectMapper
            .ProjectTo<ClientSourceCategoryResponseModel>(this
                .Data
                .ClientsSourceCategories
                .AsNoTracking()
                .Where(c => c.ClientId == clientId))
            .ToListAsync();


    private async Task<EmployeeTeamDetailsResponseModel?> GetEmployee(long userId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeTeamDetailsResponseModel>(this
                .Data
                .Employees
                .AsNoTracking()
                .Where(e => e.UserAccount.UserId == userId))
            .FirstOrDefaultAsync();
    
    private async Task<bool> CanPerformActionInTeam(List<int>? clientTeamIds, int? currentEmployeeTeamId)
        => currentEmployeeTeamId.HasValue && (clientTeamIds?.Any(x => x == currentEmployeeTeamId) ?? false) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.ContactActionsTeam);

    private async Task<bool> CanPerformActionInDepartment(List<int>? clientDepartmentIds, int? currentEmployeeDepartmentId)
        => currentEmployeeDepartmentId.HasValue && (clientDepartmentIds?.Any(x => x == currentEmployeeDepartmentId) ?? false) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.ContactActionsDepartment);

    private async Task<bool> CanPerformActionInDivision(List<int>? clientDivisionIds, int? currentEmployeeDivisionId)
        => currentEmployeeDivisionId.HasValue && (clientDivisionIds?.Any(x => x == currentEmployeeDivisionId) ?? false) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.ContactActionsDivision);
}