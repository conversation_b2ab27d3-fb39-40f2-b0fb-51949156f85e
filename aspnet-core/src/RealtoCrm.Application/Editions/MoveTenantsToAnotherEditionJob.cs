using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.BackgroundJobs;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Events.Bus;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Notifications;

namespace RealtoCrm.Editions;

public class MoveTenantsToAnotherEditionJob(
    IRepository<Tenant> tenantRepository,
    EditionManager editionManager,
    IAppNotifier appNotifier,
    IUnitOfWorkManager unitOfWorkManager)
    : AsyncBackgroundJob<MoveTenantsToAnotherEditionJobArgs>, ITransientDependency
{
    public IEventBus EventBus { get; set; } = NullEventBus.Instance;

    public override async Task ExecuteAsync(MoveTenantsToAnotherEditionJobArgs args)
    {
        if (args.SourceEditionId == args.TargetEditionId)
        {
            return;
        }

        List<int> tenantIds;

        using (var uow = unitOfWorkManager.Begin())
        {
            tenantIds = tenantRepository.GetAll()
                .Where(t => t.EditionId == args.SourceEditionId)
                .Select(t => t.Id)
                .ToList();
                
            await uow.CompleteAsync();
        }

        if (!tenantIds.Any())
        {
            return;
        }

        var changedTenantCount = await this.ChangeEditionOfTenantsAsync(tenantIds, args.SourceEditionId, args.TargetEditionId);

        if (changedTenantCount != tenantIds.Count)
        {
            this.Logger.Warn($"Unable to move all tenants from edition {args.SourceEditionId} to edition {args.TargetEditionId}");
            return;
        }

        await this.NotifyUserAsync(args);
    }

    private async Task<int> ChangeEditionOfTenantsAsync(List<int> tenantIds, int sourceEditionId, int targetEditionId)
    {
        var changedTenantCount = 0;

        foreach (var tenantId in tenantIds)
        {
            using (var uow = unitOfWorkManager.Begin())
            {
                var changed = await this.ChangeEditionOfTenantAsync(tenantId, sourceEditionId, targetEditionId);
                if (changed)
                {
                    changedTenantCount++;
                }

                await uow.CompleteAsync();
            }
        }

        return changedTenantCount;
    }

    private async Task NotifyUserAsync(MoveTenantsToAnotherEditionJobArgs args)
    {
        using (var uow = unitOfWorkManager.Begin())
        {
            var sourceEdition = await editionManager.GetByIdAsync(args.SourceEditionId);
            var targetEdition = await editionManager.GetByIdAsync(args.TargetEditionId);

            await appNotifier.TenantsMovedToEdition(
                args.User,
                sourceEdition.DisplayName,
                targetEdition.DisplayName
            );

            await uow.CompleteAsync();
        }
    }

    private async Task<bool> ChangeEditionOfTenantAsync(int tenantId, int sourceEditionId, int targetEditionId)
    {
        try
        {
            var tenant = await tenantRepository.GetAsync(tenantId);
            tenant.EditionId = targetEditionId;

            await this.CurrentUnitOfWork.SaveChangesAsync();

            await this.EventBus.TriggerAsync(new TenantEditionChangedEventData
            {
                TenantId = tenant.Id,
                OldEditionId = sourceEditionId,
                NewEditionId = targetEditionId
            });

            return true;
        }
        catch (Exception exception)
        {
            this.Logger.Error(exception.Message, exception);
            return false;
        }
    }
}