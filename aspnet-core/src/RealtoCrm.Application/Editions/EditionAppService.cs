using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.Application.Editions;
using Abp.Application.Features;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.BackgroundJobs;
using Abp.Collections.Extensions;
using Abp.Domain.Repositories;
using Abp.Runtime.Session;
using Abp.UI;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Authorization;
using RealtoCrm.Editions.Dto;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm.Editions;

public class EditionAppService(
    EditionManager editionManager,
    IRepository<SubscribableEdition> editionRepository,
    IRepository<Tenant> tenantRepository,
    IBackgroundJobManager backgroundJobManager)
    : RealtoCrmAppServiceBase, IEditionAppService
{
    [AbpAuthorize]
    public async Task<ListResultDto<EditionListDto>> GetEditions()
    {
        var editions = await (from edition in editionRepository.GetAll()
            join expiringEdition in editionRepository.GetAll() on edition.ExpiringEditionId equals expiringEdition.Id into expiringEditionJoined
            from expiringEdition in expiringEditionJoined.DefaultIfEmpty()
            select new
            {
                Edition = edition,
                expiringEditionDisplayName = expiringEdition.DisplayName
            }).ToListAsync();

        var result = new List<EditionListDto>();

        foreach (var edition in editions)
        {
            var resultEdition = this.ObjectMapper.Map<EditionListDto>(edition.Edition);
            resultEdition.ExpiringEditionDisplayName = edition.expiringEditionDisplayName;

            result.Add(resultEdition);
        }

        return new ListResultDto<EditionListDto>(result);
    }

    [AbpAuthorize]
    public async Task<GetEditionEditOutput> GetEditionForEdit(NullableIdDto input)
    {
        var features = this.FeatureManager.GetAll()
            .Where(f => f.Scope.HasFlag(FeatureScopes.Edition));

        EditionEditDto editionEditDto;
        List<NameValue> featureValues;

        if (input.Id.HasValue) //Editing existing edition?
        {
            var edition = await editionManager.FindByIdAsync(input.Id.Value);
            featureValues = (await editionManager.GetFeatureValuesAsync(input.Id.Value)).ToList();
            editionEditDto = this.ObjectMapper.Map<EditionEditDto>(edition);
        }
        else
        {
            editionEditDto = new EditionEditDto();
            featureValues = features.Select(f => new NameValue(f.Name, f.DefaultValue)).ToList();
        }

        var featureDtos = this.ObjectMapper.Map<List<FlatFeatureDto>>(features).OrderBy(f => f.DisplayName).ToList();

        return new GetEditionEditOutput
        {
            Edition = editionEditDto,
            Features = featureDtos,
            FeatureValues = featureValues.Select(fv => new NameValueDto(fv)).ToList()
        };
    }

    [AbpAuthorize]
    public async Task CreateEdition(CreateEditionDto input)
    {
        await this.CreateEditionAsync(input);
    }

    [AbpAuthorize]
    public async Task UpdateEdition(UpdateEditionDto input)
    {
        await this.UpdateEditionAsync(input);
    }

    [AbpAuthorize]
    public async Task DeleteEdition(EntityDto input)
    {
        var tenantCount = await tenantRepository.CountAsync(t => t.EditionId == input.Id);
        if (tenantCount > 0)
        {
            throw new UserFriendlyException(this.L("ThereAreTenantsSubscribedToThisEdition"));
        }

        var edition = await editionManager.GetByIdAsync(input.Id);
        await editionManager.DeleteAsync(edition);
    }

    [AbpAuthorize]
    public async Task MoveTenantsToAnotherEdition(MoveTenantsToAnotherEditionDto input)
    {
        await backgroundJobManager.EnqueueAsync<MoveTenantsToAnotherEditionJob, MoveTenantsToAnotherEditionJobArgs>(new MoveTenantsToAnotherEditionJobArgs
        {
            SourceEditionId = input.SourceEditionId,
            TargetEditionId = input.TargetEditionId,
            User = this.AbpSession.ToUserIdentifier()
        });
    }

    [AbpAuthorize]
    public async Task<List<SubscribableEditionComboboxItemDto>> GetEditionComboboxItems(int? selectedEditionId = null, bool addAllItem = false, bool onlyFreeItems = false)
    {
        var editions = await editionManager.Editions.ToListAsync();
        var subscribableEditions = editions.Cast<SubscribableEdition>()
            .WhereIf(onlyFreeItems, e => e.IsFree)
            .OrderBy(e => e.MonthlyPrice);

        var editionItems =
            new ListResultDto<SubscribableEditionComboboxItemDto>(subscribableEditions
                .Select(e => new SubscribableEditionComboboxItemDto(e.Id.ToString(), e.DisplayName, e.IsFree)).ToList()).Items.ToList();

        var defaultItem = new SubscribableEditionComboboxItemDto("", this.L("NotAssigned"), null);
        editionItems.Insert(0, defaultItem);

        if (addAllItem)
        {
            editionItems.Insert(0, new SubscribableEditionComboboxItemDto("-1", "- " + this.L("All") + " -", null));
        }

        if (selectedEditionId.HasValue)
        {
            var selectedEdition = editionItems.FirstOrDefault(e => e.Value == selectedEditionId.Value.ToString());
            if (selectedEdition != null)
            {
                selectedEdition.IsSelected = true;
            }
        }
        else
        {
            editionItems[0].IsSelected = true;
        }

        return editionItems;
    }

    public async Task<int> GetTenantCount(int editionId)
    {
        return await tenantRepository.CountAsync(t => t.EditionId == editionId);
    }

    [AbpAuthorize]
    protected virtual async Task CreateEditionAsync(CreateEditionDto input)
    {
        var edition = this.ObjectMapper.Map<SubscribableEdition>(input.Edition);

        if (edition.ExpiringEditionId.HasValue)
        {
            var expiringEdition = (SubscribableEdition)await editionManager.GetByIdAsync(edition.ExpiringEditionId.Value);
            if (!expiringEdition.IsFree)
            {
                throw new UserFriendlyException(this.L("ExpiringEditionMustBeAFreeEdition"));
            }
        }

        await editionManager.CreateAsync(edition);
        await this.CurrentUnitOfWork.SaveChangesAsync(); //It's done to get Id of the edition.

        await this.SetFeatureValues(edition, input.FeatureValues);
    }

    [AbpAuthorize]
    protected virtual async Task UpdateEditionAsync(UpdateEditionDto input)
    {
        if (input.Edition.Id != null)
        {
            var edition = await editionManager.GetByIdAsync(input.Edition.Id.Value);

            edition.DisplayName = input.Edition.DisplayName;

            await this.SetFeatureValues(edition, input.FeatureValues);
        }
    }

    private Task SetFeatureValues(Edition edition, List<NameValueDto> featureValues)
    {
        return editionManager.SetFeatureValuesAsync(edition.Id,
            featureValues.Select(fv => new NameValue(fv.Name, fv.Value)).ToArray());
    }
}