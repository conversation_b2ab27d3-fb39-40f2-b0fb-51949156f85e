namespace RealtoCrm.EstateTypes;

using System.Collections.Generic;
using System.Threading.Tasks;
using Estates;
using Models;

public interface IEstateTypesAppService : IDataCrudAppService<
    int,
    EstateType,
    EstateTypeRequestModel,
    EstateTypeRequestModel,
    EstateTypesPaginatedRequestModel,
    EstateTypeDetailsResponseModel,
    EstateTypeListingResponseModel>
{
    Task<IEnumerable<EstateTypeWithCategoryResponseModel>> GetWithCategoryByNamesAsync(IEnumerable<string> names);
}