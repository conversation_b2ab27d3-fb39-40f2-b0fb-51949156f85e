namespace RealtoCrm.AddressesToEstateCategoriesSettings;

using System.Collections.Generic;
using System.Threading.Tasks;
using AddressesEstateCharacteristics;
using Models;

public interface IAddressesToEstateCategoryCharacteristicsAppService  : IDataCrudAppService<
    int,
    AddressToEstateCategoryCharacteristic,
    AddressesToEstateCategoryCharacteristicsRequestModel,
    AddressesToEstateCategoryCharacteristicsRequestModel,
    AddressesToEstateCategoryCharacteristicsPaginatedRequestModel,
    AddressesToEstateCharacteristicsResponseModel,
    AddressesToEstateCharacteristicsResponseModel>
{
    Task<List<string>> GetPropertyNamesAsync();
    
    Task<ICollection<AddressesToEstateCharacteristicsResponseModel>> GetPropertiesByEstateCategoryAndTenantIdAsync(int categoryId); 
}