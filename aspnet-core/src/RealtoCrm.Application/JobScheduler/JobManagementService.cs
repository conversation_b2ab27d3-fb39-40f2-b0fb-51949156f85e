namespace RealtoCrm.JobScheduler;

using System.Threading.Tasks;
using Quartz;
using static CosherConsts.CronJobs;

public class JobManagementService(ISchedulerFactory schedulerFactory) : IJobManagementService
{
    public async Task ScheduleCronJobAsync<TJob>(
        string jobName,
        string cronExpression,
        JobDataMap? jobData = null)
        where TJob : IJob
    {
        var jobKey = new JobKey(jobName);

        var scheduler = await schedulerFactory.GetScheduler();

        var jobExists = await scheduler.CheckExists(jobKey);

        if (jobExists)
        {
            await scheduler.DeleteJob(jobKey);
        }

        var jobDetail = JobBuilder
            .Create<TJob>()
            .WithIdentity(jobKey)
            .Build();

        if (jobData is not null)
        {
            jobDetail.JobDataMap.PutAll(jobData);
        }

        var trigger = TriggerBuilder
            .Create()
            .WithIdentity(string.Format(TriggerKeyFormat, jobName))
            .WithCronSchedule(cronExpression)
            .Build();

        await scheduler.ScheduleJob(jobDetail, trigger);
    }

    public async Task UnscheduleJobAsync(string jobName)
    {
        var triggerKey = new TriggerKey(string.Format(TriggerKeyFormat, jobName));

        var scheduler = await schedulerFactory.GetScheduler();

        await scheduler.UnscheduleJob(triggerKey);
    }

    public async Task PauseJobAsync(string jobName)
    {
        var jobKey = new JobKey(jobName);

        var scheduler = await schedulerFactory.GetScheduler();

        await scheduler.PauseJob(jobKey);
    }

    public async Task ResumeJobAsync(string jobName)
    {
        var jobKey = new JobKey(jobName);

        var scheduler = await schedulerFactory.GetScheduler();

        await scheduler.ResumeJob(jobKey);
    }
}