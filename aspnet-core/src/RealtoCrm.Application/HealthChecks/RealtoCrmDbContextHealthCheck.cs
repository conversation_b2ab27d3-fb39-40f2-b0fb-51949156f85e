using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using RealtoCrm.EntityFrameworkCore;

namespace RealtoCrm.HealthChecks;

public class RealtoCrmDbContextHealthCheck(DatabaseCheckHelper checkHelper) : IHealthCheck
{
    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        if (checkHelper.Exist("db"))
        {
            return Task.FromResult(HealthCheckResult.Healthy("RealtoCrmDbContext connected to database."));
        }

        return Task.FromResult(HealthCheckResult.Unhealthy("RealtoCrmDbContext could not connect to database"));
    }
}