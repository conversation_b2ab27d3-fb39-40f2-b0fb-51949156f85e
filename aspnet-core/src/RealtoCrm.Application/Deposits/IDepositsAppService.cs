namespace RealtoCrm.Deposits;

using System.Collections.Generic;
using System.Threading.Tasks;
using Comments;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;

public interface IDepositsAppService : ICommentsAppService<
    int,
    Deposit,
    DepositComment,
    DepositRequestModel,
    DepositRequestModel,
    DepositsPaginatedRequestModel,
    DepositDetailsResponseModel,
    DepositListingResponseModel>
{
    Task<int> RejectAsync(int id, DepositRejectRequestModel request);

    Task<int> AnnexAsync(int id, DepositAnnexRequestModel request);

    Task<int> AdoptAsync(int id);

    Task<int> RedirectAsync(int id, DepositRequestModel request);

    Task<int> TransferToOwnerAsync(int id);

    Task UploadContractsAsync(int id, [FromForm] IFormFileCollection files);

    Task<DepositForDealResponseModel?> GetForDealAsync(int id);

    Task<IEnumerable<DepositListingResponseModel>> GetByClientIdAsync(int clientId);
}