namespace RealtoCrm.Deposits;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Authorization;
using Comments;
using Common.Attributes;
using Contracts;
using Employees;
using Employees.Models;
using EntityFrameworkCore;
using Expressions;
using Extensions;
using Files.Uploader;
using Matches;
using Matches.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Models;
using Nomenclatures.ContractTypes;
using Nomenclatures.DepositStatuses;
using Nomenclatures.MatchStatuses;
using Notifications;
using Providers;
using Specifications;
using static CosherConsts.ContractTypes;
using static CosherConsts.DepositStatuses;
using static CosherConsts.MatchStatuses;

public class DepositsAppService(
    IDateTimeService dateTimeService,
    IEmployeesAppService employeesAppService,
    IMatchesAppService matchesAppService,
    IMatchStatusesAppService matchStatusesAppService,
    IContractTypesAppService contractTypesAppService,
    IFilesUploaderAppService filesUploaderAppService,
    IAppNotifier appNotifier,
    IDepositStatusesAppService depositStatusesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : CommentsAppService<
        int,
        Deposit,
        DepositComment,
        DepositRequestModel,
        DepositRequestModel,
        DepositsPaginatedRequestModel,
        DepositDetailsResponseModel,
        DepositListingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder), IDepositsAppService
{
    private const string BlobContainerName = "deposits";

    protected override bool IgnoreQueryFilters => true;

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.DepositCreate, AppPermissions.DepositActions)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.DepositUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.DepositUpdate)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.DepositRead)
            .ConfigureAuthorize(
                nameof(this.UploadContractsAsync),
                AppPermissions.DepositAddDocument,
                AppPermissions.DepositActions)
            .AddCustomAttribute(nameof(this.UploadContractsAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.RejectAsync), AppPermissions.DepositActions)
            .ConfigureAuthorize(nameof(this.AnnexAsync), AppPermissions.DepositActions)
            .ConfigureAuthorize(nameof(this.AdoptAsync), AppPermissions.DepositActions)
            .ConfigureAuthorize(nameof(this.RedirectAsync), AppPermissions.DepositActions)
            .ConfigureAuthorize(nameof(this.TransferToOwnerAsync), AppPermissions.DepositActions);

    public override async Task<int> CreateAsync(DepositRequestModel request)
    {
        var match = await matchesAppService.GetBySearchAndOfferAsync(request.SearchId, request.OfferId);
        var depositedMatchStatusId = await matchStatusesAppService.GetIdByNameAsync(DepositedMatchStatusName);

        request.MatchId = match?.Id ?? await matchesAppService.CreateAsync(new MatchRequestModel
        {
            SearchId = request.SearchId,
            OfferId = request.OfferId,
            StatusId = depositedMatchStatusId
        });

        request.DepositStatusId = await depositStatusesAppService.GetIdByNameAsync(ActiveDepositStatusName);
        request.TenantId = this.GetTenantId();

        var createdDepositId = await base.CreateAsync(request);

        var employeeWithUserId =
            await employeesAppService.GetEmployeeForNotificationById(request.OfferEmployeeId);

        await this.SendDepositCreatedNotificationAsync(
            request,
            employeeWithUserId,
            createdDepositId);

        return createdDepositId;
    }

    public async Task<int> RejectAsync(int id, DepositRejectRequestModel request)
    {
        var deposit = await this.GetAsync(id);

        if (deposit is null)
        {
            throw new EntityNotFoundException(typeof(Deposit), id);
        }

        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        request.DepositsComments.ForEach(dc => dc.EmployeeId = employeeId);
        request.DepositStatusId = await depositStatusesAppService.GetIdByNameAsync(RejectedDepositStatusName);

        this.ObjectMapper.Map(request, deposit);

        await this.Data.SaveChangesAsync();

        return deposit.Id;
    }

    public async Task<int> AnnexAsync(int id, DepositAnnexRequestModel request)
    {
        var deposit = await this.GetAsync(id);

        if (deposit is null)
        {
            throw new EntityNotFoundException(typeof(Deposit), id);
        }

        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        request.DepositsComments.ForEach(dc => dc.EmployeeId = employeeId);
        request.DepositStatusId = await depositStatusesAppService.GetIdByNameAsync(AnnexedDepositStatusName);

        this.ObjectMapper.Map(request, deposit);

        await this.Data.SaveChangesAsync();

        return deposit.Id;
    }

    public async Task<int> AdoptAsync(int id)
    {
        var deposit = await this.GetAsync(id);

        if (deposit is null)
        {
            throw new EntityNotFoundException(typeof(Deposit), id);
        }

        deposit.StatusChangeDate = dateTimeService.Now;
        deposit.DepositStatusId = await depositStatusesAppService.GetIdByNameAsync(AdoptedDepositStatusName);

        await this.Data.SaveChangesAsync();

        return deposit.Id;
    }

    public async Task<int> RedirectAsync(int id, DepositRequestModel request)
    {
        var deposit = await this.GetAsync(id);

        if (deposit is null)
        {
            throw new EntityNotFoundException(typeof(Deposit), id);
        }

        deposit.DepositStatusId = await depositStatusesAppService.GetIdByNameAsync(RedirectedDepositStatusName);

        var newDepositId = await this.CreateAsync(request);

        var divertedDeposit = new DivertedDeposit
        {
            FromDepositId = id,
            ToDepositId = newDepositId
        };

        await this.Data.AddAsync(divertedDeposit);
        await this.Data.SaveChangesAsync();

        return newDepositId;
    }

    public async Task<int> TransferToOwnerAsync(int id)
    {
        var deposit = await this.GetAsync(id);

        if (deposit is null)
        {
            throw new EntityNotFoundException(typeof(Deposit), id);
        }

        deposit.TransferredToOwner = true;

        await this.Data.SaveChangesAsync();

        return deposit.Id;
    }

    [Consumes("multipart/form-data")]
    public async Task UploadContractsAsync(int id, [FromForm] IFormFileCollection files)
    {
        var tenantId = this.GetTenantId();
        var guaranteeContractTypeId = await contractTypesAppService.GetIdByNameAsync(GuaranteeContractTypeName);

        foreach (var file in files)
        {
            var fileName = $"{id}_{dateTimeService.Now:yyyyMMddTHHmmss}_{file.FileName}";

            var fileSource = await filesUploaderAppService.UploadAsync(file, BlobContainerName, fileName);

            await this.Data.Contracts.AddAsync(new Contract
            {
                Name = fileName,
                Size = file.Length,
                Source = fileSource,
                DepositId = id,
                ContractTypeId = guaranteeContractTypeId,
                TenantId = tenantId
            });
        }

        await this.Data.SaveChangesAsync();
    }

    public async Task<DepositForDealResponseModel?> GetForDealAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<DepositForDealResponseModel>(this
                .AllAsNoTracking()
                .Where(d => d.Id == id))
            .FirstOrDefaultAsync();

    public async Task<IEnumerable<DepositListingResponseModel>> GetByClientIdAsync(int clientId)
        => await this.ObjectMapper
            .ProjectTo<DepositListingResponseModel>(this
                .AllAsNoTracking()
                .Where(d => d.OfferClientId == clientId || d.SearchClientId == clientId)
                .OrderByDescending(d => d.CreationTime))
            .ToListAsync();

    protected override Specification<Deposit> GetSpecification(DepositsPaginatedRequestModel request)
        => new DepositByIdSpecification(request.Id)
            .And(new DepositByClientIdSpecification(request.ClientId))
            .And(new DepositBySearchIdSpecification(request.SearchId))
            .And(new DepositByOfferIdSpecification(request.OfferId))
            .And(new DepositByStatusSpecification(request.DepositStatusId))
            .And(new DepositByConsultantSpecification(request.ConsultantId))
            .And(new DepositBySignDateSpecification(request.DateFrom, request.DateTo));

    private async Task SendDepositCreatedNotificationAsync(
        DepositRequestModel request,
        EmployeeForNotificationResponseModel? employee,
        int createdDepositId)
    {
        if (employee?.UserId is null)
        {
            return;
        }

        var notificationUser = new UserIdentifier(
            employee.TenantId,
            employee.UserId.Value);

        var ownerOfSearchEmployee = (await this.EmployeesAppService
                .GetByIdOrNameOrPhoneOrEmail(request.SearchEmployeeId.ToString()))
            .FirstOrDefault();

        var message = this.L("NewOfferDepositNotificationMessage", request.OfferId, createdDepositId,
            ownerOfSearchEmployee.FirstName + " " + ownerOfSearchEmployee.LastName);

        await appNotifier.SendImportantMessageAsync(
            notificationUser,
            message);
    }
}