using AutoMapper.QueryableExtensions;
namespace RealtoCrm;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Common.Attributes;
using Common.Extensions;
using DataCrudModels;
using EntityFrameworkCore;
using Mapping;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Expressions;
using MultiTenancy.Interfaces;

[ActionAttributeFilter]
public abstract class DataCrudAppService<
    TPrimaryKey,
    TEntity,
    TCreateRequestModel,
    TUpdateRequestModel,
    TPaginatedRequestModel,
    TDetailsResponseModel,
    TListingResponseModel>(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : RealtoCrmAppServiceBase,
        IDataCrudAppService<
            TPrimaryKey,
            TEntity,
            TCreateRequestModel,
            TUpdateRequestModel,
            TPaginatedRequestModel,
            TDetailsResponseModel,
            TListingResponseModel>
    where TPrimaryKey : struct
    where TEntity : FullAuditedEntity<TPrimaryKey>
    where TCreateRequestModel : class, IMapTo<TEntity>
    where TUpdateRequestModel : class, IMapTo<TEntity>
    where TPaginatedRequestModel : PaginatedRequestModel, new()
    where TDetailsResponseModel : class, IMapFrom<TEntity>
    where TListingResponseModel : class, IMapFrom<TEntity>
{
    protected readonly ParameterExpression Parameter = Expression.Parameter(typeof(TEntity), "x");

    protected RealtoCrmDbContext Data => dbContextProvider.GetDbContext();

    protected IQueryable<TEntity> All() => this.Data.Set<TEntity>();

    protected IQueryable<TEntity> AllAsNoTracking() => this.All().AsNoTracking();

    protected virtual bool IgnoreQueryFilters => false;

    protected virtual Dictionary<string, FilterExpression> CustomFilters => new();

    protected virtual ActionAttributeConfiguration AttributeConfiguration { get; } = new();

    protected virtual string EntityNotFoundExceptionMessageFormat => "{0} with id {1} not found.";

    protected virtual Specification<TEntity> GetSpecification(TPaginatedRequestModel request)
        => new EmptySpecification();

    protected virtual async Task<Specification<TEntity>> GetSpecificationAsync(TPaginatedRequestModel request)
        => new EmptySpecification();

    protected virtual IQueryable<TEntity> BuildDetailsQuery(TPrimaryKey id)
        => this
            .AllAsNoTracking()
            .IgnoreQueryFilters(this.IgnoreQueryFilters)
            .Where(n => n.Id.Equals(id))
            .Where(this.BuildTenantSpecification());

    public virtual async Task<TDetailsResponseModel?> GetDetailsAsync(TPrimaryKey id)
    {
        var result = await this.ObjectMapper
            .ProjectTo<TDetailsResponseModel>(this.BuildDetailsQuery(id))
            .FirstOrDefaultAsync();

        if (result is null)
        {
            var message = string.Format(this.EntityNotFoundExceptionMessageFormat, typeof(TEntity).Name, id);
            throw new EntityNotFoundException(message);
        }

        return result;
    }

    [HttpPost]
    public virtual async Task<PaginatedResponseModel<TListingResponseModel>> GetAllAsync(
        TPaginatedRequestModel? request)
    {
        request ??= new TPaginatedRequestModel();

        var specification = await this.BuildSpecification(request);

        var skip = request.Page * request.PageSize;

        var filtersExpression = await this.BuildDataCrudFiltersExpression(request.Filters);

        var sorterExpressionDefinitions = await this.BuildDataCrudSortByExpression<TPaginatedRequestModel>(request);

        var items = await this.GetListingAsync(
            specification,
            filtersExpression,
            sorterExpressionDefinitions,
            skip,
            take: request.PageSize);

        var total = await this.GetTotal(specification, filtersExpression);

        var totalPages = (int) Math.Ceiling((double) total / request.PageSize);

        return new PaginatedResponseModel<TListingResponseModel>(items, request.Page, total, totalPages);
    }

    public virtual async Task<TPrimaryKey> CreateAsync(TCreateRequestModel request)
    {
        var entity = this.ObjectMapper.Map<TEntity>(request);

        this.Data.Set<TEntity>().Add(entity);

        await this.Data.SaveChangesAsync();

        return entity.Id;
    }

    public virtual async Task<TPrimaryKey> UpdateAsync(TPrimaryKey id, TUpdateRequestModel request)
    {
        var entity = await this.GetAsync(id);

        if (entity is null)
        {
            throw new EntityNotFoundException($"{typeof(TEntity).Name} with id '{id}' is not found.");
        }

        this.ObjectMapper.Map(request, entity);

        await this.Data.SaveChangesAsync();

        return entity.Id;
    }

    public virtual async Task<bool> DeleteAsync(TPrimaryKey id)
    {
        var entity = await this.GetAsync(id);

        if (entity is null)
        {
            return false;
        }

        this.Data.Set<TEntity>().Remove(entity);

        await this.Data.SaveChangesAsync();

        return true;
    }

    protected int GetTenantId() => this.AbpSession.GetTenantId();

    protected virtual async Task<TEntity?> GetAsync(TPrimaryKey id)
        => await this
            .All()
            .Where(this.BuildTenantSpecification())
            .FirstOrDefaultAsync(e => e.Id.Equals(id));

    protected async Task AddRangeAsync(IEnumerable<TEntity> entities)
    {
        this.Data.Set<TEntity>().AddRange(entities);

        await this.Data.SaveChangesAsync();
    }

    protected virtual async Task<IEnumerable<TListingResponseModel>> GetListingAsync(
        Specification<TEntity> specification,
        Expression<Func<TEntity, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<TEntity>> sortByExpressions,
        int skip = 0,
        int take = int.MaxValue)
    {
        // 1️⃣ Build the base query (filters + sorts)
        IQueryable<TEntity> query = this.GetEntityQuery(specification, filtersExpression)
            .ApplySortByDefinitions(sortByExpressions)

            // 2️⃣ Performance switches ---------------------------------
            .AsNoTracking() // turn off change-tracking for a read-only listing
            .AsSplitQuery(); // avoid cartesian row explosion when collections are projected
        // ----------------------------------------------------------

        // 3️⃣ Paging *before* projection keeps SQL efficient
        query = query.Skip(skip).Take(take);

        // 4️⃣ Project directly to the DTO
        IQueryable<TListingResponseModel> projected =
            query.ProjectTo<TListingResponseModel>(this.ObjectMapper.ConfigurationProvider);

        return await projected.ToListAsync();
    }


    protected virtual Task<Expression<Func<TEntity, bool>>> BuildDataCrudFiltersExpression(
        IEnumerable<DataFilter> filters)
    {
        var filtersExpression = expressionsBuilder.BuildDataCrudFiltersExpression<TEntity>(
            this.Parameter,
            filters,
            this.CustomFilters);

        return Task.FromResult(filtersExpression);
    }

    protected async virtual Task<IEnumerable<SortByExpressionDefinition<TEntity>>> BuildDataCrudSortByExpression<TRequest>(
        TRequest request)
        where TRequest : PaginatedRequestModel, new()
        => expressionsBuilder.BuildDataCrudSortByExpression<TEntity, TPrimaryKey>(request.SortBy);

    protected virtual Specification<TEntity> BuildTenantSpecification()
        => typeof(TEntity).ImplementsTenantInterface() && this.AbpSession.TenantId.HasValue
            ? new TenantSpecification(this.GetTenantId())
            : new EmptySpecification();

    protected virtual async Task<int> GetTotal(
        Specification<TEntity> specification,
        Expression<Func<TEntity, bool>> filtersExpression)
        => await this
            .GetEntityQuery(specification, filtersExpression)
            .CountAsync();

    protected virtual IQueryable<TEntity> GetEntityQuery(
        Specification<TEntity> specification,
        Expression<Func<TEntity, bool>> filtersExpression)
        => this
            .AllAsNoTracking()
            .IgnoreQueryFilters(this.IgnoreQueryFilters)
            .Where(filtersExpression)
            .Where(specification);

    protected async Task<Specification<TEntity>> BuildSpecification(TPaginatedRequestModel request)
    {
        var specification = this.GetSpecification(request)
            .And(await this.GetSpecificationAsync(request));

        return request.ShouldGetDataFromAllTenants ? specification : this.BuildTenantSpecification().And(specification);
    }

    private sealed class EmptySpecification : Specification<TEntity>
    {
        public override Expression<Func<TEntity, bool>> ToExpression() => value => true;
    }

    private class TenantSpecification(int tenantId) : Specification<TEntity>
    {
        public override Expression<Func<TEntity, bool>> ToExpression()
            => typeof(IHaveOptionalTenant).IsAssignableFrom(typeof(TEntity))
                ? currentEntity => ((IHaveOptionalTenant) currentEntity).TenantId == tenantId
                : currentEntity => ((IHaveTenant) currentEntity).TenantId == tenantId;
    }
}
