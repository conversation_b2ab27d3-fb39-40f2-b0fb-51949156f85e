using Abp.UI;

namespace RealtoCrm.Projects;

using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Authorization;
using Clients;
using Clients.Models;
using CommentsTasks;
using Common.Attributes;
using Contracts;
using Employees;
using EntityFrameworkCore;
using EstateGroups;
using EstateGroups.Models;
using Models;
using Expressions;
using Extensions;
using Files;
using Files.Categories;
using Files.Uploader;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nomenclatures.ImageCategories;
using Nomenclatures.ProjectStatuses;
using Nomenclatures.Vats;
using ProjectsImages;
using ProjectsImages.Models;
using Providers;
using Specifications;
using static CosherConsts.Vats;
using static CosherConsts.ProjectStatuses;
using static CosherConsts.ImageCategories;
using static CosherConsts.FileCategories;

public class ProjectsAppService(
    IDateTimeService dateTimeService,
    IVatsAppService vatsAppService,
    IEmployeesAppService employeesAppService,
    IEstateGroupsAppService estateGroupsAppService,
    IClientsAppService clientsAppService,
    IProjectStatusesAppService projectStatusesAppService,
    IContractsAppService contractsAppService,
    IFilesUploaderAppService filesUploaderAppService,
    IImageCategoriesAppService imageCategoriesAppService,
    IFileCategoriesAppService fileCategoriesAppService,
    IProjectsImagesAppService projectsImagesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder,
    IClientsPermissionsChecker clientsPermissionsChecker)
    : CommentsTasksAppService<
        int,
        Project,
        ProjectComment,
        ProjectTask,
        ProjectCreateRequestModel,
        ProjectUpdateRequestModel,
        ProjectsPaginatedRequestModel,
        ProjectDetailsResponseModel,
        ProjectListingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder), IProjectsAppService
{
    private const string BlobContainerName = "projects";
    private const string FileNameFormat = "{0}_{1}_{2}";
    private const string FileNameDateFormat = "yyyyMMddTHHmmss";

    protected override Specification<Project> GetSpecification(ProjectsPaginatedRequestModel request)
        => new ProjectByIdSpecification(request.Id);

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.ProjectCreate, AppPermissions.ContactCreateProject)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.CreateLinkedProjectsAsync), AppPermissions.LinkedProjectsCreate)
            .AddCustomAttribute(nameof(this.CreateLinkedProjectsAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.ProjectUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.ProjectDelete)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.ProjectRead)
            .AddCustomAttribute(nameof(this.UploadFilesAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UploadFilesAsync), AppPermissions.ProjectUpdate);

    public override async Task<int> CreateAsync(ProjectCreateRequestModel request)
    {
        return await CreateProjectAsync(request);
    }
    
    public async Task<int> CreateForClientAsync(ProjectCreateRequestModel request)
    {
        if(!await clientsPermissionsChecker.CanPerformAction(
               request.OwnerId,
               this.AbpSession.GetUserId(),
               [AppPermissions.ContactCreateProject]
           ))
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }
        
        var projectId = await CreateProjectAsync(request);
        
        return projectId;
    }

    public override async Task<int> UpdateAsync(int id, ProjectUpdateRequestModel request)
    {
        await this.Data
            .ProjectsEmployees
            .Where(pe => pe.ProjectId == id)
            .ExecuteDeleteAsync();

        await this.Data
            .SourceDetailsProjects
            .Where(sdp => sdp.ProjectId == id)
            .ExecuteDeleteAsync();

        await this.Data
            .ProjectDetails
            .Where(pd => pd.Project.Id == id)
            .ExecuteDeleteAsync();

        await this.Data
            .Projects
            .Where(p => p.Id == id)
            .Select(p => p.EstateGroup.EstateGroupDetail)
            .ExecuteDeleteAsync();

        await this.Data
            .Projects
            .Where(p => p.Id == id)
            .SelectMany(p => p.EstateGroup.EstateGroupsHeatings)
            .ExecuteDeleteAsync();

        await this.Data
            .ProjectsComments
            .Where(p => p.ProjectId == id)
            .ExecuteDeleteAsync();

        await this.Data
            .ProjectsImages
            .Where(od => od.ProjectId == id)
            .ExecuteDeleteAsync();

        var contractsToRemove = await this.Data
            .Contracts
            .Where(c => request.ContractIdsToRemove.Contains(c.Id))
            .ToListAsync();

        this.Data.Contracts.RemoveRange(contractsToRemove);

        var tenantId = this.GetTenantId();
        var projectOwnerId = await this.GetOwnerIdByIdAsync(id);
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        request.Contracts.ForEach(c => c.TenantId = tenantId);
        request.EstateGroup.TenantId = tenantId;
        request.ProjectsComments.ForEach(c => c.EmployeeId = employeeId);

        await clientsAppService.UpdateRelatedClientsAsync(projectOwnerId, request.RelatedClients);

        return await base.UpdateAsync(id, request);
    }

    [Consumes("multipart/form-data")]
    public async Task UploadFilesAsync(int id, [FromForm] ProjectUploadFilesRequestModel files)
    {
        var tenantId = this.GetTenantId();

        if (files.Images is not null && files.Images.Any())
        {
            await this.UploadEstateImagesAsync(id, files.Images);
        }

        if (files.Sketches is not null && files.Sketches.Any())
        {
            await this.UploadSketchImagesAsync(id, files.Sketches);
        }

        if (files.Brochures is not null && files.Brochures.Any())
        {
            await this.UploadBrochureFilesAsync(id, files.Brochures, tenantId);
        }

        if (files.Certificates is not null && files.Certificates.Any())
        {
            await this.UploadCertificateFilesAsync(id, files.CertificateTitle, files.Certificates, tenantId);
        }

        if (files.Contracts is not null && files.Contracts.Any())
        {
            await this.UploadContractFilesAsync(id, files.Contracts);
        }

        await this.Data.SaveChangesAsync();
    }

    public async Task<int> CreateLinkedProjectsAsync(LinkedProjectsRequestModel request)
    {
        var tenantId = this.GetTenantId();
        var withoutVatId = await vatsAppService.GetIdByNameAsync(WithoutVatName);
        var draftProjectStatusId = await projectStatusesAppService.GetIdByNameAsync(DraftProjectStatusName);
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        int? firstLinkedProjectId = null;

        var projects = request.Projects
            .OrderBy(p => p.TenantId != tenantId)
            .ThenBy(p => p.TenantId)
            .ToList();

        foreach (var project in projects)
        {
            project.EstateGroupId ??= await estateGroupsAppService.CreateAsync(new EstateGroupRequestModel
            {
                Name = project.EstateGroupName,
                TenantId = project.TenantId,
            });

            project.VatId = withoutVatId;
            project.ProjectStatusId = draftProjectStatusId;
            project.EmployeeId = employeeId;

            if (firstLinkedProjectId is not null)
            {
                project.LinkedProjectId = firstLinkedProjectId;
            }

            var linkedProject = this.ObjectMapper.Map<Project>(project);

            this.Data.Projects.Add(linkedProject);

            await this.Data.SaveChangesAsync();

            if (firstLinkedProjectId is null)
            {
                firstLinkedProjectId = linkedProject.Id;
                linkedProject.LinkedProjectId = linkedProject.Id;

                await this.Data.SaveChangesAsync();
            }
        }

        return firstLinkedProjectId!.Value;
    }

    public async Task<ProjectActivityResponseModel?> GetActivityAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<ProjectActivityResponseModel>(this
                .AllAsNoTracking()
                .Where(p => p.Id == id)
                .AsSplitQuery())
            .FirstOrDefaultAsync();

    public async Task DeleteImageAsync(ProjectImageDeleteRequestModel request)
    {
        var projectImage = await this
            .Data
            .ProjectsImages
            .Include(pf => pf.Image)
            .ThenInclude(i => i.Thumbnails)
            .FirstOrDefaultAsync(pf => pf.ProjectId == request.ProjectId && pf.ImageId == request.ImageId);

        if (projectImage is null)
        {
            throw new EntityNotFoundException("Project image not found.");
        }

        projectImage.IsActive = false;

        this.Data.Images.Remove(projectImage.Image);
        this.Data.ImageThumbnails.RemoveRange(projectImage.Image.Thumbnails);
        this.Data.ProjectsImages.Remove(projectImage);

        await this.Data.SaveChangesAsync();
    }

    public async Task DeleteFileAsync(ProjectFileDeleteRequestModel request)
    {
        var projectFile = await this
            .Data
            .ProjectsFiles
            .Include(pf => pf.File)
            .FirstOrDefaultAsync(pf => pf.ProjectId == request.ProjectId && pf.FileId == request.FileId);

        if (projectFile is null)
        {
            throw new EntityNotFoundException("Project file not found.");
        }

        this.Data.Files.Remove(projectFile.File);
        this.Data.ProjectsFiles.Remove(projectFile);

        await this.Data.SaveChangesAsync();
    }

    private async Task UploadCertificateFilesAsync(
        int id,
        string? certificateTitle,
        IFormFileCollection certificates,
        int tenantId)
    {
        var energyCertificateFileCategoryId = await fileCategoriesAppService.GetIdByNameAsync(
            EnergyCertificateFileCategoryName);

        foreach (var certificate in certificates)
        {
            var fileName = string.Format(
                FileNameFormat,
                id,
                dateTimeService.Now.ToString(FileNameDateFormat),
                certificate.FileName);

            var certificateSource = await filesUploaderAppService.UploadAsync(certificate, BlobContainerName, fileName);

            this.Data.ProjectsFiles.Add(new ProjectFile
            {
                ProjectId = id,
                File = new File
                {
                    Title = certificateTitle,
                    FileName = certificate.FileName,
                    Size = certificate.Length,
                    Source = certificateSource,
                    CategoryId = energyCertificateFileCategoryId,
                    TenantId = tenantId,
                }
            });
        }
    }

    private async Task UploadContractFilesAsync(int id, IFormFileCollection contracts)
    {
        var contractIds = await contractsAppService.GetIdsWithoutSourceByProjectIdAsync(id);

        await contractIds.ForEachAsync(async (contractId, index) =>
        {
            var contractFile = contracts.ElementAtOrDefault(index);

            if (contractFile is not null)
            {
                await contractsAppService.UploadContractsAsync(contractId, new FormFileCollection
                {
                    contractFile
                });
            }
        });
    }

    private async Task UploadBrochureFilesAsync(int id, IFormFileCollection brochures, int tenantId)
    {
        var brochureFileCategoryId = await fileCategoriesAppService.GetIdByNameAsync(BrochureFileCategoryName);

        foreach (var brochure in brochures)
        {
            var fileName = string.Format(
                FileNameFormat,
                id,
                dateTimeService.Now.ToString(FileNameDateFormat),
                brochure.FileName);

            var brochureSource = await filesUploaderAppService.UploadAsync(brochure, BlobContainerName, fileName);

            this.Data.ProjectsFiles.Add(new ProjectFile
            {
                ProjectId = id,
                File = new File
                {
                    FileName = brochure.FileName,
                    Size = brochure.Length,
                    Source = brochureSource,
                    CategoryId = brochureFileCategoryId,
                    TenantId = tenantId,
                }
            });
        }
    }

    private async Task UploadSketchImagesAsync(int id, IFormFileCollection sketches)
    {
        var tenantId = this.GetTenantId();
        var sketchImageCategoryId = await imageCategoriesAppService.GetIdByNameAsync(SketchImageCategoryName);

        await projectsImagesAppService.UploadImagesAsync(id, tenantId, sketchImageCategoryId, sketches);
    }

    private async Task UploadEstateImagesAsync(int id, IFormFileCollection images)
    {
        var tenantId = this.GetTenantId();
        var estateImageCategoryId = await imageCategoriesAppService.GetIdByNameAsync(EstateImageCategoryName);

        await projectsImagesAppService.UploadImagesAsync(id, tenantId, estateImageCategoryId, images);
    }

    private async Task<int> GetOwnerIdByIdAsync(int id)
        => await this
            .AllAsNoTracking()
            .Where(p => p.Id == id)
            .Select(p => p.OwnerId)
            .FirstAsync();
    
    private async Task<int> CreateProjectAsync(ProjectCreateRequestModel request)
    {
        var client = await clientsAppService.GetIdentificationDetailsAsync(request.OwnerId);

        if (client is null)
        {
            throw new EntityNotFoundException(typeof(Client), request.OwnerId);
        }

        var tenantId = this.GetTenantId();

        request.EstateGroupId ??= await estateGroupsAppService.CreateAsync(new EstateGroupRequestModel
        {
            Name = request.EstateGroupName,
            TenantId = tenantId
        });

        if (string.IsNullOrEmpty(client.IdentificationNumber) || string.IsNullOrEmpty(client.UnifiedIdentificationCode))
        {
            var clientProjectModel = new ClientProjectUpdateModel
            {
                ClientId = request.OwnerId,
                IdentificationNumber = request.IdentificationNumber,
                UnifiedIdentificationCode = request.UnifiedIdentificationCode
            };

            await clientsAppService.UpdateClientIdentificationNumberOrUnifiedIdentificationCode(clientProjectModel);
        }

        request.TenantId = tenantId;
        request.VatId = await vatsAppService.GetIdByNameAsync(WithoutVatName);
        request.ProjectStatusId = await projectStatusesAppService.GetIdByNameAsync(DraftProjectStatusName);
        request.EmployeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        return await base.CreateAsync(request);
    }
}