namespace RealtoCrm.Projects;

using System.Threading.Tasks;
using CommentsTasks;
using Microsoft.AspNetCore.Mvc;
using Models;
using ProjectsImages.Models;

public interface IProjectsAppService : ICommentsTasksAppService<
    int,
    Project,
    ProjectComment,
    ProjectTask,
    ProjectCreateRequestModel,
    ProjectUpdateRequestModel,
    ProjectsPaginatedRequestModel,
    ProjectDetailsResponseModel,
    ProjectListingResponseModel>
{
    Task UploadFilesAsync(int id, [FromForm] ProjectUploadFilesRequestModel files);

    Task<int> CreateLinkedProjectsAsync(LinkedProjectsRequestModel request);

    Task<ProjectActivityResponseModel?> GetActivityAsync(int id);

    Task DeleteImageAsync(ProjectImageDeleteRequestModel request);

    Task DeleteFileAsync(ProjectFileDeleteRequestModel request);

    Task<int> CreateForClientAsync(ProjectCreateRequestModel request);
}