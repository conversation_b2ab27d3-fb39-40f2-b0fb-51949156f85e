namespace RealtoCrm.Buildings;

using System.Collections.Generic;
using System.Threading.Tasks;
using Models;

public interface IBuildingsAppService : IDataCrudAppService<
    int,
    Building,
    BuildingRequestModel,
    BuildingRequestModel,
    BuildingsPaginatedRequestModel,
    BuildingResponseModel,
    BuildingResponseModel>
{
    Task BulkAddOrUpdateAsync(BuildingBulkAddOrUpdateRequestModel request);

    Task<IEnumerable<BuildingIdAndNameResponseModel>> GetByNamesAsync(IEnumerable<string> names);
}