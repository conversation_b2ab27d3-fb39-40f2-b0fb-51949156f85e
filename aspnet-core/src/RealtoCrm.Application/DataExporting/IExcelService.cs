namespace RealtoCrm.DataExporting;

using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Dependency;
using Mapping;
using Models;

public interface IExcelService : ITransientDependency
{
    Task<IEnumerable<TRequestModel>> ImportAsync<TRequestModel>(
        byte[] fileBytes,
        string startCell = "A1")
        where TRequestModel : class, new();

    Task<IEnumerable<TEntity>> ImportAsync<TRequestModel, TEntity>(
        byte[] fileBytes,
        string startCell = "A1")
        where TRequestModel : class, IMapTo<TEntity>, new()
        where TEntity : class, new();

    Task<ExportFileResponseModel> ExportAsync<TResponseModel>(
        string fileName,
        IEnumerable<TResponseModel> items,
        ExportType exportType = ExportType.XLSX);

    Task<ExportFileResponseModel> ExportAsync(
        string fileName,
        IEnumerable<Dictionary<string, object>> items,
        ExportType exportType = ExportType.XLSX);
}