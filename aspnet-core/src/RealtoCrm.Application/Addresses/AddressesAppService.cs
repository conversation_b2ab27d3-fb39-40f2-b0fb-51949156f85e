namespace RealtoCrm.Addresses;

using Abp.EntityFrameworkCore;
using Authorization;
using Common.Attributes;
using DataCrudModels;
using EntityFrameworkCore;
using Expressions;
using Models;

public class AddressesAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Address,
        BaseAddressRequestModel,
        BaseAddressRequestModel,
        PaginatedRequestModel,
        AddressResponseModel,
        AddressResponseModel>(dbContextProvider, expressionsBuilder), IAddressesAppService
{
    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.AdministrativeDivisionsCreate)
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.AdministrativeDivisionsUpdate)
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.AdministrativeDivisionsDelete)
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.AdministrativeDivisionsRead);
}