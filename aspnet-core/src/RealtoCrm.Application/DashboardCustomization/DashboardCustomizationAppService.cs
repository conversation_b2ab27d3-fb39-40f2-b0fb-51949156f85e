using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Features;
using Abp.Authorization;
using Abp.Dependency;
using Abp.MultiTenancy;
using Abp.Runtime.Session;
using Abp.UI;
using RealtoCrm.Configuration;
using RealtoCrm.DashboardCustomization.Definitions;
using RealtoCrm.DashboardCustomization.Dto;
using Newtonsoft.Json;

namespace RealtoCrm.DashboardCustomization;

[AbpAuthorize]
public class DashboardCustomizationAppService(DashboardConfiguration dashboardConfiguration, IIocResolver iocResolver) : RealtoCrmAppServiceBase, IDashboardCustomizationAppService
{
    public async Task<Dashboard> GetUserDashboard(GetDashboardInput input)
    {
        return await this.GetDashboardWithAuthorizedWidgets(input.Application, input.DashboardName);
    }

    public async Task SavePage(SavePageInput input)
    {
        var dashboard = await this.GetDashboardWithAuthorizedWidgets(input.Application, input.DashboardName);

        foreach (var inputPage in input.Pages)
        {
            var page = dashboard.Pages.FirstOrDefault(p => p.Id == inputPage.Id);
            var pageIndex = dashboard.Pages.IndexOf(page);

            dashboard.Pages.RemoveAt(pageIndex);

            if (page != null)
            {
                inputPage.Name = page.Name;
                dashboard.Pages.Insert(pageIndex, inputPage);
            }
        }

        await this.SaveDashboardSettingForUser(input.Application, dashboard);
    }

    public async Task RenamePage(RenamePageInput input)
    {
        var dashboard = await this.GetDashboardWithAuthorizedWidgets(input.Application, input.DashboardName);

        var page = dashboard.Pages.FirstOrDefault(p => p.Id == input.Id);
        if (page == null)
        {
            return;
        }

        page.Name = input.Name;

        await this.SaveDashboardSettingForUser(input.Application, dashboard);
    }

    public async Task<AddNewPageOutput> AddNewPage(AddNewPageInput input)
    {
        var dashboard = await this.GetDashboardWithAuthorizedWidgets(input.Application, input.DashboardName);

        var page = new Page($"Page_{Guid.NewGuid().ToString().Replace("-", "")}")
        {
            Name = input.Name,
            Widgets = new List<Widget>(),
        };

        dashboard.Pages.Add(page);
        await this.SaveDashboardSettingForUser(input.Application, dashboard);

        return new AddNewPageOutput {PageId = page.Id};
    }

    public async Task DeletePage(DeletePageInput input)
    {
        var dashboard = await this.GetDashboardWithAuthorizedWidgets(input.Application, input.DashboardName);

        dashboard.Pages.RemoveAll(p => p.Id == input.Id);

        if (dashboard.Pages.Count == 0) // return to default
        {
            dashboard = await this.GetDefaultDashboardValue(input.Application, input.DashboardName);
        }

        await this.SaveDashboardSettingForUser(input.Application, dashboard);
    }

    public async Task<Widget> AddWidget(AddWidgetInput input)
    {
        var widgetDefinition = dashboardConfiguration.GetWidgetDefinition(input.WidgetId);
            
        if (widgetDefinition == null)
        {
            throw new UserFriendlyException(this.L("WidgetNotFound"));
        }

        var dashboard = await this.GetDashboardWithAuthorizedWidgets(input.Application, input.DashboardName);

        var page = dashboard.Pages.Single(p => p.Id == input.PageId);

        if (!widgetDefinition.AllowMultipleInstanceInSamePage &&
            page.Widgets.Any(w => w.WidgetId == widgetDefinition.Id))
        {
            throw new UserFriendlyException(this.L("WidgetCanNotBePlacedMoreThanOnceInAPageWarning"));
        }

        var widget = new Widget
        {
            WidgetId = input.WidgetId,
            Height = input.Height,
            Width = input.Width,
            PositionX = 0,
            PositionY = this.CalculatePositionY(page.Widgets)
        };

        page.Widgets.Add(widget);

        await this.SaveDashboardSettingForUser(input.Application, dashboard);
        return widget;
    }

    public DashboardOutput GetDashboardDefinition(GetDashboardInput input)
    {
        var dashboardDefinition = dashboardConfiguration.GetDashboardDefinition(input.DashboardName);
        if (dashboardDefinition == null)
        {
            throw new UserFriendlyException(this.L("UnknownDashboard", input.DashboardName));
        }

        //widgets which used in that dashboard
        var usedWidgetDefinitions = this.GetWidgetDefinitionsFilteredPermissionAndMultiTenancySide(dashboardDefinition);

        List<WidgetFilterOutput> GetNeededWidgetFiltersOutput(WidgetDefinition widget)
        {
            if (widget.UsedWidgetFilters == null || !widget.UsedWidgetFilters.Any())
            {
                return new List<WidgetFilterOutput>();
            }

            var allNeededFilters = widget.UsedWidgetFilters.Distinct().ToList();

            return dashboardConfiguration.GetWidgetFilterDefinitions()
                .Where(definition => allNeededFilters.Contains(definition.Id))
                .Select(x => new WidgetFilterOutput(x.Id, x.Name))
                .ToList();
        }

        return new DashboardOutput(
            dashboardDefinition.Name,
            usedWidgetDefinitions
                .Select(widget => new WidgetOutput(
                    widget.Id,
                    widget.Name,
                    widget.Description,
                    filters: GetNeededWidgetFiltersOutput(widget))
                ).ToList()
        );
    }

    public List<WidgetOutput> GetAllWidgetDefinitions(GetDashboardInput input)
    {
        var dashboardDefinition = dashboardConfiguration.GetDashboardDefinition(input.DashboardName);
        if (dashboardDefinition == null)
        {
            throw new UserFriendlyException(this.L("UnknownDashboard", input.DashboardName));
        }

        return this.GetWidgetDefinitionsFilteredPermissionAndMultiTenancySide(dashboardDefinition)
            .Select(widget => new WidgetOutput(widget.Id, widget.Name, widget.Description)).ToList();
    }

    public async Task<List<WidgetOutput>> GetAllAvailableWidgetDefinitionsForPage(
        GetAvailableWidgetDefinitionsForPageInput input)
    {
        var dashboardDefinition = dashboardConfiguration.GetDashboardDefinition(input.DashboardName);
        if (dashboardDefinition == null)
        {
            throw new UserFriendlyException(this.L("UnknownDashboard", input.DashboardName));
        }

        var dashboard = await this.GetUserDashboard(new GetDashboardInput()
        {
            Application = input.Application,
            DashboardName = input.DashboardName
        });

        var page = dashboard.Pages.FirstOrDefault(p => p.Id == input.PageId);
        if (page == null)
        {
            throw new UserFriendlyException(this.L("UnknownPage"));
        }

        var widgetsAlreadyInPage = page.Widgets.Select(w => w.WidgetId).ToList();

        return this.GetWidgetDefinitionsFilteredPermissionAndMultiTenancySide(dashboardDefinition)
            .Where(widgetDefinition =>
                widgetDefinition.AllowMultipleInstanceInSamePage ||
                !widgetsAlreadyInPage.Contains(widgetDefinition.Id)
            )
            .Select(widget => new WidgetOutput(widget.Id, widget.Name, widget.Description)).ToList();
    }

    private async Task<Dashboard> GetDashboardWithAuthorizedWidgets(string application, string dashboardName)
    {
        var dashboardConfigAsJsonString =
            await this.SettingManager.GetSettingValueAsync(this.GetSettingName(application, dashboardName));

        if (string.IsNullOrWhiteSpace(dashboardConfigAsJsonString))
        {
            return null;
        }

        var dashboard = JsonConvert.DeserializeObject<Dashboard>(dashboardConfigAsJsonString);
        if (dashboard == null)
        {
            throw new UserFriendlyException(this.L("UnknownDashboard", dashboardName));
        }

        var dashboardDefinition = dashboardConfiguration.GetDashboardDefinition(dashboardName);
        if (dashboardDefinition == null)
        {
            throw new UserFriendlyException(this.L("UnknownDashboard", dashboardName));
        }

        //widgets which used in that dashboard
        var authorizedWidgetIdsForDashboardAndUser = this.GetWidgetDefinitionsFilteredPermissionAndMultiTenancySide(dashboardDefinition)
            .Select(definition => definition.Id).ToList();

        //if user's permission changed, we should remove all widgets which are not allowed for user
        foreach (var dashboardPage in dashboard.Pages)
        {
            dashboardPage.Widgets = dashboardPage.Widgets
                .Where(widget => authorizedWidgetIdsForDashboardAndUser.Contains(widget.WidgetId)).ToList();
        }

        return dashboard;
    }

    private async Task SaveDashboardSettingForUser(string application, Dashboard dashboard)
    {
        if (dashboard == null)
        {
            return;
        }

        //check if dashboard is available for user to prevent saving unauthorized widgets to dashboard
        var dashboardDefinition = dashboardConfiguration.GetDashboardDefinition(dashboard.DashboardName);
        if (dashboardDefinition == null)
        {
            throw new UserFriendlyException(this.L("UnknownDashboard", dashboard.DashboardName));
        }

        var authorizedWidgetIdsForDashboardAndUser = this.GetWidgetDefinitionsFilteredPermissionAndMultiTenancySide(dashboardDefinition)
            .Select(definition => definition.Id)
            .ToList();

        var allWidgetsOfDashboard = dashboard.Pages.SelectMany(p => p.Widgets)
            .DistinctBy(widget => widget.WidgetId)
            .Select(widget => widget.WidgetId)
            .ToList();

        if (allWidgetsOfDashboard.Any(widgetId => !authorizedWidgetIdsForDashboardAndUser.Contains(widgetId)))
        {
            var unknownWidgetId = allWidgetsOfDashboard.First(widget =>
                !authorizedWidgetIdsForDashboardAndUser.Contains(widget));
            throw new UserFriendlyException(this.L("UnknownWidgetId", unknownWidgetId));
        }

        //we can save dashboard now since it is authorized for user
        var dashboardJson = JsonConvert.SerializeObject(dashboard);

        var currentUser = await this.GetCurrentUserAsync();
        await this.SettingManager.ChangeSettingForUserAsync(currentUser.ToUserIdentifier(), this.GetSettingName(application, dashboard.DashboardName), dashboardJson);
    }

    private byte CalculatePositionY(List<Widget> widgets)
    {
        if (widgets == null || !widgets.Any())
        {
            return 0;
        }

        return (byte) widgets.Max(w => w.PositionY + w.Height);
    }

    private async Task<Dashboard> GetDefaultDashboardValue(string application, string dashboardName)
    {
        string dashboardConfigAsJsonString;

        if (this.AbpSession.MultiTenancySide == MultiTenancySides.Host)
        {
            dashboardConfigAsJsonString =
                await this.SettingManager.GetSettingValueForApplicationAsync(this.GetSettingName(application, dashboardName));
        }
        else
        {
            dashboardConfigAsJsonString =
                await this.SettingManager.GetSettingValueForTenantAsync(this.GetSettingName(application, dashboardName), this.AbpSession.GetTenantId());
        }

        return string.IsNullOrWhiteSpace(dashboardConfigAsJsonString)
            ? null
            : JsonConvert.DeserializeObject<Dashboard>(dashboardConfigAsJsonString);
    }

    private List<WidgetDefinition> GetWidgetDefinitionsFilteredPermissionAndMultiTenancySide(
        DashboardDefinition dashboardDefinition)
    {
        var dashboardWidgets = dashboardDefinition.AvailableWidgets ?? new List<string>();

        var widgetDefinitions = dashboardConfiguration.GetWidgetDefinitions()
            .Where(wd => dashboardWidgets.Contains(wd.Id)).ToList();

        //filter for multi-tenancy side
        widgetDefinitions = widgetDefinitions.Where(w => w.Side.HasFlag(this.AbpSession.MultiTenancySide)).ToList();

        //filter for permissions
        var filteredWidgets = new List<WidgetDefinition>();

        using (var scope = iocResolver.CreateScope())
        {
            var permissionDependencyContext = scope.Resolve<PermissionDependencyContext>();
            permissionDependencyContext.User = this.AbpSession.ToUserIdentifier();

            var featureDependencyContext = scope.Resolve<FeatureDependencyContext>();
            featureDependencyContext.TenantId = this.AbpSession.TenantId;

            foreach (var widget in widgetDefinitions)
            {
                if (widget.PermissionDependency != null &&
                    (!widget.PermissionDependency.IsSatisfied(permissionDependencyContext)))
                {
                    continue;
                }

                if (widget.FeatureDependency != null &&
                    (!widget.FeatureDependency.IsSatisfied(featureDependencyContext)))
                {
                    continue;
                }

                filteredWidgets.Add(widget);
            }
        }

        return filteredWidgets;
    }

    public string GetSettingName(string application, string dashboardName)
    {
        return AppSettings.DashboardCustomization.Configuration + "." + application + "." + dashboardName;
    }
}