using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Abp;
using Abp.Auditing;
using Abp.Authorization;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Localization;
using Abp.Net.Mail;
using Abp.Runtime.Security;
using Abp.UI;
using Abp.Zero.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Authorization;
using RealtoCrm.Configuration;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.Configuration.Host.Dto;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Identity;
using RealtoCrm.Install.Dto;

namespace RealtoCrm.Install;

using Abp.Authorization.Users;
using EntityFrameworkCore.Migrations.Seed;
using EntityFrameworkCore.Migrations.Seed.Host;

[AbpAllowAnonymous]
[DisableAuditing]
public class InstallAppService(
    AbpZeroDbMigrator migrator,
    LogInManager logInManager,
    SignInManager signInManager,
    DatabaseCheckHelper databaseCheckHelper,
    IAppConfigurationAccessor appConfigurationAccessor,
    IAppConfigurationWriter appConfigurationWriter)
    : RealtoCrmAppServiceBase, IInstallAppService
{
    private readonly AbpZeroDbMigrator<RealtoCrmDbContext> migrator = migrator;
    private readonly IConfigurationRoot appConfiguration = appConfigurationAccessor.Configuration;

    public async Task Setup(InstallDto input)
    {
        if (this.CheckDatabaseInternal())
        {
            throw new UserFriendlyException("Setup process is already done.");
        }

        this.SetConnectionString(input.ConnectionString);

        if (this.CheckDatabaseInternal())
        {
            await this.SetAdminPassword(input.AdminPassword);
            this.SetUrl(input.WebSiteUrl, input.ServerUrl);
            await this.SetDefaultLanguage(input.DefaultLanguage);
            await this.SetSmtpSettings(input.SmtpSettings);
            await this.SetBillingSettings(input.BillInfo);
        }
        else
        {
            throw new UserFriendlyException("Database couldn't be created!");
        }
    }

    [UnitOfWork(IsDisabled = true)]
    public AppSettingsJsonDto GetAppSettingsJson()
    {
        var appUrl = this.appConfiguration.GetSection("App");

        if (appUrl["WebSiteRootAddress"].IsNullOrEmpty())
        {
            return new AppSettingsJsonDto
            {
                WebSiteUrl = appUrl["ClientRootAddress"],
                ServerSiteUrl = appUrl["ServerRootAddress"],
                Languages = DefaultLanguagesCreator.InitialLanguages
                    .Select(l => new NameValue(l.DisplayName, l.Name)).ToList()
            };
        }

        return new AppSettingsJsonDto
        {
            WebSiteUrl = appUrl["WebSiteRootAddress"]
        };
    }

    public CheckDatabaseOutput CheckDatabase()
    {
        return new CheckDatabaseOutput
        {
            IsDatabaseExist = this.CheckDatabaseInternal()
        };
    }

    private bool CheckDatabaseInternal()
    {
        var connectionString = this.appConfiguration[$"ConnectionStrings:{RealtoCrmConsts.ConnectionStringName}"];

        if (string.IsNullOrEmpty(connectionString))
        {
            return false;
        }

        return databaseCheckHelper.Exist(connectionString);
    }

    private void SetConnectionString(string constring)
    {
        this.EditAppSettingsjson($"ConnectionStrings:{RealtoCrmConsts.ConnectionStringName}", constring);
    }

    private async Task SetAdminPassword(string adminPassword)
    {
        var admin = await this.UserManager.FindByIdAsync("1");

        await this.UserManager.InitializeOptionsAsync(this.AbpSession.TenantId);

        var loginResult = await logInManager.LoginAsync(AbpUserBase.AdminUserName, "123qwe");
        var signInResult = await signInManager.SignInOrTwoFactorAsync(loginResult, false);
        if (signInResult.Succeeded)
        {
            this.CheckErrors(await this.UserManager.ChangePasswordAsync(admin, adminPassword));
            admin.ShouldChangePasswordOnNextLogin = false;
            this.CheckErrors(await this.UserManager.UpdateAsync(admin));
        }
    }

    private void SetUrl(string webSitRUrl, string serverUrl)
    {
        if (!serverUrl.IsNullOrEmpty())
        {
            this.EditAppSettingsjson("App:ClientRootAddress", webSitRUrl);
            this.EditAppSettingsjson("App:ServerRootAddress", serverUrl);
        }
        else
        {
            this.EditAppSettingsjson("App:WebSiteRootAddress", webSitRUrl);
        }
    }

    private async Task SetDefaultLanguage(string language)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(LocalizationSettingNames.DefaultLanguage, language);
    }

    private async Task SetSmtpSettings(EmailSettingsEditDto input)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.DefaultFromAddress, input.DefaultFromAddress);
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.DefaultFromDisplayName, input.DefaultFromDisplayName);
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.Host, input.SmtpHost);
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.Port, input.SmtpPort?.ToString(CultureInfo.InvariantCulture));
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.UserName, input.SmtpUserName);
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.Password, SimpleStringCipher.Instance.Encrypt(input.SmtpPassword));
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.Domain, input.SmtpDomain);
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.EnableSsl, input.SmtpEnableSsl.ToString().ToLowerInvariant());
        await this.SettingManager.ChangeSettingForApplicationAsync(EmailSettingNames.Smtp.UseDefaultCredentials, input.SmtpUseDefaultCredentials.ToString().ToLowerInvariant());
    }

    private async Task SetBillingSettings(HostBillingSettingsEditDto input)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(AppSettings.HostManagement.BillingLegalName, input.LegalName);
        await this.SettingManager.ChangeSettingForApplicationAsync(AppSettings.HostManagement.BillingAddress, input.Address);
    }

    private void EditAppSettingsjson(string key, string value)
    {
        appConfigurationWriter.Write(key, value);
    }
}