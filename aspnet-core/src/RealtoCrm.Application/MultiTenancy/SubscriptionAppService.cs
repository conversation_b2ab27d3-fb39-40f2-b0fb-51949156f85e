// using System.Threading.Tasks;
// using Abp.Events.Bus;
// using Abp.Runtime.Session;
// using RealtoCrm.MultiTenancy.Payments;
//
// namespace RealtoCrm.MultiTenancy;
//
// public class SubscriptionAppService : RealtoCrmAppServiceBase, ISubscriptionAppService
// {
//     public IEventBus EventBus { get; set; } = NullEventBus.Instance;
//
//     public async Task EnableRecurringPayments()
//     {
//         using (this.CurrentUnitOfWork.SetTenantId(null))
//         {
//             var tenant = await this.TenantManager.GetByIdAsync(this.AbpSession.GetTenantId());
//             if (tenant.SubscriptionPaymentType == SubscriptionPaymentType.RecurringManual)
//             {
//                 tenant.SubscriptionPaymentType = SubscriptionPaymentType.RecurringAutomatic;
//                     
//                 await this.EventBus.TriggerAsync(new RecurringPaymentsEnabledEventData
//                 {
//                     TenantId = this.AbpSession.GetTenantId()
//                 });
//             }
//         }
//     }
// }