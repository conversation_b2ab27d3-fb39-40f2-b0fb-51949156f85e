using Abp.Application.Features;
using Abp.Application.Services.Dto;
using Abp.Authorization.Users;
using Abp.Configuration;
using Abp.Configuration.Startup;
using Abp.Localization;
using Abp.Runtime.Session;
using Abp.Timing;
using Abp.UI;
using Abp.Zero.Configuration;
using RealtoCrm.Configuration;
using RealtoCrm.Debugging;
using RealtoCrm.Editions;
using RealtoCrm.Editions.Dto;
using RealtoCrm.Features;
using RealtoCrm.MultiTenancy.Dto;
using RealtoCrm.MultiTenancy.Payments.Dto;
using RealtoCrm.Notifications;
using RealtoCrm.Security.Recaptcha;
using RealtoCrm.Url;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RealtoCrm.MultiTenancy.Payments;

namespace RealtoCrm.MultiTenancy;

public class TenantRegistrationAppService(
    IMultiTenancyConfig multiTenancyConfig,
    IRecaptchaValidator recaptchaValidator,
    EditionManager editionManager,
    IAppNotifier appNotifier,
    ILocalizationContext localizationContext,
    TenantManager tenantManager,
    ISubscriptionPaymentRepository subscriptionPaymentRepository)
    : RealtoCrmAppServiceBase, ITenantRegistrationAppService
{
    public IAppUrlService AppUrlService { get; set; } = NullAppUrlService.Instance;

    private async Task<bool> IsNewRegisteredTenantActiveByDefault(SubscriptionStartType subscriptionStartType)
    {
        if (subscriptionStartType == SubscriptionStartType.Paid)
        {
            return false;
        }

        return await this.SettingManager.GetSettingValueForApplicationAsync<bool>(AppSettings.TenantManagement
            .IsNewRegisteredTenantActiveByDefault);
    }

    private async Task CheckRegistrationWithoutEdition()
    {
        var editions = await editionManager.GetAllAsync();
        if (editions.Any())
        {
            throw new Exception(
                "Tenant registration is not allowed without edition because there are editions defined !");
        }
    }

    public async Task<EditionsSelectOutput> GetEditionsForSelect()
    {
        var features = this.FeatureManager
            .GetAll()
            .Where(feature =>
                (feature[FeatureMetadata.CustomFeatureKey] as FeatureMetadata)?.IsVisibleOnPricingTable ?? false);

        var flatFeatures = this.ObjectMapper
            .Map<List<FlatFeatureSelectDto>>(features)
            .OrderBy(f => f.DisplayName)
            .ToList();

        var editions = (await editionManager.GetAllAsync())
            .Cast<SubscribableEdition>()
            .OrderBy(e => e.MonthlyPrice)
            .ToList();

        var featureDictionary = features.ToDictionary(feature => feature.Name, f => f);

        var editionWithFeatures = new List<EditionWithFeaturesDto>();
        foreach (var edition in editions)
        {
            editionWithFeatures.Add(await this.CreateEditionWithFeaturesDto(edition, featureDictionary));
        }

        if (this.AbpSession.UserId.HasValue)
        {
            var currentEditionId = (await tenantManager.GetByIdAsync(this.AbpSession.GetTenantId()))
                .EditionId;

            if (currentEditionId.HasValue)
            {
                editionWithFeatures = editionWithFeatures.Where(e => e.Edition.Id != currentEditionId).ToList();

                var currentEdition =
                    (SubscribableEdition) (await editionManager.GetByIdAsync(currentEditionId.Value));
                if (!currentEdition.IsFree)
                {
                    var lastPayment = await subscriptionPaymentRepository.GetLastCompletedPaymentOrDefaultAsync(this.AbpSession.GetTenantId(),
                        null,
                        null);

                    if (lastPayment != null)
                    {
                        editionWithFeatures = editionWithFeatures
                            .Where(e =>
                                e.Edition.GetPaymentAmount(lastPayment.PaymentPeriodType) >
                                currentEdition.GetPaymentAmount(lastPayment.PaymentPeriodType)
                            )
                            .ToList();
                    }
                }
            }
        }

        return new EditionsSelectOutput
        {
            AllFeatures = flatFeatures,
            EditionsWithFeatures = editionWithFeatures,
        };
    }

    public async Task<EditionSelectDto> GetEdition(int editionId)
    {
        var edition = await editionManager.GetByIdAsync(editionId);
        var editionDto = this.ObjectMapper.Map<EditionSelectDto>(edition);

        return editionDto;
    }

    private async Task<EditionWithFeaturesDto> CreateEditionWithFeaturesDto(SubscribableEdition edition,
        Dictionary<string, Feature> featureDictionary)
    {
        return new EditionWithFeaturesDto
        {
            Edition = this.ObjectMapper.Map<EditionSelectDto>(edition),
            FeatureValues = (await editionManager.GetFeatureValuesAsync(edition.Id))
                .Where(featureValue => featureDictionary.ContainsKey(featureValue.Name))
                .Select(fv => new NameValueDto(
                    fv.Name,
                    featureDictionary[fv.Name].GetValueText(fv.Value, localizationContext))
                )
                .ToList()
        };
    }

    private void CheckTenantRegistrationIsEnabled()
    {
        if (!this.IsSelfRegistrationEnabled())
        {
            throw new UserFriendlyException(this.L("SelfTenantRegistrationIsDisabledMessage_Detail"));
        }

        if (!multiTenancyConfig.IsEnabled)
        {
            throw new UserFriendlyException(this.L("MultiTenancyIsNotEnabled"));
        }
    }

    private bool IsSelfRegistrationEnabled()
    {
        return this.SettingManager.GetSettingValueForApplication<bool>(
            AppSettings.TenantManagement.AllowSelfRegistration);
    }

    private bool UseCaptchaOnRegistration()
    {
        return this.SettingManager.GetSettingValueForApplication<bool>(AppSettings.TenantManagement
            .UseCaptchaOnRegistration);
    }

    private async Task CheckEditionSubscriptionAsync(int editionId, SubscriptionStartType subscriptionStartType)
    {
        var edition = await editionManager.GetByIdAsync(editionId) as SubscribableEdition;

        CheckSubscriptionStart(edition, subscriptionStartType);
    }

    private static void CheckSubscriptionStart(SubscribableEdition edition,
        SubscriptionStartType subscriptionStartType)
    {
        switch (subscriptionStartType)
        {
            case SubscriptionStartType.Free:
                if (!edition.IsFree)
                {
                    throw new Exception("This is not a free edition !");
                }

                break;
            case SubscriptionStartType.Trial:
                if (!edition.HasTrial())
                {
                    throw new Exception("Trial is not available for this edition !");
                }

                break;
            case SubscriptionStartType.Paid:
                if (edition.IsFree)
                {
                    throw new Exception("This is a free edition and cannot be subscribed as paid !");
                }

                break;
        }
    }
}