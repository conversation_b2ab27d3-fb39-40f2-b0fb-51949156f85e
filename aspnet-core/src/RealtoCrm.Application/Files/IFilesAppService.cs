namespace RealtoCrm.Files;

using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

using Models;

public interface IFilesAppService : IDataCrudAppService<
    int,
    File,
    FileCreateRequestModel,
    FileUpdateRequestModel,
    FilePaginatedRequestModel,
    FileDetailsResponseModel,
    FileListingResponseModel>
{
    Task<int> UploadAndCreateForClientsAsync([FromForm] FileCreateRequestModel createRequest);
}