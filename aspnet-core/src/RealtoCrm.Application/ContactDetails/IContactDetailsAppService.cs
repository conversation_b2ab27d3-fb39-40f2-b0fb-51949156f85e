namespace RealtoCrm.ContactDetails;

using System.Threading.Tasks;
using Models;
using RealtoCrm.DataCrudModels;

public interface IContactDetailsAppService : IDataCrudAppService<
    int,
    ContactDetail,
    ContactDetailRequestModel,
    ContactDetailRequestModel,
    PaginatedRequestModel,
    ContactDetailResponseModel,
    ContactDetailResponseModel>
{
    Task<ContactDetailId> GetContactDetailId();
}