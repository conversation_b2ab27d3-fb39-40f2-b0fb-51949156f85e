namespace RealtoCrm.ContactDetails;

using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using RealtoCrm.DataCrudModels;
using Expressions;

public class ContactDetailsAppService(IDbContextProvider<RealtoCrmDbContext> dbContextProvider, IExpressionsBuilder expressionsBuilder) : DataCrudAppService<
    int,
    ContactDetail,
    ContactDetailRequestModel,
    ContactDetailRequestModel,
    PaginatedRequestModel,
    ContactDetailResponseModel,
    ContactDetailResponseModel>(dbContextProvider, expressionsBuilder), IContactDetailsAppService
{
    public Task<ContactDetailId> GetContactDetailId()
        => Task.FromResult(new ContactDetailId());
}