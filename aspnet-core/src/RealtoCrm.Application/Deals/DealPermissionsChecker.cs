namespace RealtoCrm.Deals;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Authorization;
using Employees.Models;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Models;

public class DealPermissionsChecker(IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : RealtoCrmAppServiceBase, IDealPermissionsChecker
{
    private RealtoCrmDbContext Data => dbContextProvider.GetDbContext();

    public async Task<bool> CanPerformActionAsync(
        int dealId,
        long userId,
        string[] permissions,
        bool checkForSameTenant = true)
    {
        var hasAdministrationFullAccessPermission = await this.PermissionChecker.IsGrantedAsync(
            AppPermissions.AdministrationFullAccess);

        if (hasAdministrationFullAccessPermission)
        {
            return true;
        }

        var deal = await this.GetDealEmployeeAsync(dealId);
        var employee = await this.GetEmployeeAsync(userId);

        var hasPermission = (await Task.WhenAll(permissions.Select(p => this.PermissionChecker.IsGrantedAsync(p))))
            .Any(result => result);

        var currentUserId = this.AbpSession.UserId;

        var areInSameTenant =
            deal?.EmployeeTenantId == employee?.TenantId ||
            deal?.OppositeSideEmployeeTenantId == employee?.TenantId;

        if (deal?.EmployeeUserId == currentUserId || deal?.OppositeSideEmployeeUserId == currentUserId)
        {
            return checkForSameTenant
                ? areInSameTenant && hasPermission
                : hasPermission;
        }

        var canPerformActionInDepartment = await this.CanPerformActionInDepartmentAsync(
            deal?.EmployeeDepartmentId,
            deal?.OppositeSideEmployeeDepartmentId,
            employee?.DepartmentId);

        var canPerformActionInDivision = await this.CanPerformActionInDivisionAsync(
            deal?.EmployeeDivisionId,
            deal?.OppositeSideEmployeeDivisionId,
            employee?.DivisionId);

        var canPerformActionInTeam = await this.CanPerformActionInTeamAsync(
            deal?.EmployeeTeamId,
            deal?.OppositeSideEmployeeTeamId,
            employee?.TeamId);

        var canPerformActionInCompany = await this.CanPerformActionInCompanyAsync(
            deal?.EmployeeCompanyId,
            deal?.OppositeSideEmployeeCompanyId,
            employee?.CompanyId);

        var canPerformAction =
            (canPerformActionInDepartment ||
             canPerformActionInDivision ||
             canPerformActionInTeam ||
             canPerformActionInCompany) &&
            hasPermission;

        return checkForSameTenant
            ? areInSameTenant && canPerformAction
            : canPerformAction;
    }

    private async Task<bool> CanPerformActionInTeamAsync(
        int? dealEmployeeTeamId,
        int? dealOppositeSideEmployeeTeamId,
        int? currentEmployeeTeamId)
        => currentEmployeeTeamId.HasValue &&
           (dealEmployeeTeamId == currentEmployeeTeamId ||
            dealOppositeSideEmployeeTeamId == currentEmployeeTeamId) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.DealTeamAction);

    private async Task<bool> CanPerformActionInDepartmentAsync(
        int? dealEmployeeDepartmentId,
        int? dealOppositeSideEmployeeDepartmentId,
        int? currentEmployeeDepartmentId)
        => currentEmployeeDepartmentId.HasValue &&
           (dealEmployeeDepartmentId == currentEmployeeDepartmentId ||
            dealOppositeSideEmployeeDepartmentId == currentEmployeeDepartmentId) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.DealDepartmentAction);

    private async Task<bool> CanPerformActionInDivisionAsync(
        int? dealEmployeeDivisionId,
        int? dealOppositeSideEmployeeDivisionId,
        int? currentEmployeeDivisionId)
        => currentEmployeeDivisionId.HasValue &&
           (dealEmployeeDivisionId == currentEmployeeDivisionId ||
            dealOppositeSideEmployeeDivisionId == currentEmployeeDivisionId) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.DealDivisionAction);

    private async Task<bool> CanPerformActionInCompanyAsync(
        int? dealEmployeeCompanyId,
        int? dealOppositeSideEmployeeCompanyId,
        int? currentEmployeeCompanyId)
        => currentEmployeeCompanyId.HasValue &&
           (dealEmployeeCompanyId == currentEmployeeCompanyId ||
            dealOppositeSideEmployeeCompanyId == currentEmployeeCompanyId) &&
           await this.PermissionChecker.IsGrantedAsync(AppPermissions.DealCompanyAction);

    private async Task<EmployeeTeamDetailsResponseModel?> GetEmployeeAsync(long userId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeTeamDetailsResponseModel>(this
                .Data
                .Employees
                .AsNoTracking()
                .Where(e => e.UserAccount.UserId == userId))
            .FirstOrDefaultAsync();

    private async Task<DealEmployeeTeamDetailsResponseModel?> GetDealEmployeeAsync(int dealId)
        => await this.ObjectMapper
            .ProjectTo<DealEmployeeTeamDetailsResponseModel>(this
                .Data
                .Deals
                .AsNoTracking()
                .Where(d => d.Id == dealId))
            .FirstOrDefaultAsync();
}