namespace RealtoCrm.Deals;

using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Abp;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Abp.UI;
using Authorization;
using Common.Attributes;
using Employees;
using Employees.Models;
using EntityFrameworkCore;
using Expressions;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Models;
using Nomenclatures.OfferStatuses;
using Nomenclatures.SearchStatuses;
using Notifications;
using Offers;
using Searches;
using Specifications;
using static CosherConsts.OfferStatuses;
using static CosherConsts.SearchStatuses;

public class DealsAppService(
    IOffersAppService offersService,
    ISearchesAppService searchesService,
    IEmployeesAppService employeesService,
    IAppNotifier appNotifier,
    IOfferStatusesAppService offerStatusesService,
    ISearchStatusesAppService searchStatusesService,
    IDealPermissionsChecker dealPermissions<PERSON><PERSON><PERSON>,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Deal,
        DealCreateRequestModel,
        DealUpdateRequestModel,
        DealsPaginatedRequestModel,
        DealDetailsResponseModel,
        DealListingResponseModel>(dbContextProvider, expressionsBuilder), IDealsAppService
{
    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.DealCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.DealUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.DealUpdate)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.DealRead);

    protected override Specification<Deal> GetSpecification(DealsPaginatedRequestModel request)
        => new DealByIdSpecification(request.Id)
            .And(new DealByStatusSpecification(request.StatusId))
            .And(new DealByDateSpecification(request.StartDate, request.EndDate))
            .And(new DealByOfferSpecification(request.OfferId))
            .And(new DealBySearchSpecification(request.SearchId))
            .And(new DealByConsultantSpecification(request.ConsultantId))
            .And(new DealByClientIdSpecification(request.ClientId))
            .And(new DealWithoutAnnulledSpecification(request.StatusId, request.WithoutAnnulled));

    public override async Task<int> CreateAsync(DealCreateRequestModel request)
    {
        var tenantId = this.GetTenantId();
        var lastDealIdByTenant = await this.GetLastIdByTenantIdAsync(tenantId) ?? 0;
        var employeeId = await employeesService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());
        var oppositeSideEmployeeIsOwnerOfOffer = false;

        request.DealStatus = DealStatus.AwaitsSecondSide;
        request.EmployeeId = employeeId;

        if (request.OfferId.HasValue)
        {
            var offerInfo = await offersService.GetInfoForDealAsync(request.OfferId.Value);

            request.VatId = offerInfo?.VatId;
            request.OfferClientId = offerInfo?.ClientId;
            request.OfferContractTypeId = offerInfo?.ContractTypeId;

            if (offerInfo?.EmployeeId != employeeId)
            {
                request.OppositeSideEmployeeId = offerInfo?.EmployeeId;
                oppositeSideEmployeeIsOwnerOfOffer = true;
            }
        }

        if (request.SearchId.HasValue)
        {
            var searchClientContractTypeId =
                await searchesService.GetClientAndContractTypeIdsAsync(request.SearchId.Value);

            request.SearchClientId = searchClientContractTypeId?.ClientId;
            request.SearchContractTypeId = searchClientContractTypeId?.ContractTypeId;

            if (searchClientContractTypeId?.EmployeeId != employeeId)
            {
                request.OppositeSideEmployeeId = searchClientContractTypeId?.EmployeeId;
                oppositeSideEmployeeIsOwnerOfOffer = false;
            }
        }

        request.OppositeSideEmployeeId ??= employeeId;

        request.DealParticipations.ForEach(participation =>
        {
            participation.TenantId = tenantId;

            participation.DealParticipants.ForEach((participant, index) =>
            {
                participant.IsLead = index == 0;
                participant.TenantId = tenantId;
            });
        });

        request.DealTenants.Add(new DealTenantRequestModel
        {
            TenantId = tenantId,
            DealTenantId = lastDealIdByTenant + 1,
        });

        request.DealHistories.Add(new DealHistoryRequestModel
        {
            State = JsonSerializer.Serialize(request)
        });

        var employeeWithUserId =
            await employeesService.GetEmployeeForNotificationById(request.OppositeSideEmployeeId.Value);

        await this.SendViewingCreatedNotificationAsync(
            request,
            employeeWithUserId,
            oppositeSideEmployeeIsOwnerOfOffer);

        // TODO: ExternalSearchId and ExternalOfferId
        // TODO: OppositeSideEmployeeId when we have ExternalSearchId and ExternalOfferId

        return await base.CreateAsync(request);
    }

    public override async Task<int> UpdateAsync(int id, DealUpdateRequestModel request)
    {
        var commentsToRemove = request.DealParticipations
            .SelectMany(dp => dp.DealParticipationsComments)
            .Where(dc => dc.CommentId.HasValue)
            .Select(dc => dc.CommentId)
            .ToList();

        await this
            .Data
            .DealParticipationsComments
            .Where(dc => commentsToRemove.Contains(dc.CommentId))
            .ExecuteDeleteAsync();

        await this
            .Data
            .Comments
            .Where(c => commentsToRemove.Contains(c.Id))
            .ExecuteDeleteAsync();

        request.DealHistories.Add(new DealHistoryRequestModel
        {
            State = JsonSerializer.Serialize(request)
        });

        return await base.UpdateAsync(id, request);
    }

    public async Task<int> ApproveAsync(int id)
    {
        var canPerformAction = await dealPermissionsChecker.CanPerformActionAsync(
            id,
            this.AbpSession.GetUserId(),
            [AppPermissions.DealApprovalAction]);

        if (!canPerformAction)
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var deal = await this.GetAsync(id);

        if (deal is null)
        {
            throw new EntityNotFoundException(typeof(Deal), id);
        }

        deal.DealStatus = DealStatus.AwaitsPayment;

        if (deal.Offer is not null)
        {
            deal.Offer.OfferStatusId = await offerStatusesService.GetIdByNameAsync(DealOfferStatusName);
        }

        if (deal.Search is not null)
        {
            deal.Search.SearchStatusId = await searchStatusesService.GetIdByNameAsync(DealSearchStatusName);
        }

        await this.Data.SaveChangesAsync();

        return deal.Id;
    }

    public async Task<int> CancelAsync(int id)
    {
        var canPerformAction = await dealPermissionsChecker.CanPerformActionAsync(
            id,
            this.AbpSession.GetUserId(),
            [AppPermissions.DealAnnulledAction]);

        if (!canPerformAction)
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var deal = await this.GetAsync(id);

        if (deal is null)
        {
            throw new EntityNotFoundException(typeof(Deal), id);
        }

        deal.DealStatus = DealStatus.IsAnnulled;

        if (deal.Offer is not null)
        {
            deal.Offer.OfferStatusId = await offerStatusesService.GetIdByNameAsync(ActiveOfferStatusName);
        }

        if (deal.Search is not null)
        {
            deal.Search.SearchStatusId = await searchStatusesService.GetIdByNameAsync(ActiveSearchStatusName);
        }

        await this.Data.SaveChangesAsync();

        return deal.Id;
    }

    public async Task<DealIdResponseModel?> GetBySearchAndOfferAsync(int searchId, int offerId)
        => await this.ObjectMapper
            .ProjectTo<DealIdResponseModel>(this
                .AllAsNoTracking()
                .Where(d => d.SearchId == searchId && d.OfferId == offerId))
            .FirstOrDefaultAsync();

    public async Task<ICollection<DealShortResponseModel>> GetDealsByClientIdAsync(int clientId)
        => await this.ObjectMapper
            .ProjectTo<DealShortResponseModel>(this
                .AllAsNoTracking()
                .Where(d => d.OfferClientId == clientId || d.SearchClientId == clientId))
            .ToListAsync();

    protected override async Task<Deal?> GetAsync(int id)
        => await this
            .All()
            .Where(this.BuildTenantSpecification())
            .Include(d => d.Offer)
            .Include(d => d.Search)
            .FirstOrDefaultAsync(e => e.Id == id);

    protected override IQueryable<Deal> BuildDetailsQuery(int id)
        => this
            .AllAsNoTracking()
            .Where(d => d.Id == id)
            .IgnoreQueryFilters();

    private async Task<int?> GetLastIdByTenantIdAsync(int tenantId)
        => await this
            .AllAsNoTracking()
            .SelectMany(d => d.DealTenants)
            .Where(dt => dt.TenantId == tenantId)
            .OrderByDescending(dt => dt.DealTenantId)
            .Select(dt => dt.DealTenantId)
            .FirstOrDefaultAsync();

    private async Task SendViewingCreatedNotificationAsync(
        DealCreateRequestModel request,
        EmployeeForNotificationResponseModel? employee,
        bool oppositeSideEmployeeIsOwnerOfOffer)
    {
        if (employee?.UserId is null)
        {
            return;
        }

        var notificationUser = new UserIdentifier(
            employee.TenantId,
            employee.UserId.Value);

        var message = oppositeSideEmployeeIsOwnerOfOffer
            ? this.L("NewDealForOfferNotificationMessage", request.OfferId)
            : this.L("NewDealForSearchNotificationMessage", request.SearchId);

        await appNotifier.SendImportantMessageAsync(
            notificationUser,
            message);
    }
}