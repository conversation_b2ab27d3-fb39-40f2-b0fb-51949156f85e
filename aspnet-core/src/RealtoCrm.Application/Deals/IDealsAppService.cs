namespace RealtoCrm.Deals;

using System.Collections.Generic;
using System.Threading.Tasks;
using Models;

public interface IDealsAppService : IDataCrudAppService<
    int,
    Deal,
    DealCreateRequestModel,
    DealUpdateRequestModel,
    DealsPaginatedRequestModel,
    DealDetailsResponseModel,
    DealListingResponseModel>
{
    Task<int> ApproveAsync(int id);

    Task<int> CancelAsync(int id);

    Task<DealIdResponseModel?> GetBySearchAndOfferAsync(int searchId, int offerId);

    Task<ICollection<DealShortResponseModel>> GetDealsByClientIdAsync(int clientId);
}