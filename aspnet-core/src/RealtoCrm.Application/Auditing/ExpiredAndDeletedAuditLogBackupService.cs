using System;
using System.Collections.Generic;
using System.IO;
using Abp.Auditing;
using RealtoCrm.Configuration;
using RealtoCrm.DataExporting.Excel.MiniExcel;
using RealtoCrm.Dto;
using RealtoCrm.Storage;
using MiniExcelLibs;

namespace RealtoCrm.Auditing;

public class ExpiredAndDeletedAuditLogBackupService : MiniExcelExcelExporterBase,
    IExpiredAndDeletedAuditLogBackupService
{
    private readonly bool isBackupEnabled;

    private readonly IAppConfigurationAccessor configurationAccessor;
    private readonly ITempFileCacheManager tempFileCacheManager;

    public ExpiredAndDeletedAuditLogBackupService(
        ITempFileCacheManager tempFileCacheManager,
        IAppConfigurationAccessor configurationAccessor
    )
        : base(tempFileCacheManager)
    {
        this.tempFileCacheManager = tempFileCacheManager;
        this.configurationAccessor = configurationAccessor;
        this.isBackupEnabled =
            this.configurationAccessor.Configuration["App:AuditLog:AutoDeleteExpiredLogs:ExcelBackup:IsEnabled"] ==
            true.ToString();
    }

    public bool CanBackup() => this.isBackupEnabled;

    public void Backup(List<AuditLog> auditLogs)
    {
        if (auditLogs.Count == 0)
        {
            return;
        }

        var items = new List<Dictionary<string, object>>();

        foreach (var auditLog in auditLogs)
        {
            items.Add(new Dictionary<string, object>()
            {
                { this.L("TenantId"), auditLog.TenantId},
                { this.L("UserId"), auditLog.UserId},
                { this.L("ServiceName"), auditLog.ServiceName},
                { this.L("MethodName"), auditLog.MethodName},
                { this.L("Parameters"), auditLog.Parameters},
                { this.L("ReturnValue"), auditLog.ReturnValue},
                { this.L("ExecutionTime"), auditLog.ExecutionTime},
                { this.L("ExecutionDuration"), auditLog.ExecutionDuration},
                { this.L("ClientIpAddress"), auditLog.ClientIpAddress},
                { this.L("ClientName"), auditLog.ClientName},
                { this.L("BrowserInfo"), auditLog.BrowserInfo},
                { this.L("Exception"), auditLog.Exception},
                { this.L("ExceptionMessage"), auditLog.ExceptionMessage},
                { this.L("ImpersonatorUserId"), auditLog.ImpersonatorUserId},
                { this.L("ImpersonatorTenantId"), auditLog.ImpersonatorTenantId},
                { this.L("CustomData"), auditLog.CustomData},
            });
        }

        this.CreateExcelPackage(
            "AuditLogBackup_" + DateTime.UtcNow.ToString("yyyy-MM-ddTHH.mm.ss.FFFZ") + ".xlsx",
            items
        );
    }

    protected override void Save(List<Dictionary<string, object>> items, FileDto file)
    {
        var backupFilePath =
            this.configurationAccessor.Configuration["App:AuditLog:AutoDeleteExpiredLogs:ExcelBackup:FilePath"];
        if (string.IsNullOrWhiteSpace(backupFilePath))
        {
            return;
        }

        if (!Directory.Exists(backupFilePath))
        {
            Directory.CreateDirectory(backupFilePath);
        }

        using (var stream = new MemoryStream())
        {
            stream.SaveAs(items);
            this.tempFileCacheManager.SetFile(file.FileToken, stream.ToArray());

            using (var excelFile = new FileStream(
                       Path.Combine(backupFilePath, file.FileName),
                       FileMode.Create,
                       FileAccess.Write)
                  )
            {
                var fileContent = this.tempFileCacheManager.GetFile(file.FileToken);
                excelFile.Write(fileContent);
            }
        }
    }
}