using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Abp.Auditing;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore.EFPlus;
using Abp.Logging;
using Abp.Threading;
using Abp.Threading.BackgroundWorkers;
using Abp.Threading.Timers;
using Abp.Timing;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Configuration;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm.Auditing;

public class ExpiredAuditLogDeleterWorker : PeriodicBackgroundWorkerBase, ISingletonDependency
{
    /// <summary>
    /// Set this const field to true if you want to enable ExpiredAuditLogDeleterWorker.
    /// Be careful, If you enable this, all expired logs will be permanently deleted.
    /// </summary>
    public bool IsEnabled { get; }

    private const int CheckPeriodAsMilliseconds = 1 * 1000 * 60 * 3; // 3min
    private const int MaxDeletionCount = 10000;

    private readonly TimeSpan logExpireTime = TimeSpan.FromDays(7);
    private readonly IRepository<AuditLog, long> auditLogRepository;
    private readonly IRepository<Tenant> tenantRepository;
    private readonly IExpiredAndDeletedAuditLogBackupService expiredAndDeletedAuditLogBackupService;

    public ExpiredAuditLogDeleterWorker(
        AbpTimer timer,
        IRepository<AuditLog, long> auditLogRepository,
        IRepository<Tenant> tenantRepository,
        IExpiredAndDeletedAuditLogBackupService expiredAndDeletedAuditLogBackupService,
        IAppConfigurationAccessor configurationAccessor
    )
        : base(timer)
    {
        this.auditLogRepository = auditLogRepository;
        this.tenantRepository = tenantRepository;
        this.expiredAndDeletedAuditLogBackupService = expiredAndDeletedAuditLogBackupService;

        this.LocalizationSourceName = RealtoCrmConsts.LocalizationSourceName;

        this.Timer.Period = CheckPeriodAsMilliseconds;
        this.Timer.RunOnStart = true;

        this.IsEnabled = configurationAccessor.Configuration["App:AuditLog:AutoDeleteExpiredLogs:IsEnabled"] ==
                         true.ToString();
    }

    protected override void DoWork()
    {
        if (!this.IsEnabled)
        {
            return;
        }

        var expireDate = Clock.Now - this.logExpireTime;

        List<int> tenantIds;
        using (var uow = this.UnitOfWorkManager.Begin())
        {
            tenantIds = this.tenantRepository.GetAll()
                .Where(t => !string.IsNullOrEmpty(t.ConnectionString))
                .Select(t => t.Id)
                .ToList();

            uow.Complete();
        }

        this.DeleteAuditLogsOnHostDatabase(expireDate);

        foreach (var tenantId in tenantIds)
        {
            this.DeleteAuditLogsOnTenantDatabase(tenantId, expireDate);
        }
    }

    protected virtual void DeleteAuditLogsOnHostDatabase(DateTime expireDate)
    {
        try
        {
            using (var uow = this.UnitOfWorkManager.Begin())
            {
                using (this.CurrentUnitOfWork.SetTenantId(null))
                {
                    using (this.CurrentUnitOfWork.DisableFilter(AbpDataFilters.MayHaveTenant))
                    {
                        this.DeleteAuditLogs(expireDate);
                        uow.Complete();
                    }
                }
            }
        }
        catch (Exception e)
        {
            this.Logger.Log(LogSeverity.Error, $"An error occured while deleting audit logs on host database", e);
        }
    }

    protected virtual void DeleteAuditLogsOnTenantDatabase(int tenantId, DateTime expireDate)
    {
        try
        {
            using (var uow = this.UnitOfWorkManager.Begin())
            {
                using (this.CurrentUnitOfWork.SetTenantId(tenantId))
                {
                    this.DeleteAuditLogs(expireDate);
                    uow.Complete();
                }
            }
        }
        catch (Exception e)
        {
            this.Logger.Log(LogSeverity.Error,
                $"An error occured while deleting audit log for tenant. TenantId: {tenantId}", e);
        }
    }

    private void DeleteAuditLogs(DateTime expireDate)
    {
        var expiredEntryCount = this.auditLogRepository.LongCount(l => l.ExecutionTime < expireDate);

        if (expiredEntryCount == 0)
        {
            return;
        }

        void BatchDelete(Expression<Func<AuditLog, bool>> expression)
        {
            if (this.expiredAndDeletedAuditLogBackupService.CanBackup())
            {
                var auditLogs = this.auditLogRepository.GetAll().AsNoTracking().Where(expression).ToList();
                this.expiredAndDeletedAuditLogBackupService.Backup(auditLogs);
            }

            //will not delete the logs from database if backup operation throws an exception
            AsyncHelper.RunSync(() => this.auditLogRepository.BatchDeleteAsync(expression));
        }

        if (expiredEntryCount > MaxDeletionCount)
        {
            var deleteStartId = this.auditLogRepository.GetAll().OrderBy(l => l.Id).Skip(MaxDeletionCount)
                .Select(x => x.Id).First();

            BatchDelete(l => l.Id < deleteStartId);
        }
        else
        {
            BatchDelete(l => l.ExecutionTime < expireDate);
        }
    }
}