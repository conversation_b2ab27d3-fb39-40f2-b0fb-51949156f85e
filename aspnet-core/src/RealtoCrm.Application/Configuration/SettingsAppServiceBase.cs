using System;
using System.Threading.Tasks;
using Abp.Net.Mail;
using Abp.UI;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.Configuration.Host.Dto;

namespace RealtoCrm.Configuration;

public abstract class SettingsAppServiceBase(
    IEmailSender emailSender,
    IAppConfigurationAccessor configurationAccessor) : RealtoCrmAppServiceBase
{
    #region Send Test Email

    public async Task SendTestEmail(SendTestEmailInput input)
    {
        try
        {
            await emailSender.SendAsync(
                input.EmailAddress, this.L("TestEmail_Subject"), this.L("TestEmail_Body")
            );
        }
        catch (Exception e)
        {
            throw new UserFriendlyException("An error was encountered while sending an email. " + e.Message, e);
        }
    }

    public ExternalLoginSettingsDto GetEnabledSocialLoginSettings()
    {
        var dto = new ExternalLoginSettingsDto();
        if (!bool.Parse(configurationAccessor.Configuration["Authentication:AllowSocialLoginSettingsPerTenant"]))
        {
            return dto;
        }

        if (this.IsSocialLoginEnabled("Facebook"))
        {
            dto.EnabledSocialLoginSettings.Add("Facebook");
        }

        if (this.IsSocialLoginEnabled("Google"))
        {
            dto.EnabledSocialLoginSettings.Add("Google");
        }

        if (this.IsSocialLoginEnabled("Twitter"))
        {
            dto.EnabledSocialLoginSettings.Add("Twitter");
        }

        if (this.IsSocialLoginEnabled("Microsoft"))
        {
            dto.EnabledSocialLoginSettings.Add("Microsoft");
        }

        if (this.IsSocialLoginEnabled("WsFederation"))
        {
            dto.EnabledSocialLoginSettings.Add("WsFederation");
        }

        if (this.IsSocialLoginEnabled("OpenId"))
        {
            dto.EnabledSocialLoginSettings.Add("OpenId");
        }

        return dto;
    }

    private bool IsSocialLoginEnabled(string name)
    {
        return configurationAccessor.Configuration.GetSection("Authentication:" + name).Exists() &&
               bool.Parse(configurationAccessor.Configuration["Authentication:" + name + ":IsEnabled"]);
    }

    #endregion
}