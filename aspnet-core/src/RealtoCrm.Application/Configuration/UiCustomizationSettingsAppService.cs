using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Authorization;
using Abp.Configuration;
using Abp.Dependency;
using Abp.Runtime.Session;
using RealtoCrm.Authorization;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.UiCustomization;

namespace RealtoCrm.Configuration;

[AbpAuthorize]
public class UiCustomizationSettingsAppService(
    SettingManager settingManager,
    IIocResolver iocResolver,
    IUiThemeCustomizerFactory uiThemeCustomizerFactory) : RealtoCrmAppServiceBase, IUiCustomizationSettingsAppService
{
    public async Task<List<ThemeSettingsDto>> GetUiManagementSettings()
    {
        var settings = new List<ThemeSettingsDto>();
        var themeCustomizers = iocResolver.ResolveAll<IUiCustomizer>();

        foreach (var themeUiCustomizer in themeCustomizers)
        {
            var themeSettings = await themeUiCustomizer.GetUiSettings();
            settings.Add(themeSettings.BaseSettings);
        }

        return settings;
    }

    public async Task ChangeThemeWithDefaultValues(string themeName)
    {
        var settings = (await this.GetUiManagementSettings()).FirstOrDefault(s => s.Theme == themeName);

        var hasUiCustomizationPagePermission = await this.PermissionChecker.IsGrantedAsync(AppPermissions.SetDefaultTheme);

        if (hasUiCustomizationPagePermission)
        {
            await this.UpdateDefaultUiManagementSettings(settings);
        }
        else
        {
            await this.UpdateUiManagementSettings(settings);
        }
    }

    public async Task UpdateUiManagementSettings(ThemeSettingsDto settings)
    {
        var themeCustomizer = uiThemeCustomizerFactory.GetUiCustomizer(settings.Theme);
        await themeCustomizer.UpdateUserUiManagementSettingsAsync(this.AbpSession.ToUserIdentifier(), settings);
    }

    public async Task UpdateDefaultUiManagementSettings(ThemeSettingsDto settings)
    {
        var themeCustomizer = uiThemeCustomizerFactory.GetUiCustomizer(settings.Theme);

        if (this.AbpSession.TenantId.HasValue)
        {
            await themeCustomizer.UpdateTenantUiManagementSettingsAsync(this.AbpSession.TenantId.Value, settings, this.AbpSession.ToUserIdentifier());
        }
        else
        {
            await themeCustomizer.UpdateApplicationUiManagementSettingsAsync(settings, this.AbpSession.ToUserIdentifier());
        }
    }

    public async Task UseSystemDefaultSettings()
    {
        if (this.AbpSession.TenantId.HasValue)
        {
            var theme = await settingManager.GetSettingValueForTenantAsync(AppSettings.UiManagement.Theme, this.AbpSession.TenantId.Value);
            var themeCustomizer = uiThemeCustomizerFactory.GetUiCustomizer(theme);
            var settings = await themeCustomizer.GetTenantUiCustomizationSettings(this.AbpSession.TenantId.Value);
            await themeCustomizer.UpdateUserUiManagementSettingsAsync(this.AbpSession.ToUserIdentifier(), settings);
        }
        else
        {
            var theme = await settingManager.GetSettingValueForApplicationAsync(AppSettings.UiManagement.Theme);
            var themeCustomizer = uiThemeCustomizerFactory.GetUiCustomizer(theme);
            var settings = await themeCustomizer.GetHostUiManagementSettings();
            await themeCustomizer.UpdateUserUiManagementSettingsAsync(this.AbpSession.ToUserIdentifier(), settings);
        }
    }

    public async Task ChangeDarkModeOfCurrentTheme(bool isDarkModeActive)
    {
        var user = this.AbpSession.ToUserIdentifier();
        var theme = await settingManager.GetSettingValueForUserAsync(AppSettings.UiManagement.Theme, user);

        var themeCustomizer = uiThemeCustomizerFactory.GetUiCustomizer(theme);
        await themeCustomizer.UpdateDarkModeSettingsAsync(user, isDarkModeActive);
    }
}