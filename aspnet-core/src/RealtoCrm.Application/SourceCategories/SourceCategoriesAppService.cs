namespace RealtoCrm.SourceCategories;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using Specifications;
using Expressions;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;

public class SourceCategoriesAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        SourceCategory,
        SourceCategoryRequestModel,
        SourceCategoryRequestModel,
        SourceCategoriesPaginatedRequestModel,
        SourceCategoryResponseModel,
        SourceCategoryResponseModel>(dbContextProvider, expressionsBuilder), ISourceCategoriesAppService
{
    protected override Specification<SourceCategory> GetSpecification(SourceCategoriesPaginatedRequestModel request)
        => new SourceCategoryByNameSpecification(request.Name)
            .And(new SourceCategoryByParentSpecification(request.ParentId));

    public async Task<int> GetIdByNameAsync(string name)
        => await this
            .AllAsNoTracking()
            .Where(new SourceCategoryByNameSpecification(name))
            .Select(sc => sc.Id)
            .FirstAsync();

    public async Task<IEnumerable<SourceCategoryResponseModel>> GetForFiltering()
        => await this.ObjectMapper.ProjectTo<SourceCategoryResponseModel>(
            this.AllAsNoTracking())
            .ToListAsync();
}