namespace RealtoCrm.SourceCategories;

using System.Threading.Tasks;
using Models;
using System.Collections.Generic;

public interface ISourceCategoriesAppService : IDataCrudAppService<
    int,
    SourceCategory,
    SourceCategoryRequestModel,
    SourceCategoryRequestModel,
    SourceCategoriesPaginatedRequestModel,
    SourceCategoryResponseModel,
    SourceCategoryResponseModel>
{
    Task<int> GetIdByNameAsync(string name);

    Task<IEnumerable<SourceCategoryResponseModel>> GetForFiltering();
}