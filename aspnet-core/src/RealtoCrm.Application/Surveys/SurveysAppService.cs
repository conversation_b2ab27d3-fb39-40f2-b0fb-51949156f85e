using System.Threading.Tasks;
using Newtonsoft.Json;
using RealtoCrm.Surveys.Models;

namespace RealtoCrm.Application.Surveys.Services;

using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Expressions;
using RealtoCrm.Application.Surveys.Models;
using RealtoCrm.Surveys;

public class SurveysAppService(IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : DataCrudAppService<
    int,
    Survey,
    SurveyCreateRequestModel,
    SurveyCreateRequestModel,
    SurveysPaginatedRequestModel,
    SurveyResponseModel,
    SurveyResponseModel>(dbContextProvider, expressionsBuilder), ISurveysAppService
{
    public override async Task<int> CreateAsync(SurveyCreateRequestModel request)
    {
       this.Logger.Info(JsonConvert.SerializeObject(request));
       return -1;
    }

}