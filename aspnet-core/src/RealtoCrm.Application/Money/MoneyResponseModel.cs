namespace RealtoCrm.Money;

using System;
using AutoMapper;
using Mapping;

public class MoneyResponseModel : IMapFrom<Money>, IMapExplicitly
{
    public decimal Amount { get; init; }

    public Currency Currency { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Money, MoneyResponseModel>()
            .ForMember(m => m.Amount, cfg => cfg
                .MapFrom(m => Math.Ceiling(m.Amount)));
}