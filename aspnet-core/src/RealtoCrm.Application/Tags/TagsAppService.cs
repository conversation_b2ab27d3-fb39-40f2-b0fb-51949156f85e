namespace RealtoCrm.Tags;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using DataCrudModels;
using EntityFrameworkCore;
using Expressions;
using Microsoft.EntityFrameworkCore;
using Models;

public class TagsAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Tag,
        TagsRequestModel,
        TagsRequestModel,
        PaginatedRequestModel,
        TagsDetailsResponseModel,
        TagsListingResponseModel>(dbContextProvider, expressionsBuilder), ITagsAppService
{
    public async Task<IEnumerable<TagResponseModel>> GetAllByNamesAndCategories(
        IEnumerable<string> names,
        IEnumerable<int> categoriesIds)
        => await this.ObjectMapper
            .ProjectTo<TagResponseModel>(this
                .AllAsNoTracking()
                .Where(t =>
                    names.Any(name => t.Name == name.ToLower()) &&
                    categoriesIds.Any(id => t.CategoryId == id)))
            .ToListAsync();
}