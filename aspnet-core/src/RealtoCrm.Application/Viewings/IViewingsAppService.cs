namespace RealtoCrm.Viewings;

using System.Collections.Generic;
using System.Threading.Tasks;
using Comments;
using DataCrudModels;
using Models;

public interface IViewingsAppService : ICommentsAppService<
    int,
    Viewing,
    ViewingComment,
    ViewingRequestModel,
    ViewingRequestModel,
    PaginatedRequestModel,
    ViewingResponseModel,
    ViewingResponseModel>
{
    Task<ICollection<ViewingResponseModel>> GetViewingsByClientIdAsync(int clientId);
}