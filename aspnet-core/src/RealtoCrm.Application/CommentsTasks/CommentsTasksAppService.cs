namespace RealtoCrm.CommentsTasks;

using System.Collections.Generic;
using System.Linq;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Abp.UI;
using Authorization;
using Comments;
using Common.Attributes;
using DataCrudModels;
using Employees;
using EntityFrameworkCore;
using Expressions;
using Mapping;
using Microsoft.EntityFrameworkCore;
using Tasks;
using Tasks.Models;
using Task = System.Threading.Tasks.Task;

public abstract class CommentsTasksAppService<
    TPrimaryKey,
    TEntity,
    TEntityComment,
    TEntityTask,
    TCreateRequestModel,
    TUpdateRequestModel,
    TPaginatedRequestModel,
    TDetailsResponseModel,
    TListingResponseModel>(
    IEmployeesAppService employeesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : CommentsAppService<
            TPrimaryKey,
            TEntity,
            TEntityComment,
            TCreateRequestModel,
            TUpdateRequestModel,
            TPaginatedRequestModel,
            TDetailsResponseModel,
            TListingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder),
        ITasksAppService<
            TPrimaryKey,
            TEntity,
            TEntityTask,
            TCreateRequestModel,
            TUpdateRequestModel,
            TPaginatedRequestModel,
            TDetailsResponseModel,
            TListingResponseModel>
    where TPrimaryKey : struct
    where TEntity : FullAuditedEntity<TPrimaryKey>, IAddComment, IAddTask
    where TEntityComment : class, IHaveComment
    where TEntityTask : class, IHaveTask
    where TCreateRequestModel : class, IMapTo<TEntity>
    where TUpdateRequestModel : class, IMapTo<TEntity>
    where TPaginatedRequestModel : PaginatedRequestModel, new()
    where TDetailsResponseModel : class, IMapFrom<TEntity>
    where TListingResponseModel : class, IMapFrom<TEntity>
{
    private const string NotTaskOwner = "You are not the owner of this task.";

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.AddTasksAsync), AppPermissions.TaskCreate)
            .AddCustomAttribute(nameof(this.AddTasksAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateTaskAsync), AppPermissions.TaskUpdate)
            .AddCustomAttribute(nameof(this.UpdateTaskAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteTaskAsync), AppPermissions.TaskUpdate)
            .AddCustomAttribute(nameof(this.DeleteTaskAsync), new RequireTenantAttribute());

    public virtual async Task AddTasksAsync(TPrimaryKey id, IEnumerable<TaskRequestModel> request)
    {
        var entity = await this.GetAsync(id);

        if (entity is null)
        {
            throw new EntityNotFoundException(typeof(TEntity), id);
        }

        var employeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        foreach (var taskRequest in request)
        {
            taskRequest.EmployeeId = employeeId;

            var task = this.ObjectMapper.Map<Tasks.Task>(taskRequest);

            entity.AddTask(task);
        }

        await this.Data.SaveChangesAsync();
    }

    public virtual async Task UpdateTaskAsync(int taskId, TaskRequestModel request)
    {
        var task = await this.Data
            .Tasks
            .Where(t => t.Id == taskId)
            .FirstOrDefaultAsync();

        if (task is null)
        {
            throw new EntityNotFoundException(typeof(Task), taskId);
        }

        var employeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        if (task.EmployeeId != employeeId)
        {
            throw new UserFriendlyException(NotTaskOwner);
        }

        this.ObjectMapper.Map(request, task);

        await this.Data.SaveChangesAsync();
    }

    public virtual async Task DeleteTaskAsync(int taskId)
    {
        var entityTask = await this.Data
            .Set<TEntityTask>()
            .Where(et => et.TaskId == taskId)
            .Include(et => et.Task)
            .FirstOrDefaultAsync();

        if (entityTask is null)
        {
            throw new EntityNotFoundException(typeof(TEntityTask), taskId);
        }

        var employeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        if (entityTask.Task.EmployeeId != employeeId)
        {
            throw new UserFriendlyException(NotTaskOwner);
        }

        this.Data.Tasks.Remove(entityTask.Task);
        this.Data.Set<TEntityTask>().Remove(entityTask);

        await this.Data.SaveChangesAsync();
    }
}