using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.DynamicEntityProperties;
using RealtoCrm.Authorization;
using RealtoCrm.DynamicEntityProperties.Dto;

namespace RealtoCrm.DynamicEntityProperties;

[AbpAuthorize]
public class DynamicPropertyValueAppService(
    IDynamicPropertyValueManager dynamicPropertyValueManager,
    IDynamicPropertyValueStore dynamicPropertyValueStore) : RealtoCrmAppServiceBase, IDynamicPropertyValueAppService
{
    public async Task<DynamicPropertyValueDto> Get(int id)
    {
        var entity = await dynamicPropertyValueManager.GetAsync(id);
        return this.ObjectMapper.Map<DynamicPropertyValueDto>(entity);
    }

    public async Task<ListResultDto<DynamicPropertyValueDto>> GetAllValuesOfDynamicProperty(EntityDto input)
    {
        var entities = await dynamicPropertyValueStore.GetAllValuesOfDynamicPropertyAsync(input.Id);
        return new ListResultDto<DynamicPropertyValueDto>(this.ObjectMapper.Map<List<DynamicPropertyValueDto>>(entities)
        );
    }

    public async Task Add(DynamicPropertyValueDto dto)
    {
        dto.TenantId = this.AbpSession.TenantId;
        await dynamicPropertyValueManager.AddAsync(this.ObjectMapper.Map<DynamicPropertyValue>(dto));
    }

    public async Task Update(DynamicPropertyValueDto dto)
    {
        dto.TenantId = this.AbpSession.TenantId;
        await dynamicPropertyValueManager.UpdateAsync(this.ObjectMapper.Map<DynamicPropertyValue>(dto));
    }

    public async Task Delete(int id)
    {
        await dynamicPropertyValueManager.DeleteAsync(id);
    }
}