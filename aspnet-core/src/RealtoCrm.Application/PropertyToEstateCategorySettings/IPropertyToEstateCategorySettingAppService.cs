namespace RealtoCrm.PropertyToEstateCategorySettings;

using System.Collections.Generic;
using System.Threading.Tasks;
using RealtoCrm.PropertyToEstateCategorySettings.Models;

public interface IPropertyToEstateCategorySettingAppService : IDataCrudAppService<
    int,
    PropertyToEstateCategorySetting,
    PropertyToEstateCategorySettingCreateRequestModel,
    PropertyToEstateCategorySettingUpdateRequestModel,
    PropertyToEstateCategorySettingsPaginatedRequestModel,
    PropertyToEstateCategorySettingResponseModel,
    PropertyToEstateCategorySettingResponseModel>
{
    Task<List<string>> GetPropertyNamesAsync();
    
    Task<ICollection<PropertyToEstateCategorySettingResponseModel>> GetPropertiesByEstateCategoryAndTenantIdAsync(int categoryId); 
}