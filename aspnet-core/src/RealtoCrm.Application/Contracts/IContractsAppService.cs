namespace RealtoCrm.Contracts;

using System.Collections.Generic;
using System.Threading.Tasks;
using DataCrudModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;

public interface IContractsAppService : IDataCrudAppService<
    int,
    Contract,
    ContractRequestModel,
    ContractRequestModel,
    PaginatedRequestModel,
    ContractResponseModel,
    ContractResponseModel>
{
    Task<ContractCreationTimeResponseModel?> GetCreationDateByClientAndSearchAsync(int clientId, int searchId);

    Task<bool> HasClientContractForOfferAsync(int clientId, int offerId);

    Task UploadContractsAsync(int id, [FromForm] IFormFileCollection files);

    Task<IEnumerable<int>> GetIdsWithoutSourceByProjectIdAsync(int projectId);
}