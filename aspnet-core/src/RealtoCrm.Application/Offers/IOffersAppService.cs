using RealtoCrm.Common.Models;
using RealtoCrm.Searches.Models;

namespace RealtoCrm.Offers;

using System.Collections.Generic;
using System.Threading.Tasks;
using Clients.Models;
using CommentsTasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;
using Models.Permissions;
using OffersImages.Models;
using Searches.Models.Permissions;
using Tasks.Models;

public interface IOffersAppService : ICommentsTasksAppService<
    int,
    Offer,
    OfferComment,
    OfferTask,
    OfferCreateRequestModel,
    OfferUpdateRequestModel,
    OffersPaginatedRequest,
    OfferDetailsResponseModel,
    OfferListingResponseModel>
{
    Task ImportExcelAsync(IFormFile? file);

    Task<IEnumerable<RelatedOfferResponseModel>> GetClientRelatedOffersAsync(int clientId);

    Task<OfferClientEmployeeResponseModel?> GetClientAndEmployeeIdsAsync(int id);

    Task<OfferDealInfoResponseModel?> GetInfoForDealAsync(int id);

    Task<List<SearchClientRegisteredOfferResponseModel>> GetClientRegisteredSellOffersAsync(
        int clientId,
        int? currentEmployeeId);

    Task<OfferEmployeePermissionsResponseModel> GetEmployeeOfferPermissionsAsync(int clientId);

    Task UploadImagesAsync(int id, [FromForm] IFormFileCollection images);

    Task<IEnumerable<OfferInListResponseModel?>> GetLastActiveOfferForClientsAsync(ICollection<int> clientIds);

    Task<OfferActivityResponseModel?> GetActivityAsync(int offerId);

    Task<ICollection<TaskActivityResponseModel>> GetTasksAsync(int offerId);

    Task OfferToEmployeeAsync(SuggestToEmployeeRequestModel request);

    Task TransferOfferToEmployeeAsync(TransferToEmployeeRequestModel request);

    Task DeleteImageAsync(OfferImageDeleteRequest request);

    Task ArchiveAsync(ArchiveRequestModel request);

    Task ActivateAsync(int offerId);

    Task ChangeOwnerAsync(ChangeOwnerRequestModel request);

    Task<IEnumerable<OffersForDealResponseModel>> GetAllForDealCreationAsync(OffersForDealRequestModel request);

    Task<IEnumerable<OfferEstateForProjectListingModel>> GetEstatesForProjectAsync(
        OfferEstateForProjectRequestModel request);

    Task UpdateContractType(int offerId);

    Task<IEnumerable<OfferMarkerPositionResponseModel>> GetOffersFromCoordinates(
        OffersPaginatedRequest? request);
    
    Task<OfferMapCardResponseModel?> GetOfferMapCardByIdAsync(int offerId);
    
    Task<IEnumerable<int>> EndExpiredImmunityAsync();

    Task<int> CreateOfferForClientAsync(OfferCreateRequestModel request);
    
    Task<OfferInListResponseModel>? GetOfferByClientStatusAndId(ClientsOfferSearchRequestModel request);
}