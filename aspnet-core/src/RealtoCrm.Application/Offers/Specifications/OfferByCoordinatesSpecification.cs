namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;
using Models;

public class OfferByCoordinatesSpecification(CoordinatesFilteringModel? coordinatesFilteringModel)
    : Specification<Offer>
{
    protected override bool Include => coordinatesFilteringModel is
        { LatitudeTo: not null, LatitudeFrom: not null, LongitudeTo: not null, LongitudeFrom: not null };

    public override Expression<Func<Offer, bool>> ToExpression()
     => offer => offer.Estate.Latitude >= coordinatesFilteringModel!.LatitudeFrom &&
                 offer.Estate.Latitude <= coordinatesFilteringModel.LatitudeTo &&
                 offer.Estate.Longitude >= coordinatesFilteringModel.LongitudeFrom &&
                 offer.Estate.Longitude <= coordinatesFilteringModel.LongitudeTo;
}