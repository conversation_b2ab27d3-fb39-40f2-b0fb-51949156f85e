namespace RealtoCrm.Offers.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using Addresses.Models;
using AutoMapper;
using Comments.Models;
using ContactDetails.Models;
using Images.Models;
using Mapping;
using Money;
using static CosherConsts.Clients;
using Nomenclatures;

public class OfferListingResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public int Id { get; init; }

    public DateTime CreationTime { get; init; }

    public MoneyResponseModel Price { get; init; } = default!;

    public MoneyResponseModel? OldPrice { get; init; }

    public Money? SquareMetrePrice { get; set; }

    public int OperationTypeId { get; init; }

    public string OperationTypeName { get; init; } = default!;

    public string EstateTypeName { get; init; } = default!;

    public AddressResponseModel EstateAddress { get; init; } = default!;

    public int ClientId { get; init; }
    public string ClientFirstName { get; init; } = default!;
    public string ClientLastName { get; init; } = default!;

    public int EmployeeId { get; init; }

    public string EmployeeFirstName { get; init; } = default!;

    public string EmployeeLastName { get; init; } = default!;

    public string EmployeePhoneNumber { get; init; } = default!;

    public int? EmployeeTeamId { get; init; }

    public int? EmployeeDepartmentId { get; init; }

    public int? EmployeeDivisionId { get; init; }
    public long? EmployeeUserId { get; init; }

    public int EstateId { get; init; }
    public double? EstateArea { get; init; }

    public double? EstateLatitude { get; init; }

    public double? EstateLongitude { get; init; }

    public int? EstateBedroomsCount { get; init; }

    public int? EstateBathroomsCount { get; init; }

    public int? EstateTerracesCount { get; init; }
    public int? OfferStatusId { get; init; }

    public string? OfferStatusName { get; init; }
    public OfferStatuses OfferStatusType { get; init; }
    public MoneyResponseModel? MaxOfferedPriceFromDeposits { get; set; }
    public string? EstateCategoryName { get; init; }

    public DateTime? LastModificationTime { get; init; }

    public bool? HasKey { get; init; }
    public string? ContractTypeName { get; set; }
    public ArchiveType? ArchiveType { get; set; }
    public IEnumerable<string> ClientPhoneNumbers { get; init; } = [];
    public IEnumerable<CommentResponseModel> Comments { get; init; } = [];
    public IEnumerable<ImageSmallResponseModel> OffersImages { get; init; } = [];
    public IEnumerable<ImageNormalResponseModel> NormalOfferImages { get; init; } = [];
    
    public string? ArchiveReasonName { get; init; }
    
    public DateTime? ArchiveDate { get; init; }
    
    public bool Immunity { get; set; }

    public DateTime? ImmunityEndDate { get; set; }
    public int? ViewingsCount { get; init; }
    public DateTime? LastViewingDate { get; init; }
    public DateTime? LastContact { get; init; }
    
    public decimal? PriceDifferenceAmmount { get; set; }
    public decimal? PriceDifferencePercent { get; set; }
    public int? ArchiveReasonId { get; init; }
    public bool IsArchived => this.ArchiveReasonId is not null;
    
    public int? OfferMappingAdminId { get; init; }

    public virtual void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Offer, OfferListingResponseModel>()
            .ForMember(dest => dest.ClientFirstName,
                opt
                    => opt.MapFrom(src =>
                        src.Client.TypeId == PersonalClientId
                            ? src.Client.PersonalData!.FirstName
                            : src.Client.LegalEntity!.Name))
            .ForMember(dest => dest.ClientLastName,
                opt
                    => opt.MapFrom(src =>
                        src.Client.TypeId == PersonalClientId ? src.Client.PersonalData!.LastName : string.Empty))
            .ForMember(dest => dest.EmployeeUserId, opt
                => opt.MapFrom(src => src.Employee != null
                    ? src.Employee.UserAccount.UserId
                    : (long?) null))
            .ForMember(dest => dest.EstateArea,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Area.HasValue ? (double?) Math.Round(src.Estate.Area.Value, 2) : null))
            .ForMember(dest => dest.ClientPhoneNumbers,
                opt
                    => opt.MapFrom(src =>
                        src.Client.ContactDetails.Where(x => x.ContactDetailId == (int) ContactDetailId.Phone)
                            .Select(x => x.Value)
                            .ToList()))
            .ForMember(slm => slm.EstateCategoryName, cfg => cfg
                .MapFrom(s => s.Estate.Type.Category.Name))
            .ForMember(slm => slm.OffersImages, cfg => cfg
                .MapFrom(s => s.OffersImages
                    .Where(oi => oi.IsActive)
                    .OrderBy(oi => oi.Order)))
            .ForMember(dest => dest.MaxOfferedPriceFromDeposits,
                opt
                    => opt.MapFrom(src => src.Deposits.Count > 0
                        ? src.Deposits.OrderByDescending(s => s.OfferedPrice.Amount).First().OfferedPrice
                        : null)
            )
            .ForMember(dest => dest.OfferStatusType, opt => opt
                .MapFrom(src => (OfferStatuses) src.OfferStatusId))
            .ForMember(slm => slm.Comments, cfg => cfg
                .MapFrom(src => src.OffersComments
                    .Where(sc => sc.Comment.IsPrivate == false)
                    .Select(sc => sc.Comment)
                    .OrderByDescending(sc => sc.CreationTime)))
            .ForMember(dest => dest.ArchiveType, opt => opt
                .MapFrom(src => src.ArchiveReason != null ? (ArchiveType?) src.ArchiveReason.ArchiveType : null))
            .ForMember(dest => dest.ContractTypeName, opt => opt
                .MapFrom(src => src.ContractType != null ? src.ContractType.Name : null))
            .ForMember(slm => slm.ViewingsCount, cfg => cfg
                .MapFrom(s => s.Viewings.Count))
            .ForMember(slm => slm.LastViewingDate, cfg => cfg
                .MapFrom(s =>
                    s.Viewings.OrderByDescending(v => v.CreationTime).Select(v => v.CreationTime).FirstOrDefault()))
            .ForMember(slm => slm.LastContact, cfg => cfg
                .MapFrom(s => s.Client.Calls
                    .Where(c => c.ConsultantId == s.EmployeeId)
                    .OrderByDescending(c => c.CreationTime)
                    .Select(c => (DateTime?) c.CreationTime)
                    .FirstOrDefault()))
            .ForMember(slm => slm.NormalOfferImages, cfg => cfg
                .MapFrom(s => s.OffersImages
                    .Where(oi => oi.IsActive)
                    .OrderBy(oi => oi.Order)))
            .ForMember(dest => dest.PriceDifferencePercent, cfg
                => cfg.MapFrom(dest => (dest.Price.Amount - dest.OldPrice.Amount) / dest.OldPrice.Amount * 100));
}