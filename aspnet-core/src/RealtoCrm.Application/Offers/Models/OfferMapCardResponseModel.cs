namespace RealtoCrm.Offers.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using Addresses.Models;
using AutoMapper;
using ContactDetails.Models;
using Images.Models;
using Mapping;
using Money;
using Nomenclatures;
using static CosherConsts.ClientTypes;

public class OfferMapCardResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public int Id { get; init; }

    public double? EstateLatitude { get; init; }

    public double? EstateLongitude { get; init; }

    public string? OfferStatusName { get; init; }

    public string OperationTypeName { get; init; } = default!;

    public string EstateTypeName { get; init; } = default!;

    public MoneyResponseModel Price { get; init; } = default!;

    public MoneyResponseModel? OldPrice { get; init; }

    public double? EstateArea { get; init; }

    public string? ContractTypeName { get; set; }

    public ArchiveType? ArchiveType { get; set; }

    public int? EstateBedroomsCount { get; init; }

    public int? EstateBathroomsCount { get; init; }

    public int? EstateTerracesCount { get; init; }

    public string ClientFirstName { get; init; } = default!;

    public string ClientLastName { get; init; } = default!;

    public int EmployeeId { get; init; }

    public string EmployeeFirstName { get; init; } = default!;

    public string EmployeeLastName { get; init; } = default!;

    public string EmployeePhoneNumber { get; init; } = default!;

    public IEnumerable<string> ClientPhoneNumbers { get; init; } = [];

    public AddressResponseModel EstateAddress { get; init; } = default!;

    public IEnumerable<ImageSmallResponseModel> OffersImages { get; init; } = [];

    public int ClientId { get; init; }
    
    public int? ArchiveReasonId { get; init; }
    
    public bool IsArchived => this.ArchiveReasonId is not null;
    
    public string? ArchiveReasonName { get; init; }
    
    public DateTime? ArchiveDate { get; init; }
    
    public OfferStatuses OfferStatusType { get; init; }
    
    public int? EmployeeTeamId { get; init; }

    public int? EmployeeDepartmentId { get; init; }

    public int? EmployeeDivisionId { get; init; }
    
    public long? EmployeeUserId { get; init; }
    
    public int? OfferMappingAdminId { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Offer, OfferMapCardResponseModel>()
            .ForMember(dest => dest.EstateArea,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Area.HasValue ? (double?)Math.Round(src.Estate.Area.Value, 2) : null))
            .ForMember(dest => dest.ClientPhoneNumbers,
                opt
                    => opt.MapFrom(src =>
                        src.Client.ContactDetails.Where(x => x.ContactDetailId == (int)ContactDetailId.Phone)
                            .Select(x => x.Value)
                            .ToList()))
            .ForMember(dest => dest.ArchiveType, opt => opt
                .MapFrom(src => src.ArchiveReason != null ? (ArchiveType?)src.ArchiveReason.ArchiveType : null))
            .ForMember(dest => dest.ContractTypeName, opt => opt
                .MapFrom(src => src.ContractType != null ? src.ContractType.Name : null))
            .ForMember(dest => dest.ClientFirstName,
                opt
                    => opt.MapFrom(src =>
                        src.Client.Type.Name == PersonalClientTypeName
                            ? src.Client.PersonalData!.FirstName
                            : src.Client.LegalEntity!.Name))
            .ForMember(dest => dest.ClientLastName,
                opt
                    => opt.MapFrom(src =>
                        src.Client.Type.Name == PersonalClientTypeName
                            ? src.Client.PersonalData!.LastName
                            : string.Empty))
            .ForMember(slm => slm.OffersImages, cfg => cfg
                .MapFrom(s => s.OffersImages
                    .Where(oi => oi.IsActive)
                    .OrderBy(oi => oi.Order)))
            .ForMember(dest => dest.OfferStatusType, opt => opt
                .MapFrom(src => (OfferStatuses) src.OfferStatusId))
            .ForMember(dest => dest.EmployeeUserId, opt
                => opt.MapFrom(src => src.Employee != null
                    ? src.Employee.UserAccount.UserId
                    : (long?) null));
}