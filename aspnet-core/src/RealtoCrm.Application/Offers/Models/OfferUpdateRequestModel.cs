namespace RealtoCrm.Offers.Models;

using System.Collections.Generic;
using Addresses.Models;
using AutoMapper;
using Estates.Models;
using FacingDirections;
using Garages;
using Heating;
using Mapping;
using Money;
using OffersImages.Models;

public class OfferUpdateRequestModel : IMapTo<Offer>, IMapExplicitly
{
    public string? YoutubeVideoId { get; set; }

    public string? VideoWalk { get; set; }

    public string? CadastralNumber { get; set; }

    public string? ZonedPropertyId { get; set; }

    public string? ZonedPropertyNumber { get; set; }

    public double? EstateArea { get; init; }

    public int? EstateBedroomsCount { get; init; }

    public int? EstateBathroomsCount { get; init; }

    public int? EstateRoomsCount { get; set; }

    public double? EstateLatitude { get; init; }
    
    public MoneyRequestModel? PriceSpa { get;init; }

    public double? EstateLongitude { get; init; }

    public double? EstateUsefulFloorArea { get; set; }

    public int? EstateTerracesCount { get; set; }

    public AddressRequestModel? Address { get; init; } = default!;

    public string? Name { get; init; }

    public string? EstateDescription { get; init; }

    public string? BuildingDescription { get; init; }

    public string? LocationDescription { get; init; }

    public string? AdvantagesDescription { get; init; }

    public string? DistributionDescription { get; init; }
    
    public string? DirectionDescription { get; init; }

    public string? ConvenientTimeToView { get; init; }

    public MoneyRequestModel? OldPrice { get; set; } = default!;

    public MoneyRequestModel? RecommendedPrice { get; init; } = default!;

    public MoneyRequestModel? SquareMetrePrice { get; init; } = default!;

    public MoneyRequestModel? ComparativeMarketAnalysisPrice { get; init; } = default!;

    public MoneyRequestModel? MaintenanceFee { get; init; } = default!;

    public bool PriceOnRequest { get; init; }

    public bool? HasKey { get; init; }

    public int? BuildingId { get; init; }

    public int? ProjectId { get; init; }

    public int? FurnitureId { get; init; }

    public int? VatId { get; init; }

    public int? ContractTypeId { get; init; }

    public int ClientId { get; init; }

    public int EstateId { get; init; }

    public int EstateCategoryId { get; init; }

    public int EstateTypeId { get; init; }
    
    public int EstateSubcategoryId { get; init; }

    public int OperationTypeId { get; init; }

    public MoneyRequestModel Price { get; init; } = default!;
    
    public int? EstateConditionId { get; init; }
    
    public int? EstateCompletionLevelId { get; set; }
    
    public int? EstateWindowJoineryId { get; set; }
    
    public int? EstateConstructionTypeId { get; set; }
    
    public int? EstateBuildingYear { get; set; }
    
    public int? EstateBuildingClassId { get; set; }
    
    public int? EstateFloors { get; set; }

    public int OfferStatusId { get; init; }

    public EstateDetailRequestModel? EstateDetails { get; set; }

    //TODO ceiling heights
    
    public OfferDetailRequestModel OfferDetail { get; init; } = default!;

    public ICollection<SourceDetailOfferRequestModel> SourceDetails { get; init; } =
        new List<SourceDetailOfferRequestModel>();

    public ICollection<EstateOfferMarketingTypeRequestModel> EstateOffersMarketingTypes { get; init; } =
        new List<EstateOfferMarketingTypeRequestModel>();
    
    public ICollection<OfferPriceChangeRequestModel> OfferPriceChanges { get; init; } =
        new List<OfferPriceChangeRequestModel>();
    
    public ICollection<GarageRequestModel> Garages { get; init; } = new List<GarageRequestModel>();
    
    public ICollection<HeatingRequestModel> EstateHeatingSystems { get; init; } = new List<HeatingRequestModel>();

    public ICollection<FacingDirectionsRequestModel> EstateFacingDirections { get; init; } =
        new List<FacingDirectionsRequestModel>();
    
    public ICollection<OfferImageRequestModel> OffersImages { get; init; } = new List<OfferImageRequestModel>();

    public ICollection<OfferLifeStyleRequestModel> OfferLifeStyles { get; init; } =
        new List<OfferLifeStyleRequestModel>();

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<OfferUpdateRequestModel, Offer>()
            .ForPath(m => m.Estate.CategoryId, cfg => cfg
                .MapFrom(m => m.EstateCategoryId))
            .ForPath(m => m.Estate.EstateDetail, cfg => cfg
                .MapFrom(m => m.EstateDetails))
            .ForPath(m => m.Estate.TypeId, cfg => cfg
                .MapFrom(m => m.EstateTypeId))
            .ForPath(m => m.Estate.CategoryId, cfg => cfg
                .MapFrom(m => m.EstateCategoryId))
            .ForPath(m => m.Estate.BathroomsCount, cfg => cfg
                .MapFrom(m => m.EstateBathroomsCount))
            .ForPath(m => m.Estate.BedroomsCount, cfg => cfg
                .MapFrom(m => m.EstateBedroomsCount))
            .ForPath(m => m.Estate.SubcategoryId, cfg => cfg
                .MapFrom(m => m.EstateSubcategoryId))
            .ForPath(m => m.Estate.TerracesCount, cfg => cfg
                .MapFrom(m => m.EstateTerracesCount))
            .ForPath(m => m.Estate.RoomsCount, cfg => cfg
                .MapFrom(m => m.EstateRoomsCount))
            .ForPath(m => m.Estate.Area, cfg => cfg
                .MapFrom(m => m.EstateArea))
            .ForPath(m => m.Estate.Longitude, cfg => cfg
                .MapFrom(m => m.EstateLongitude))
            .ForPath(m => m.Estate.Latitude, cfg => cfg
                .MapFrom(m => m.EstateLatitude))
            .ForPath(m => m.Estate.CadastralNumber, cfg => cfg
                .MapFrom(m => m.CadastralNumber))
            .ForPath(m => m.Estate.ZonedPropertyId, cfg => cfg
                .MapFrom(m => m.ZonedPropertyId))
            .ForPath(m => m.Estate.ZonedPropertyNumber, cfg => cfg
                .MapFrom(m => m.ZonedPropertyNumber))
            .ForPath(m => m.Estate.ConditionId, cfg => cfg
                .MapFrom(m => m.EstateConditionId))
            .ForPath(m => m.Estate.CompletionLevelId, cfg => cfg
                .MapFrom(m => m.EstateCompletionLevelId))
            .ForPath(m => m.Estate.WindowJoineryId, cfg => cfg
                .MapFrom(m => m.EstateWindowJoineryId))
            .ForPath(m => m.Estate.ConstructionTypeId, cfg => cfg
                .MapFrom(m => m.EstateConstructionTypeId))
            .ForPath(m => m.Estate.BuildingYear, cfg => cfg
                .MapFrom(m => m.EstateBuildingYear))
            .ForPath(m => m.Estate.BuildingClassId, cfg => cfg
                .MapFrom(m => m.EstateBuildingClassId))
            .ForPath(m => m.Estate.Floors, cfg => cfg
                .MapFrom(m => m.EstateFloors));
}