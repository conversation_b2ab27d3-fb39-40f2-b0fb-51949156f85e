namespace RealtoCrm.Offers.Models;

using System;
using System.Collections.Generic;
using RealtoCrm.ContactDetails.Models;
using System.Linq;
using Addresses.Models;
using AutoMapper;
using Estates.Models;
using Garages;
using Heating;
using Images.Models;
using Money;
using Nomenclatures.Common.Models;
using SourceCategories.Models;
using static CosherConsts.Clients;

public class OfferDetailsResponseModel : OfferListingResponseModel
{
    public string? YoutubeVideoId { get; set; }

    public string? VideoWalk { get; set; }

    public int? EstateFloors { get; set; }

    public string? CadastralNumber { get; set; }

    public string? ZonedPropertyId { get; set; }

    public string? ZonedPropertyNumber { get; set; }

    public int SubcategoryId { get; set; }

    public string? SubcategoryName { get; set; }

    public int? EstateRoomsCount { get; set; }

    public string? Name { get; init; }

    public double? EstateUsefulFloorArea { get; set; }

    public int? EstateLeaseTermId { get; set; }

    public string? EstateLeaseTermName { get; set; }

    public string? EstateDescription { get; init; }

    public string? DirectionDescription { get; set; }

    public string? EstateCategoryParent { get; init; }

    public int? EstateCategoryParentId { get; init; }

    public string? EstateCategoryParentParent { get; init; }

    public int? EstateCategoryId { get; init; }

    public int? EstateSubcategoryId { get; init; }

    public string? EstateSubcategoryName { get; init; }

    public int? EstateTypeId { get; init; }

    public string? BuildingDescription { get; init; }

    public string? LocationDescription { get; init; }

    public string? AdvantagesDescription { get; init; }

    public string? DistributionDescription { get; init; }

    public string? ConvenientTimeToView { get; init; }

    public MoneyResponseModel? RecommendedPrice { get; init; }

    public MoneyResponseModel? PriceSpa { get; init; }

    public MoneyResponseModel? ComparativeMarketAnalysisPrice { get; init; }

    public MoneyResponseModel? MaintenanceFee { get; init; }

    public bool PriceOnRequest { get; init; }

    public int? BuildingId { get; init; }

    public string? BuildingType { get; init; }

    public AddressResponseModel? BuildingAddress { get; init; }

    public int? ProjectId { get; init; }

    public string? ProjectName { get; init; }

    public int? FurnitureId { get; init; }

    public string? FurnitureName { get; init; }

    public int? VatId { get; init; }

    public string? VatName { get; init; }

    public int? ContractTypeId { get; init; }

    public string? ContractTypeName { get; init; }

    public string? ClientWorkplace { get; init; }

    public string? ClientPosition { get; init; }

    public string? ClientSource { get; init; }

    public string? EmployeeFullName { get; init; }

    public int TenantId { get; init; }

    public int? EstateHeatingId { get; init; }

    public string? EstateHeatingName { get; init; }

    public int? EstateConditionId { get; init; }

    public string? EstateConditionName { get; init; }

    public int? EstateCompletionLevelId { get; set; }

    public string? EstateCompletionLevelName { get; set; }

    public int? EstateFacingDirectionId { get; set; }

    public string? EstateFacingDirectionName { get; set; }

    public int? EstateWindowJoineryId { get; set; }

    public string? EstateWindowJoineryName { get; set; }

    public string? EstateConstructionTypeId { get; set; }

    public string? EstateConstructionTypeName { get; set; }

    public int? EstateBuildingYear { get; set; }

    public int? EstateBuildingClassId { get; set; }

    public string? EstateBuildingClassName { get; set; }

    public int DepositsCount { get; init; }

    public string? ArchiveReasonName { get; init; }

    public DateTime? ArchiveDate { get; init; }
    
    public int? OfferMappingAdminId { get; init; }

    public IEnumerable<string> ClientEmails { get; init; } = [];

    public IEnumerable<GarageResponseModel> Garages { get; init; } = [];

    public OfferDetailResponseModel? OfferDetail { get; init; }

    public EstateDetailResponseModel? EstateDetail { get; init; }

    public SourceDetailOfferResponseModel? SourceDetail { get; init; }

    public SourceCategoryResponseModel SourceCategory { get; init; } = default!;

    public IEnumerable<OfferPriceChangeResponseModel> OfferPriceChanges { get; init; } = [];

    public IEnumerable<HeatingResponseModel> EstateHeatingSystems { get; init; } = [];

    public IEnumerable<NomenclatureResponseModel> EstateFacingDirections { get; init; } = [];

    public new IEnumerable<ImageNormalResponseModel> OffersImages { get; init; } = [];

    public IEnumerable<OfferLifeStylesResponseModel> OfferLifestyles { get; init; } = [];

    public override void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Offer, OfferDetailsResponseModel>()
            .IncludeBase<Offer, OfferListingResponseModel>()
            .ForMember(m => m.DepositsCount, cfg => cfg
                .MapFrom(m => m.Deposits.Count))
            .ForMember(dest => dest.ClientFirstName,
                opt
                    => opt.MapFrom(src =>
                        src.Client.TypeId == PersonalClientId
                            ? src.Client.PersonalData!.FirstName
                            : src.Client.LegalEntity!.Name))
            .ForMember(dest => dest.CadastralNumber,
                opt
                    => opt.MapFrom(src => src.Estate.CadastralNumber))
            .ForMember(dest => dest.ZonedPropertyId,
                opt
                    => opt.MapFrom(src => src.Estate.ZonedPropertyId))
            .ForMember(dest => dest.ZonedPropertyNumber,
                opt
                    => opt.MapFrom(src => src.Estate.ZonedPropertyNumber))
            .ForMember(dest => dest.ClientLastName,
                opt
                    => opt.MapFrom(src =>
                        src.Client.TypeId == PersonalClientId ? src.Client.PersonalData!.LastName : string.Empty))
            .ForMember(dest => dest.ClientPhoneNumbers,
                opt
                    => opt.MapFrom(src =>
                        src.Client.ContactDetails.Where(x => x.ContactDetailId == (int)ContactDetailId.Phone)
                            .Select(x => x.Value)
                            .ToList()))
            .ForMember(dest => dest.ClientEmails,
                opt
                    => opt.MapFrom(src =>
                        src.Client.ContactDetails.Where(x => x.ContactDetailId == (int)ContactDetailId.Email)!
                            .Select(x => x.Value)
                            .ToList()))
            .ForMember(dest => dest.ClientWorkplace,
                opt
                    => opt.MapFrom(src => src.Client.ClientsWorkplaces
                        .Select(x => x.Workplace.Name)
                        .FirstOrDefault() ?? "")
            )
            .ForMember(dest => dest.ClientPosition,
                opt
                    => opt.MapFrom(src =>
                        src.Client.PersonalData != null && src.Client.PersonalData.JobPosition != null
                            ? src.Client.PersonalData.JobPosition.Name
                            : ""))
            .ForMember(dest => dest.ClientSource,
                opt
                    => opt.MapFrom(src => src.Client.ClientsSourceCategories
                        .Select(x => x.SourceCategory.Name)
                        .FirstOrDefault() ?? ""))
            .ForMember(dest => dest.BuildingAddress,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Address))
            .ForMember(dest => dest.EstateCategoryName,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Category != null ? src.Estate.Category.Name : null))
            .ForMember(dest => dest.EmployeeUserId, opt
                => opt.MapFrom(src => src.Employee != null
                    ? src.Employee.UserAccount.UserId
                    : (long?)null))
            .ForMember(slm => slm.EmployeeFullName, cfg => cfg
                .MapFrom(s => s.Employee != null
                    ? $"{s.Employee.FirstName} {s.Employee.LastName}".Trim()
                    : null))
            .ForMember(dest => dest.EstateCategoryParent,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Category != null && src.Estate.Category.Parent != null
                            ? src.Estate.Category.Parent.Name
                            : null))
            .ForMember(dest => dest.EstateCategoryParentParent,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Category != null && src.Estate.Category.Parent != null &&
                        src.Estate.Category.Parent.Parent != null
                            ? src.Estate.Category.Parent.Parent.Name
                            : null))
            .ForMember(dest => dest.OfferDetail, opt
                => opt.MapFrom(src => src.OfferDetail))
            .ForMember(dest => dest.EstateDetail, opt
                => opt.MapFrom(src => src.Estate.EstateDetail))
            .ForMember(m => m.OffersImages, cfg => cfg
                .MapFrom(m => m.OffersImages.OrderBy(oi => oi.Order)));
}