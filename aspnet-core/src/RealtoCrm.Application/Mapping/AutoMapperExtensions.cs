namespace RealtoCrm.Mapping;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using AutoMapper;
using Extensions;

public static class AutoMapperExtensions
{
    private static readonly Type MapFromType = typeof(IMapFrom<>);
    private static readonly Type MapToType = typeof(IMapTo<>);
    private static readonly Type ExplicitMapType = typeof(IMapExplicitly);

    public static void RegisterMappingsFrom(
        this IMapperConfigurationExpression mapper,
        params Assembly[] assemblies)
        => assemblies
            .SelectMany(a => a.GetExportedTypes())
            .Where(t => t
                .GetInterfaces()
                .Any(i => i.IsGenericType &&
                          (i.GetGenericTypeDefinition() == MapFromType ||
                           i.GetGenericTypeDefinition() == MapToType ||
                           ExplicitMapType.IsAssignableFrom(i))))
            .Select(t => new
            {
                Type = t,
                AllMapFrom = GetMappingModels(t, MapFromType),
                AllMapTo = GetMappingModels(t, MapToType),
                ExplicitMap = t
                    .GetInterfaces()
                    .Where(i => ExplicitMapType.IsAssignableFrom(i))
                    .Select(_ => (IMapExplicitly)Activator.CreateInstance(t)!)
                    .FirstOrDefault()
            })
            .ForEach(t =>
            {
                t.AllMapFrom.ForEach(mapFrom => mapper.CreateMap(mapFrom, t.Type));

                t.AllMapTo.ForEach(mapTo => mapper.CreateMap(t.Type, mapTo));

                t.ExplicitMap?.RegisterMappings(mapper);
            });

    private static IEnumerable<Type> GetMappingModels(Type source, Type mappingType)
        => source
            .GetInterfaces()
            .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == mappingType)
            .Select(i => i.GetGenericArguments().First());
}